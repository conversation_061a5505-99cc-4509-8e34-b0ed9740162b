﻿using Newtonsoft.Json;
using Payroll.Shared;
using Serilog;
using System.Text;
using System;

namespace Radar.Tool;

public class ConvertPunchesCommand
{
    private Dictionary<string, string> ParseArgs(List<string> args)
    {
        var rtn = new Dictionary<string, string>();
        foreach (var arg in args)
        {
            if (arg.Contains(":"))
            {
                var pair = arg.Split(':');
                rtn.Add(pair[0], pair[1]);
            }
        }
        return rtn;
    }

    public void Punches(List<string> args)
    {
        var p = ParseArgs(args);
        var punches = new List<PunchPair>();
        
        //read json from STD
        if(!Console.IsInputRedirected)
        {
            Log.Logger.Error("Input has not been redirected!");
            
        }

        Log.Logger.Debug("...reading standard input...");
        Log.Logger.Debug("");
        var json = Console.In.ReadToEnd();
        Log.Logger.Debug(string.Concat(json.AsSpan(0, Math.Min(100, json.Length)), "..."));

        try
        {
            punches = JsonConvert.DeserializeObject<List<PunchPair>>(json);
            
            foreach (var punch in punches)
            {
                //convert
                var fields = new List<string>
                {
                    punch.EECode,
                    string.Empty,
                    punch.JobCode,
                    MapJob(punch.JobCode),
                    punch.Date.ToString("MM/dd/yyyy"),
                    punch.TimeIn.ToString("MM/dd/yyyy hh:mm:sstt"),
                    punch.TimeOut.ToString("MM/dd/yyyy hh:mm:sstt"),
                    punch.Rate.ToString("0.00"),
                    punch.TotalPay.ToString("0.00"),
                    punch.LastName,
                    punch.FirstName,
                    MapLocation(punch.Location)
                };


                //write to STDOUT
                Console.WriteLine(string.Join(",", fields));
            }
        }
        catch (Exception ex)
        {
            Log.Logger.Error(ex, "Error while deserializing punch json");
            return;
        }
    }
    
    private string MapJob(string job)
    {
        var key = job.ToLower();
        var map = Config.RadarJobsMap;
        if (map.ContainsKey(key))
        {
            return map[key];
        }
        else
        {
            Log.Logger.Warning($"Job Map missing for job: '{job}'");
            return string.Empty;
        }
    }

    private string MapLocation(string _7shiftsLocation)
    {
        var map = Config.RadarTo7shiftsLocationMap;
        if (map.ContainsKey(_7shiftsLocation))
        {
            return map[_7shiftsLocation];
        }
        else
        {
            Log.Logger.Warning($"Job Map missing for 7shifts location: '{_7shiftsLocation}'");
            return string.Empty;
        }
    }
}
