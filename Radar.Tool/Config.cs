﻿using Payroll.Shared;

namespace Radar.Tool;

public static class Config
{
    public static readonly string JobSection = "radar_job_codes";
    public static readonly string LocationMapSection = "radar_7shifts_location_map";

    static Config()
    {
        Setting.Init();
    }

    public static Dictionary<string, string> RadarJobsMap
    {
        get
        {
            var iniDictionary = Setting.ListSection(JobSection);
            var output = new Dictionary<string, string>();
            foreach (var kvp in iniDictionary)
            {
                output.Add(kvp.Key.ToLower(), kvp.Value);
            }
            return output;
        }
    }

    public static Dictionary<string, string> RadarTo7shiftsLocationMap => Setting.ListSection(LocationMapSection);
}
