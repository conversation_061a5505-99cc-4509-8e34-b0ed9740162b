using CommandLine;
using Payroll.Shared;
using Serilog;

namespace Radar.Tool;

public class Program : ProgramBase<SettingCommand>
{
    static readonly string AppVersion = "0.1";

    public override int ShowUsage()
    {
        Console.WriteLine("Usage: Radar.Tool.exe <command> <command-args>");
        Console.WriteLine("  where <command> is one of...");
        Console.WriteLine("   - convert [punches]");
        return 0;
    }

    public void Help(List<string> args)
    {
        ShowUsage();
    }

    public void Info(List<string> args)
    {
        try
        {
            Payroll.Shared.Setting.Init();
            Console.WriteLine($"Radar.Tool.exe version {AppVersion} ({Payroll.Shared.Setting.IniFilePath})");
            Console.WriteLine();
            WriteSettingsSection(Config.JobSection);
            WriteSettingsSection(Config.LocationMapSection);
        }
        catch (Exception ex)
        {
            Console.WriteLine(ex.ToString());
        }
    }

    private void WriteSettingsSection(string section)
    {
        var list = Payroll.Shared.Setting.ListSection(section);
        Console.WriteLine();
        Console.WriteLine($"[{section}]");
        foreach (var e in list)
        {
            Console.WriteLine($"{e.Key}={e.Value}");
        }
    }

    private int ExecCommand<T>(List<string> args) where T : new()
    {
        var command = new CommandArguments(args);

        return Command<T>.Invoke(command);
    }

    public void Convert(List<string> args)
    {
        ExecCommand<ConvertPunchesCommand>(args);
    }

    static int Main(string[] args)
    {
        // setup logging services...
        var command = string.Join(" ", args);
        Logger.Setup($"Radar.Tool.Exe - Command: '{command}', Version: {AppVersion}");

        // Log version information
        Log.Logger.Information($"Radar.Tool.exe version {AppVersion} ({Payroll.Shared.Setting.IniFilePath})");
        Log.Logger.Information($"  Branch: {BuildInfo.Branch}, Built: {BuildInfo.Built}, Host: {BuildInfo.Host}, Hash: {BuildInfo.Commit}");

        try
        {
            Parser.Default.ParseArguments<ProgramArguments>(args)
                .MapResult((ProgramArguments opts) => ProgramDriver<Program>.Run(opts), errs => 1);
        }
        catch (Exception e)
        {
            Log.Logger.Error(e.Message);
            Log.Logger.Error(e.StackTrace ?? "");
            return -1;
        }

        return 0;
    }
}