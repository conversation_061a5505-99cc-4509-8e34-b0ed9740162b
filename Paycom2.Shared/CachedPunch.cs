using System;
using LiteDB;

namespace Paycom2.Shared
{
    public class CachedPunch
    {
        [BsonId]
        public string Id { get; set; }
        public string EECode { get; set; }
        public string PunchTime { get; set; }
        public string PunchType { get; set; }
        public DateTime ImportTimestamp { get; set; }

        public static string GenerateId(string eeCode, string punchTime, string punchType)
        {
            // Ensure consistent key generation even with nulls
            return $"{eeCode ?? "NULL_EECODE"}_{punchTime}_{punchType ?? "NULL_PUNCHTYPE"}";
        }
    }
}
