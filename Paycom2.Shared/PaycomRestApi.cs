using System;
using System.Collections.Generic;
using System.Text.Json;
using System.Text.Json.Serialization;

namespace Paycom2.Shared
{
    public partial class AuthResponse
    {
        [JsonPropertyName("@class")]
        public required string Class { get; set; }

        [JsonPropertyName("token")]
        public required AuthToken AuthToken { get; set; }

        [JsonPropertyName("status")]
        public required string Status { get; set; }
    }

    public partial class AuthToken
    {
        [JsonPropertyName("tokenType")]
        public required string TokenType { get; set; }

        [JsonPropertyName("scope")]
        public required string Scope { get; set; }

        [JsonPropertyName("expiresIn")]
        public required long ExpiresIn { get; set; }

        [JsonPropertyName("accessToken")]
        public required string AccessToken { get; set; }

        [JsonPropertyName("idToken")]
        public required object IdToken { get; set; }

        [JsonPropertyName("refreshToken")]
        public required object RefreshToken { get; set; }
    }

    public class ApiResponse<T>
    {
        public ApiResponse()
        {
            Errors = new List<string>();
            Data = new List<T>();
        }

        [JsonPropertyName("result")]
        public bool Result { get; set; }
        [JsonPropertyName("data")]
        public required List<T> Data { get; set; }
        [JsonPropertyName("errors")]
        public required List<string> Errors { get; set; }
        [JsonPropertyName("errorCount")]
        public int ErrorCount { get; set; }
        [JsonPropertyName("records")]
        public int Records { get; set; }
    }

    public class ApiPunchResponse
    {
        public ApiPunchResponse()
        {
            Errors = new List<PunchError>();
        }

        [JsonPropertyName("result")]
        public bool Result { get; set; }
        [JsonPropertyName("data")]
        public required string Data { get; set; }
        [JsonPropertyName("errors")]
        public required List<PunchError> Errors { get; set; }
        [JsonPropertyName("errorCount")]
        public int ErrorCount { get; set; }
        [JsonPropertyName("records")]
        public int Records { get; set; }
    }

    public class PunchError
    {
        [JsonPropertyName("punch")]
        public Punch Punch { get; set; }

        [JsonPropertyName("errors")]
        public List<string> Errors { get; set; }
    }

    public class Location
    {
        // all lowercase id field, do not change!
        public int locationid { get; set; }
        public string? description { get; set; }
        public string? facilityId { get; set; }
        public string? address { get; set; }
        public string? city { get; set; }
        public string? state { get; set; }
        public string? zipCode { get; set; }
        public string? country { get; set; }
        public string? EEOC_Unit_Number { get; set; }
        public bool hiringSite { get; set; }
        public bool? VETS_4212_Hiring_Location { get; set; }
        public bool locationHidden { get; set; }
    }

    public class EmployeeIdentifier
    {
        public EmployeeIdentifier()
        {
            EECode = string.Empty;
            EEBadge = string.Empty;
            ClockSeq = string.Empty;
            FirstName = string.Empty;
            MiddleName = string.Empty;
            LastName = string.Empty;
        }

        [JsonPropertyName("eecode")]
        public string EECode { get; set; }

        [JsonPropertyName("eebadge")]
        public string EEBadge { get; set; }

        [JsonPropertyName("clockseq")]
        public string ClockSeq { get; set; }

        [JsonPropertyName("firstname")]
        public string FirstName { get; set; }

        [JsonPropertyName("middlename")]
        public string MiddleName { get; set; }

        [JsonPropertyName("lastname")]
        public string LastName { get; set; }
    }

    public class DirectoryEntry
    {
        public DirectoryEntry()
        {
            eecode = string.Empty;
            eename = string.Empty;
            firstname = string.Empty;
            lastname = string.Empty;
            clockseq = string.Empty;
            cityaddr = string.Empty;
        }

        public string eecode { get; set; }
        public string eename { get; set; }
        public string firstname { get; set; }
        public string lastname { get; set; }
        public string? gender { get; set; }
        public string? streetaddr { get; set; }
        public object apt_suite_other { get; set; }
        public string cityaddr { get; set; }
        public string clockseq { get; set; }
        public string? eebadge { get; set; }
        public string? zipcode { get; set; }
        public string? homestate { get; set; }
        public string? homephone { get; set; }
        public string? eestatus { get; set; }
        public string? deptcode { get; set; }
        public string? deptdesc { get; set; }
        public string? cat1 { get; set; }
        public string? cat1desc { get; set; }
        public string? cat10 { get; set; }

        public string? cat10desc { get; set; }

        public string? cat11 { get; set; }

        public string? cat11desc { get; set; }

        public string? cat12 { get; set; }

        public string? cat12desc { get; set; }

        public string? cat13 { get; set; }

        public string? cat13desc { get; set; }

        public string? cat14 { get; set; }

        public string? cat14desc { get; set; }

        public string? cat15 { get; set; }

        public string? cat15desc { get; set; }

        public string? cat16 { get; set; }

        public string? cat16desc { get; set; }

        public string? cat17 { get; set; }

        public string? cat17desc { get; set; }

        public string? cat18 { get; set; }

        public string? cat18desc { get; set; }

        public string? cat19 { get; set; }

        public string? cat19desc { get; set; }

        public string? cat2 { get; set; }

        public string? cat20 { get; set; }

        public string? cat20desc { get; set; }

        public string? cat2desc { get; set; }

        public string? cat3 { get; set; }

        public string? cat3desc { get; set; }

        public string? cat4 { get; set; }

        public string? cat4desc { get; set; }

        public string? cat5 { get; set; }

        public string? cat5desc { get; set; }

        public string? cat6 { get; set; }

        public string? cat6desc { get; set; }

        public string? cat7 { get; set; }

        public string? cat7desc { get; set; }

        public string? cat8 { get; set; }

        public string? cat8desc { get; set; }

        public string? cat9 { get; set; }

        public string? cat9desc { get; set; }

    }

    public class LaborAllocationDetail
    {
        public int tippedjob { get; set; }
        public string? detailcode { get; set; }
        public string? detaildesc { get; set; }
        public string? glcode { get; set; }
        public string? glcodedef { get; set; }
    }

    public class MasterEmployeeRecord
    {
        public MasterEmployeeRecord()
        {
            firstname = string.Empty;
            lastname = string.Empty;
            clocksequencenumber = string.Empty;
        }

        public readonly static string ActiveStatus = "A";
        public readonly static string InactiveStatus = "I";
        public readonly static string RetiredStatus = "R";
        public readonly static string PendingStatus = "P";
        public readonly static string OnLeaveStatus = "V";
        public readonly static string DeceasedStatus = "D";
        public readonly static string TerminatedStatus = "T";
        public readonly static string NotHiredStatus = "N";

        public string? cat1 { get; set; }
        public string? cat1desc { get; set; }
        public string? cat10 { get; set; }

        public string? cat10desc { get; set; }

        public string? cat11 { get; set; }

        public string? cat11desc { get; set; }

        public string? cat12 { get; set; }

        public string? cat12desc { get; set; }

        public string? cat13 { get; set; }

        public string? cat13desc { get; set; }

        public string? cat14 { get; set; }

        public string? cat14desc { get; set; }

        public string? cat15 { get; set; }

        public string? cat15desc { get; set; }

        public string? cat16 { get; set; }

        public string? cat16desc { get; set; }

        public string? cat17 { get; set; }

        public string? cat17desc { get; set; }

        public string? cat18 { get; set; }

        public string? cat18desc { get; set; }

        public string? cat19 { get; set; }

        public string? cat19desc { get; set; }

        public string? cat2 { get; set; }

        public string? cat20 { get; set; }

        public string? cat20desc { get; set; }

        public string? cat2desc { get; set; }

        public string? cat3 { get; set; }

        public string? cat3desc { get; set; }

        public string? cat4 { get; set; }

        public string? cat4desc { get; set; }

        public string? cat5 { get; set; }

        public string? cat5desc { get; set; }

        public string? cat6 { get; set; }

        public string? cat6desc { get; set; }

        public string? cat7 { get; set; }

        public string? cat7desc { get; set; }

        public string? cat8 { get; set; }

        public string? cat8desc { get; set; }

        public string? cat9 { get; set; }

        public string? cat9desc { get; set; }

        public string? ss_number { get; set; }
        public string? actual_marital_status { get; set; }
        public string? actual_marital_status_description { get; set; }
        public int? age { get; set; }
        public string? alternate_pay_frequency { get; set; }
        public object? apt_suite_other { get; set; }
        public DateTime? birth_date { get; set; }
        public string? business_title { get; set; }
        public string? city { get; set; }
        public string clocksequencenumber { get; set; }
        public bool? comission_only { get; set; }
        public bool? current_key_employee { get; set; }
        public string? custom_standard_hours { get; set; }
        public string? department_code { get; set; }
        public string? department_description { get; set; }
        public string? eeoc_class { get; set; }
        public string? eeoc_class_description { get; set; }
        public string? ee_message { get; set; }
        public string? emergency_1_contact { get; set; }
        public string? emergency_1_phone { get; set; }
        public string? emergency_1_relationship { get; set; }
        public string emergency_2_contact { get; set; }
        public string? emergency_2_phone { get; set; }
        public string? emergency_2_relationship { get; set; }
        public string? emergency_3_contact { get; set; }
        public string? emergency_3_phone { get; set; }
        public string? emergency_3_relationship { get; set; }
        public DateTime employee_added { get; set; }
        public string? employee_badge { get; set; }
        public string? employee_code { get; set; }
        public string? employee_gl_code { get; set; }
        public string? employee_name { get; set; }
        public string? employee_status { get; set; }
        public string? employee_supervisor_level { get; set; }
        public string? employee_supervisor_pin { get; set; }
        public string? employee_terminal_group { get; set; }
        public string? employee_terminal_group_description { get; set; }
        public bool employee_type_1099 { get; set; }
        public string? ethnic_background { get; set; }
        public string firstname { get; set; }
        public int? fulltime_or_parttime { get; set; }
        public string? gender { get; set; }
        public bool has_direct_deposit { get; set; }
        public int highly_comp_employee { get; set; }
        public DateTime hire_date { get; set; }
        public bool hourly_or_salary { get; set; }
        public string? hours_401k { get; set; }
        public string? labor_allocation_details { get; set; }
        public string lastname { get; set; }
        public DateTime? last_pay_change { get; set; }
        public string? manager_level { get; set; }
        public string? middlename { get; set; }
        public bool new_hire { get; set; }
        public DateTime? new_hire_report_date { get; set; }
        public DateTime? next_review { get; set; }
        public string? nickname { get; set; }
        public bool non_resident_alien { get; set; }
        public DateTime? parttime_to_fulltime_date { get; set; }
        public string? part_num_401k { get; set; }
        public bool? participation_401k { get; set; }
        public string? pay_class { get; set; }
        public string? pay_frequency { get; set; }
        public string? personal_email { get; set; }
        public string? position { get; set; }
        public string? position_code { get; set; }
        public string? position_family { get; set; }
        public string? position_family_code { get; set; }
        public string? position_family_name { get; set; }
        public string? position_id { get; set; }
        public string? position_level { get; set; }
        public string? position_seat_number { get; set; }
        public string? position_seat_title { get; set; }
        public string? position_title { get; set; }
        public DateTime? previous_termination_date { get; set; }
        public string? primary_phone { get; set; }
        public int primary_phone_type { get; set; }
        public string? primary_schedule_group_description { get; set; }
        public bool print_ee_message { get; set; }
        public string? rate_1 { get; set; }
        public DateTime? rehire_date { get; set; }
        public bool report_new_hire { get; set; }
        public bool retirement_plan { get; set; }
        public int schedule_group { get; set; }
        public string? schedule_time_zone { get; set; }
        public string? secondary_phone { get; set; }
        public int? secondary_phone_type { get; set; }
        public string? state { get; set; }
        public bool? statutory_employee { get; set; }
        public string? street { get; set; }
        public string? supervisor_approval { get; set; }
        public string? supervisor_approval_code { get; set; }
        public string? supervisor_primary { get; set; }
        public string? supervisor_primary_code { get; set; }
        public string? supervisor_quaternary { get; set; }
        public string? supervisor_quaternary_code { get; set; }
        public string? supervisor_secondary { get; set; }
        public string? supervisor_secondary_code { get; set; }
        public string? supervisor_talent { get; set; }
        public string? supervisor_talent_management { get; set; }
        public string? supervisor_tertiary { get; set; }
        public string? supervisor_tertiary_code { get; set; }
        public string? termination_reason { get; set; }
        public string? termination_type { get; set; }
        public DateTime? termination_date { get; set; }
        public int vets_4212_emp_category { get; set; }
        public string? vets_4212_job_category { get; set; }
        public string? workers_comp_code { get; set; }
        public string? workers_comp_desc { get; set; }
        public string? work_email { get; set; }
        public string? zipcode { get; set; }
        public int companyLocationId { get; set; }
        public int? companyEstablishmentId { get; set; }
        public string? dol_status { get; set; }
        public string? exempt_status { get; set; }
    }

    public class NewHireEntry
    {
        public int new_hire_id {  get; set; }
        public string status {  get; set; } // "Pending New Hire", "New Hire"
        public string? addedby { get; set; }
        public string? eecode { get; set; }
        public string? eename { get; set; }
        public string? timeadded { get; set; }
        public int? usertype { get; set; }
        public string? supervisor_primary_code { get; set; }
    }

    public class AuditLogEntry
    {
        public string? changedby { get; set; }

        public string? changedesc { get; set; }

        public string? changetime { get; set; }

        public string? changetype { get; set; }

        public string? childcode { get; set; }

        public string clockseq { get; set; }

        public string eecode { get; set; }

        public string eename { get; set; }

        public string new_value { get; set; }

        public string? notes { get; set; }

        public string old_value { get; set; }

        public int usetype { get; set; }
    }

    // annoyingly Paycom uses a different JSON format for punch history records
    public class HistoricalPunch
    {
        public bool IsPaidTimeOff()
        {
            if (string.IsNullOrEmpty(EarnCode))
            {
                return false;
            }

            // Create a HashSet with the specified values for efficient lookup
            HashSet<string> validValues = new HashSet<string>
            {
                "P", "PLS", "PLL", "SLL", "ISP", "FH2", "IPP", "F2H", "PTO"
            };

            // Check if the input value is in the set (case-sensitive)
            return validValues.Contains(EarnCode);
        }
        
        [JsonPropertyName("entrytype")]
        public int EntryType { get; set; }

        [JsonPropertyName("punchtime")]
        public DateTimeOffset PunchTime { get; set; }

        [JsonPropertyName("timeadded")]
        public DateTimeOffset TimeAdded { get; set; }

        [JsonPropertyName("punchtype")]
        public string PunchType { get; set; }

        [JsonPropertyName("punchdesc")]
        public string PunchDescription { get; set; }

        [JsonPropertyName("eecode")]
        public string EmployeeCode { get; set; }

        [JsonPropertyName("deptcode")]
        public string DepartmentCode { get; set; }

        [JsonPropertyName("earncode")]
        public string EarnCode { get; set; }

        [JsonPropertyName("taxprofid")]
        public int TaxProfileId { get; set; }

        [JsonPropertyName("eebadge")]
        public string EmployeeBadge { get; set; }

        [JsonPropertyName("clocktype")]
        public string ClockType { get; set; }

        [JsonPropertyName("hours")]
        public string Hours { get; set; }

        [JsonPropertyName("dollaramount")]
        public string DollarAmount { get; set; }

        [JsonPropertyName("units")]
        public string Units { get; set; }

        [JsonPropertyName("isdeleted")]
        public int IsDeleted { get; set; }

        [JsonPropertyName("clockseq")]
        public string ClockSequence { get; set; }

        [JsonPropertyName("clockid")]
        public string ClockId { get; set; }

        [JsonPropertyName("cat1")]
        public string Category1 { get; set; }

        [JsonPropertyName("cat1desc")]
        public string Category1Description { get; set; }

        [JsonPropertyName("punchid")]
        public int PunchId { get; set; }

        [JsonPropertyName("is_archived")]
        public bool IsArchived { get; set; }
    }

    public class Punch
    {
        [JsonPropertyName("dollaramount")]
        public double? DollarAmount { get; set; }

        [JsonPropertyName("clocksequencenumber")]
        public string? ClockSeq { get; set; }

        [JsonPropertyName("eecode")]
        public string? EECode { get; set; }

        [JsonPropertyName("externalid")]
        public string? ExternalId { get; set; }

        [JsonPropertyName("entrytype")]
        public required int EntryType { get; set; }

        [JsonPropertyName("punchtime")]
        public required string PunchTime { get; set; }

        [JsonPropertyName("punchtype")]
        public string? PunchType { get; set; }

        [JsonPropertyName("timezone")]
        public required string TimeZone { get; set; }

        [JsonPropertyName(name: "deptcode")]
        public string? DeptCode { get; set; }

        [JsonPropertyName(name: "punchdesc")]
        public string? Description { get; set; }

        [JsonPropertyName(name: "earncode")]
        public string? EarnCode { get; set; }

        [JsonPropertyName(name: "cat1")]
         public string? Cat1 { get; set; }
        [JsonPropertyName(name: "cat2")] 
        public string? Cat2 { get; set; }
        [JsonPropertyName(name: "cat3")] 
        public string? Cat3 { get; set; }
    }
}