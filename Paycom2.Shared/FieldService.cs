﻿using Payroll.Shared;
using System;
using System.Collections.Generic;
using System.Net;
using System.Text;

namespace Paycom2.Shared
{
    public static class FieldService
    {
        public readonly static string FieldSection = "paycom_fields";
        public static string FirstNameField = "FirstName";
        public static string DivisionCodeField = "";
        public static string DivisionNameField = "";
        public static string OrgCodeField = "";
        public static string LocationField = "deptcode";
        public static string JobLocationField = "";
        public static string JobCodeField = "";
        public static string JobNameField = "position_id";


        static FieldService()
        {
            Setting.Init();
            FirstNameField = Setting.Get(FieldSection, "fname").ToLower();
            DivisionCodeField = Setting.Get(FieldSection, "division_code").ToLower();
            DivisionNameField = Setting.Get(FieldSection, "division_name").ToLower();
            LocationField = Setting.Get(FieldSection, "location").ToLower();
            OrgCodeField = Setting.Get(FieldSection, "org_code").ToLower();

            // these settings are used to configure where the job code and location are stored on punches
            JobLocationField = Setting.Get(FieldSection, "jobloc").ToLower();
            JobCodeField = Setting.Get(FieldSection, "jobcode").ToLower();
            JobNameField = Setting.Get(FieldSection, "jobname").ToLower();
        }

        public static string GetFirstName(MasterEmployeeRecord record)
        {
            if (string.IsNullOrEmpty(record.nickname) || FirstNameField != "nickname")
                return record.firstname.ToProperCase();

            return record.nickname.ToProperCase();
        }

        public static void AddSensitiveDataAttributes(Employee e, MasterEmployeeRecord mer)
        {
            e.AddAttribute("manager_name", mer.supervisor_primary);
            e.AddAttribute("manager_code", mer.supervisor_primary_code);
            e.AddAttribute("business_title", mer.business_title);
            e.AddAttribute("middle_name", mer.middlename);
            e.AddAttribute("badge_no", mer.employee_badge);
            e.AddAttribute("dol_status", mer.dol_status);
            e.AddAttribute("rate", mer.rate_1);

            if (!string.IsNullOrEmpty(mer.nickname))
                e.AddAttribute("nickname", mer.nickname);


            if (!string.IsNullOrEmpty(JobCodeField))
                e.AddAttribute("job_code", FieldService.GetPrimaryJobCode(mer));
            if (!string.IsNullOrEmpty(DivisionCodeField))
                e.AddAttribute("division_code", FieldService.GetDivisionCode(mer));
            if (!string.IsNullOrEmpty(DivisionNameField))
                e.AddAttribute("division_name", FieldService.GetDivisionName(mer));
            if (!string.IsNullOrEmpty(mer.termination_reason))
                e.AddAttribute("term_reason", mer.termination_reason);
            if (!string.IsNullOrEmpty(OrgCodeField))
                e.OrgCode = FieldService.GetOrgCode(mer);
        }

        public static string? GetPrimaryJobCode(MasterEmployeeRecord record)
        {
            switch (JobCodeField)
            {
                case "cat1":
                    return record.cat1;
                case "cat2":
                    return record.cat2;
                case "cat3":
                    return record.cat3;
                case "cat4":
                    return record.cat4;
                case "position_code":
                    return record.position_code;
                default:
                    return string.Empty;
            }
        }

        public static string? GetPrimaryJobName(MasterEmployeeRecord record)
        {
            switch (JobNameField)
            {
                case "cat1desc":
                    return record.cat1desc ?? "";
                case "cat2desc":
                    return record.cat2desc ?? "";
                case "cat3desc":
                    return record.cat3desc ?? "";
                case "cat4desc":
                    return record.cat4desc ?? "";
                case "position_id":
                    return record.position_id ?? "";
                default:
                    return string.Empty;
            }
        }

        public static string? GetDivisionCode(MasterEmployeeRecord record)
        {
            switch (DivisionCodeField)
            {
                case "cat1":
                    return record.cat1;
                case "cat2":
                    return record.cat2;
                case "cat3":
                    return record.cat3;
                default:
                    return string.Empty;
            }
        }

        public static string GetOrgCode(MasterEmployeeRecord record)
        {
            switch (OrgCodeField)
            {
                case "new_hire":
                    return record.new_hire ? "new" : "cur";
                case "cat1":
                    return record.cat1;
                default:
                    return string.Empty;
            }
        }

        public static string GetDivisionName(MasterEmployeeRecord record)
        {
            switch (DivisionNameField)
            {
                case "cat1desc":
                    return record.cat1desc ?? "";
                case "cat2desc":
                    return record.cat2desc ?? "";
                case "cat3desc":
                    return record.cat3desc ?? "";
                default:
                    return string.Empty;
            }
        }

        public static string GetWorkLocationName(MasterEmployeeRecord record, Dictionary<int, Location> locationMap)
        {
            switch (LocationField)
            {
                case "deptcode":
                    return record.department_code ?? "";
                case "cat1":
                    return record.cat1 ?? "";
                case "cat2":
                    return record.cat2 ?? "";
                case "cat3":
                    return record.cat3 ?? "";
                case "loccode":
                    if (!locationMap.ContainsKey(record.companyLocationId)) return string.Empty;
                    var loc = locationMap[record.companyLocationId];
                    return loc.address == null ? string.Empty : loc.address;
                default:
                    return string.Empty;
            }
        }

        public static bool UseClockSeqAsEmployeeId()
        {
            var eid = Setting.Get(FieldSection, "eid");

            // this is the default, used by most customers
            if (string.IsNullOrEmpty(eid)) return true;

            return eid.Trim() != "sysid";
        }

        private static void SetPunchValue(Punch punch, string field, string val)
        {
            // if unconfigured skip trying to set this field
            if (string.IsNullOrEmpty(field)) return;

            switch (field)
            {
                case "deptcode":
                    punch.DeptCode = val;
                    break;
                case "cat1":
                    punch.Cat1 = val;
                    break;
                case "cat2":
                    punch.Cat2 = val;
                    break;
                case "cat3":
                    punch.Cat3 = val;
                    break;
            }
        }

        public static void SetPunchLocation(Punch punch, string location)
        {
            SetPunchValue(punch, LocationField, location);
        }
    }
}
