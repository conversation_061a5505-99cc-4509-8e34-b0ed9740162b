using System;
using System.Collections.Generic;
using System.Text.Json;
using System.Text.Json.Serialization;

namespace Paycom2.Shared
{
    public class PunchAuditDataConverter : JsonConverter<PunchAuditData>
    {
        public override PunchAuditData Read(ref Utf8JsonReader reader, Type typeToConvert, JsonSerializerOptions options)
        {
            if (reader.TokenType == JsonTokenType.String)
            {
                // If data is a string (empty in error cases), return empty object
                reader.GetString();
                return new PunchAuditData();
            }

            if (reader.TokenType != JsonTokenType.StartObject)
            {
                throw new JsonException("Expected object or string value for PunchAuditData");
            }

            return JsonSerializer.Deserialize<PunchAuditData>(ref reader, options);
        }

        public override void Write(Utf8JsonWriter writer, PunchAuditData value, JsonSerializerOptions options)
        {
            JsonSerializer.Serialize(writer, value, options);
        }
    }

    public class PunchAuditApiResponse
    {
        [JsonPropertyName("result")]
        public bool Result { get; set; }

        [JsonPropertyName("data")]
        [JsonConverter(typeof(PunchAuditDataConverter))]
        public PunchAuditData Data { get; set; } = new PunchAuditData();

        [JsonPropertyName("errors")]
        public List<string> Errors { get; set; } = new List<string>();

        [JsonPropertyName("errorCount")]
        public int ErrorCount { get; set; }

        [JsonPropertyName("records")]
        public int Records { get; set; }
    }

    public class PunchAuditData
    {
        [JsonPropertyName("eecode")]
        public string EECode { get; set; }

        [JsonPropertyName("active")]
        public List<ActivePunch> Active { get; set; } = new List<ActivePunch>();

        [JsonPropertyName("changed")]
        public List<ChangedPunch> Changed { get; set; } = new List<ChangedPunch>();

        [JsonPropertyName("deleted")]
        public List<DeletedPunch> Deleted { get; set; } = new List<DeletedPunch>();

        [JsonPropertyName("timecardapproval")]
        public List<TimeCardApproval> TimeCardApproval { get; set; } = new List<TimeCardApproval>();
    }

    public class ActivePunch
    {
        [JsonPropertyName("eebadge")]
        public string EEBadge { get; set; }

        [JsonPropertyName("punchid")]
        public int PunchId { get; set; }

        [JsonPropertyName("punchtype")]
        public string PunchType { get; set; }

        [JsonPropertyName("punchdate")]
        public string PunchDate { get; set; }

        [JsonPropertyName("punchtime")]
        public string PunchTime { get; set; }

        [JsonPropertyName("rnd")]
        public string Rnd { get; set; }

        [JsonPropertyName("effective")]
        public string Effective { get; set; }

        [JsonPropertyName("earndesc")]
        public string EarnDesc { get; set; }

        [JsonPropertyName("allocation")]
        public string Allocation { get; set; }

        [JsonPropertyName("deptcode")]
        public string DeptCode { get; set; }

        [JsonPropertyName("dollaramount")]
        public string DollarAmount { get; set; }

        [JsonPropertyName("hours")]
        public string Hours { get; set; }

        [JsonPropertyName("units")]
        public string Units { get; set; }

        [JsonPropertyName("batch")]
        public bool Batch { get; set; }

        [JsonPropertyName("clockid")]
        public string ClockId { get; set; }

        [JsonPropertyName("remoteip")]
        public string RemoteIP { get; set; }

        [JsonPropertyName("location")]
        public string Location { get; set; }

        [JsonPropertyName("lastmodified")]
        public string LastModified { get; set; }

        [JsonPropertyName("modifiedby")]
        public string ModifiedBy { get; set; }
    }

    public class ChangedPunch
    {
        [JsonPropertyName("eebadge")]
        public string EEBadge { get; set; }

        [JsonPropertyName("changetime")]
        public string ChangeTime { get; set; }

        [JsonPropertyName("dateaffected")]
        public string DateAffected { get; set; }

        [JsonPropertyName("changesmadeby")]
        public string ChangesMadeBy { get; set; }

        [JsonPropertyName("changedesc")]
        public string ChangeDesc { get; set; }

        [JsonPropertyName("oldval")]
        public string OldValue { get; set; }

        [JsonPropertyName("newval")]
        public string NewValue { get; set; }
    }

    public class DeletedPunch
    {
        [JsonPropertyName("eebadge")]
        public string EEBadge { get; set; }

        [JsonPropertyName("punchid")]
        public int PunchId { get; set; }

        [JsonPropertyName("punchtype")]
        public string PunchType { get; set; }

        [JsonPropertyName("punchdate")]
        public string PunchDate { get; set; }

        [JsonPropertyName("punchtime")]
        public string PunchTime { get; set; }

        [JsonPropertyName("rnd")]
        public string Rnd { get; set; }

        [JsonPropertyName("effective")]
        public string Effective { get; set; }

        [JsonPropertyName("earndesc")]
        public string EarnDesc { get; set; }

        [JsonPropertyName("allocation")]
        public string Allocation { get; set; }

        [JsonPropertyName("deptcode")]
        public string DeptCode { get; set; }

        [JsonPropertyName("dollaramount")]
        public string DollarAmount { get; set; }

        [JsonPropertyName("hours")]
        public string Hours { get; set; }

        [JsonPropertyName("batch")]
        public string Batch { get; set; }

        [JsonPropertyName("clockid")]
        public string ClockId { get; set; }

        [JsonPropertyName("remoteip")]
        public string RemoteIP { get; set; }

        [JsonPropertyName("deleted")]
        public string Deleted { get; set; }

        [JsonPropertyName("deletedby")]
        public string DeletedBy { get; set; }
    }

    public class TimeCardApproval
    {
        [JsonPropertyName("changetime")]
        public string ChangeTime { get; set; }

        [JsonPropertyName("dateaffected")]
        public string DateAffected { get; set; }

        [JsonPropertyName("changesmadeby")]
        public string ChangesMadeBy { get; set; }

        [JsonPropertyName("changedesc")]
        public string ChangeDesc { get; set; }
    }
} 