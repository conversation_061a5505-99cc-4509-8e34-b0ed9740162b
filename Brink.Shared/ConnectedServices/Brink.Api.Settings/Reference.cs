﻿//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

namespace Brink.Api.Settings
{
    using System.Runtime.Serialization;
    
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.0.1-preview-30310-0943")]
    [System.Runtime.Serialization.DataContractAttribute(Name="CashDrawer", Namespace="http://schemas.datacontract.org/2004/07/Pos.Web.Service.Data")]
    public partial class CashDrawer : object
    {
        
        private decimal DefaultBankField;
        
        private byte DrawerNumberField;
        
        private int IdField;
        
        private bool IsCompulsoryField;
        
        private bool IsPublicField;
        
        private string NameField;
        
        private int TerminalIdField;
        
        private int TypeField;
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public decimal DefaultBank
        {
            get
            {
                return this.DefaultBankField;
            }
            set
            {
                this.DefaultBankField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public byte DrawerNumber
        {
            get
            {
                return this.DrawerNumberField;
            }
            set
            {
                this.DrawerNumberField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public int Id
        {
            get
            {
                return this.IdField;
            }
            set
            {
                this.IdField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public bool IsCompulsory
        {
            get
            {
                return this.IsCompulsoryField;
            }
            set
            {
                this.IsCompulsoryField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public bool IsPublic
        {
            get
            {
                return this.IsPublicField;
            }
            set
            {
                this.IsPublicField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string Name
        {
            get
            {
                return this.NameField;
            }
            set
            {
                this.NameField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public int TerminalId
        {
            get
            {
                return this.TerminalIdField;
            }
            set
            {
                this.TerminalIdField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public int Type
        {
            get
            {
                return this.TypeField;
            }
            set
            {
                this.TypeField = value;
            }
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.0.1-preview-30310-0943")]
    [System.Runtime.Serialization.DataContractAttribute(Name="AccessFault", Namespace="http://schemas.datacontract.org/2004/07/Pos.Web.Service.Faults")]
    public partial class AccessFault : object
    {
        
        private Brink.Api.Settings.AccessErrorCode CodeField;
        
        private string MessageField;
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public Brink.Api.Settings.AccessErrorCode Code
        {
            get
            {
                return this.CodeField;
            }
            set
            {
                this.CodeField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string Message
        {
            get
            {
                return this.MessageField;
            }
            set
            {
                this.MessageField = value;
            }
        }
    }
    
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.0.1-preview-30310-0943")]
    [System.Runtime.Serialization.DataContractAttribute(Name="AccessErrorCode", Namespace="http://schemas.datacontract.org/2004/07/Pos.Web.Service.Faults")]
    public enum AccessErrorCode : int
    {
        
        [System.Runtime.Serialization.EnumMemberAttribute()]
        InvalidToken = 1,
        
        [System.Runtime.Serialization.EnumMemberAttribute()]
        AccessDenied = 2,
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.0.1-preview-30310-0943")]
    [System.Runtime.Serialization.DataContractAttribute(Name="LocationFault", Namespace="http://schemas.datacontract.org/2004/07/Pos.Web.Service.Faults")]
    public partial class LocationFault : object
    {
        
        private Brink.Api.Settings.LocationErrorCode CodeField;
        
        private string MessageField;
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public Brink.Api.Settings.LocationErrorCode Code
        {
            get
            {
                return this.CodeField;
            }
            set
            {
                this.CodeField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string Message
        {
            get
            {
                return this.MessageField;
            }
            set
            {
                this.MessageField = value;
            }
        }
    }
    
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.0.1-preview-30310-0943")]
    [System.Runtime.Serialization.DataContractAttribute(Name="LocationErrorCode", Namespace="http://schemas.datacontract.org/2004/07/Pos.Web.Service.Faults")]
    public enum LocationErrorCode : int
    {
        
        [System.Runtime.Serialization.EnumMemberAttribute()]
        InvalidToken = 1,
        
        [System.Runtime.Serialization.EnumMemberAttribute()]
        Disabled = 2,
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.0.1-preview-30310-0943")]
    [System.Runtime.Serialization.DataContractAttribute(Name="DayPart", Namespace="http://schemas.datacontract.org/2004/07/Pos.Web.Service.Data")]
    public partial class DayPart : object
    {
        
        private int IdField;
        
        private string NameField;
        
        private System.DateTime StartTimeField;
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public int Id
        {
            get
            {
                return this.IdField;
            }
            set
            {
                this.IdField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string Name
        {
            get
            {
                return this.NameField;
            }
            set
            {
                this.NameField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public System.DateTime StartTime
        {
            get
            {
                return this.StartTimeField;
            }
            set
            {
                this.StartTimeField = value;
            }
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.0.1-preview-30310-0943")]
    [System.Runtime.Serialization.DataContractAttribute(Name="Destination", Namespace="http://schemas.datacontract.org/2004/07/Pos.Web.Service.Data")]
    public partial class Destination : object
    {
        
        private string DescriptionField;
        
        private int IdField;
        
        private string KitchenDescriptionField;
        
        private string NameField;
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string Description
        {
            get
            {
                return this.DescriptionField;
            }
            set
            {
                this.DescriptionField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public int Id
        {
            get
            {
                return this.IdField;
            }
            set
            {
                this.IdField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string KitchenDescription
        {
            get
            {
                return this.KitchenDescriptionField;
            }
            set
            {
                this.KitchenDescriptionField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string Name
        {
            get
            {
                return this.NameField;
            }
            set
            {
                this.NameField = value;
            }
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.0.1-preview-30310-0943")]
    [System.Runtime.Serialization.DataContractAttribute(Name="Discount", Namespace="http://schemas.datacontract.org/2004/07/Pos.Web.Service.Data")]
    public partial class Discount : object
    {
        
        private bool ActiveField;
        
        private bool AllowLaterDiscountsField;
        
        private bool AllowPriorDiscountsField;
        
        private decimal AmountField;
        
        private bool DiscountedItemsEligibleField;
        
        private System.Collections.Generic.List<int> EligibleItemGroupIdsField;
        
        private int EligibleItemIdField;
        
        private System.DateTime EndDateField;
        
        private System.DateTime EndTimeField;
        
        private bool EnforceMaximumAmountField;
        
        private bool EnforceMaximumItemsField;
        
        private bool EnforceMaximumPerOrderField;
        
        private bool ExcludeItemsFromProductMixField;
        
        private bool FridayField;
        
        private Brink.Api.Settings.DiscountGranularity GranularityField;
        
        private int IdField;
        
        private bool IsEmployeeDiscountField;
        
        private bool IsLoyaltyDiscountField;
        
        private bool ManagerNeededField;
        
        private decimal MaximumAmountField;
        
        private int MaximumItemsField;
        
        private int MaximumPerOrderField;
        
        private bool MondayField;
        
        private bool MustEnterAmountField;
        
        private bool MustEnterNameField;
        
        private bool MustEnterPercentField;
        
        private string NameField;
        
        private bool OpenDrawerField;
        
        private bool PrintOrderField;
        
        private string PrintedNameField;
        
        private decimal RateField;
        
        private bool SaturdayField;
        
        private System.DateTime StartDateField;
        
        private System.DateTime StartTimeField;
        
        private bool SundayField;
        
        private bool ThursdayField;
        
        private bool TuesdayField;
        
        private Brink.Api.Settings.DiscountType TypeField;
        
        private bool UseDateRangeFilterField;
        
        private bool UseDayFilterField;
        
        private bool UseItemFilterField;
        
        private bool UseItemGroupFilterField;
        
        private bool UseTimeRangeFilterField;
        
        private bool WednesdayField;
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public bool Active
        {
            get
            {
                return this.ActiveField;
            }
            set
            {
                this.ActiveField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public bool AllowLaterDiscounts
        {
            get
            {
                return this.AllowLaterDiscountsField;
            }
            set
            {
                this.AllowLaterDiscountsField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public bool AllowPriorDiscounts
        {
            get
            {
                return this.AllowPriorDiscountsField;
            }
            set
            {
                this.AllowPriorDiscountsField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public decimal Amount
        {
            get
            {
                return this.AmountField;
            }
            set
            {
                this.AmountField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public bool DiscountedItemsEligible
        {
            get
            {
                return this.DiscountedItemsEligibleField;
            }
            set
            {
                this.DiscountedItemsEligibleField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public System.Collections.Generic.List<int> EligibleItemGroupIds
        {
            get
            {
                return this.EligibleItemGroupIdsField;
            }
            set
            {
                this.EligibleItemGroupIdsField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public int EligibleItemId
        {
            get
            {
                return this.EligibleItemIdField;
            }
            set
            {
                this.EligibleItemIdField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public System.DateTime EndDate
        {
            get
            {
                return this.EndDateField;
            }
            set
            {
                this.EndDateField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public System.DateTime EndTime
        {
            get
            {
                return this.EndTimeField;
            }
            set
            {
                this.EndTimeField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public bool EnforceMaximumAmount
        {
            get
            {
                return this.EnforceMaximumAmountField;
            }
            set
            {
                this.EnforceMaximumAmountField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public bool EnforceMaximumItems
        {
            get
            {
                return this.EnforceMaximumItemsField;
            }
            set
            {
                this.EnforceMaximumItemsField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public bool EnforceMaximumPerOrder
        {
            get
            {
                return this.EnforceMaximumPerOrderField;
            }
            set
            {
                this.EnforceMaximumPerOrderField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public bool ExcludeItemsFromProductMix
        {
            get
            {
                return this.ExcludeItemsFromProductMixField;
            }
            set
            {
                this.ExcludeItemsFromProductMixField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public bool Friday
        {
            get
            {
                return this.FridayField;
            }
            set
            {
                this.FridayField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public Brink.Api.Settings.DiscountGranularity Granularity
        {
            get
            {
                return this.GranularityField;
            }
            set
            {
                this.GranularityField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public int Id
        {
            get
            {
                return this.IdField;
            }
            set
            {
                this.IdField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public bool IsEmployeeDiscount
        {
            get
            {
                return this.IsEmployeeDiscountField;
            }
            set
            {
                this.IsEmployeeDiscountField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public bool IsLoyaltyDiscount
        {
            get
            {
                return this.IsLoyaltyDiscountField;
            }
            set
            {
                this.IsLoyaltyDiscountField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public bool ManagerNeeded
        {
            get
            {
                return this.ManagerNeededField;
            }
            set
            {
                this.ManagerNeededField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public decimal MaximumAmount
        {
            get
            {
                return this.MaximumAmountField;
            }
            set
            {
                this.MaximumAmountField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public int MaximumItems
        {
            get
            {
                return this.MaximumItemsField;
            }
            set
            {
                this.MaximumItemsField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public int MaximumPerOrder
        {
            get
            {
                return this.MaximumPerOrderField;
            }
            set
            {
                this.MaximumPerOrderField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public bool Monday
        {
            get
            {
                return this.MondayField;
            }
            set
            {
                this.MondayField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public bool MustEnterAmount
        {
            get
            {
                return this.MustEnterAmountField;
            }
            set
            {
                this.MustEnterAmountField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public bool MustEnterName
        {
            get
            {
                return this.MustEnterNameField;
            }
            set
            {
                this.MustEnterNameField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public bool MustEnterPercent
        {
            get
            {
                return this.MustEnterPercentField;
            }
            set
            {
                this.MustEnterPercentField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string Name
        {
            get
            {
                return this.NameField;
            }
            set
            {
                this.NameField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public bool OpenDrawer
        {
            get
            {
                return this.OpenDrawerField;
            }
            set
            {
                this.OpenDrawerField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public bool PrintOrder
        {
            get
            {
                return this.PrintOrderField;
            }
            set
            {
                this.PrintOrderField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string PrintedName
        {
            get
            {
                return this.PrintedNameField;
            }
            set
            {
                this.PrintedNameField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public decimal Rate
        {
            get
            {
                return this.RateField;
            }
            set
            {
                this.RateField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public bool Saturday
        {
            get
            {
                return this.SaturdayField;
            }
            set
            {
                this.SaturdayField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public System.DateTime StartDate
        {
            get
            {
                return this.StartDateField;
            }
            set
            {
                this.StartDateField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public System.DateTime StartTime
        {
            get
            {
                return this.StartTimeField;
            }
            set
            {
                this.StartTimeField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public bool Sunday
        {
            get
            {
                return this.SundayField;
            }
            set
            {
                this.SundayField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public bool Thursday
        {
            get
            {
                return this.ThursdayField;
            }
            set
            {
                this.ThursdayField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public bool Tuesday
        {
            get
            {
                return this.TuesdayField;
            }
            set
            {
                this.TuesdayField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public Brink.Api.Settings.DiscountType Type
        {
            get
            {
                return this.TypeField;
            }
            set
            {
                this.TypeField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public bool UseDateRangeFilter
        {
            get
            {
                return this.UseDateRangeFilterField;
            }
            set
            {
                this.UseDateRangeFilterField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public bool UseDayFilter
        {
            get
            {
                return this.UseDayFilterField;
            }
            set
            {
                this.UseDayFilterField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public bool UseItemFilter
        {
            get
            {
                return this.UseItemFilterField;
            }
            set
            {
                this.UseItemFilterField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public bool UseItemGroupFilter
        {
            get
            {
                return this.UseItemGroupFilterField;
            }
            set
            {
                this.UseItemGroupFilterField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public bool UseTimeRangeFilter
        {
            get
            {
                return this.UseTimeRangeFilterField;
            }
            set
            {
                this.UseTimeRangeFilterField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public bool Wednesday
        {
            get
            {
                return this.WednesdayField;
            }
            set
            {
                this.WednesdayField = value;
            }
        }
    }
    
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.0.1-preview-30310-0943")]
    [System.Runtime.Serialization.DataContractAttribute(Name="DiscountGranularity", Namespace="http://schemas.datacontract.org/2004/07/Pos")]
    public enum DiscountGranularity : sbyte
    {
        
        [System.Runtime.Serialization.EnumMemberAttribute()]
        Order = 0,
        
        [System.Runtime.Serialization.EnumMemberAttribute()]
        Items = 1,
    }
    
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.0.1-preview-30310-0943")]
    [System.Runtime.Serialization.DataContractAttribute(Name="DiscountType", Namespace="http://schemas.datacontract.org/2004/07/Pos")]
    public enum DiscountType : sbyte
    {
        
        [System.Runtime.Serialization.EnumMemberAttribute()]
        None = 0,
        
        [System.Runtime.Serialization.EnumMemberAttribute()]
        Comp = 1,
        
        [System.Runtime.Serialization.EnumMemberAttribute()]
        NewPrice = 2,
        
        [System.Runtime.Serialization.EnumMemberAttribute()]
        Max = 3,
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.0.1-preview-30310-0943")]
    [System.Runtime.Serialization.DataContractAttribute(Name="Employee", Namespace="http://schemas.datacontract.org/2004/07/Pos.Web.Service.Data")]
    public partial class Employee : object
    {
        
        private string Address1Field;
        
        private string Address2Field;
        
        private System.DateTime BirthDateField;
        
        private string CellPhoneField;
        
        private string CityField;
        
        private string EmailAddressField;
        
        private string FirstNameField;
        
        private System.DateTime HireDateField;
        
        private string HomePhoneField;
        
        private int IdField;
        
        private bool IdentificationVerifiedField;
        
        private Brink.Api.Settings.EmployeeJobs JobsField;
        
        private string LastNameField;
        
        private Brink.Api.Settings.MaritalStatus MaritalStatusField;
        
        private string MiddleNameField;
        
        private string NameField;
        
        private string NotesField;
        
        private string PayrollIdField;
        
        private string SSNField;
        
        private string StateField;
        
        private int TaxWithholdingAllowanceField;
        
        private bool TerminatedField;
        
        private System.DateTime TerminationDateField;
        
        private string ZipField;
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string Address1
        {
            get
            {
                return this.Address1Field;
            }
            set
            {
                this.Address1Field = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string Address2
        {
            get
            {
                return this.Address2Field;
            }
            set
            {
                this.Address2Field = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public System.DateTime BirthDate
        {
            get
            {
                return this.BirthDateField;
            }
            set
            {
                this.BirthDateField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string CellPhone
        {
            get
            {
                return this.CellPhoneField;
            }
            set
            {
                this.CellPhoneField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string City
        {
            get
            {
                return this.CityField;
            }
            set
            {
                this.CityField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string EmailAddress
        {
            get
            {
                return this.EmailAddressField;
            }
            set
            {
                this.EmailAddressField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string FirstName
        {
            get
            {
                return this.FirstNameField;
            }
            set
            {
                this.FirstNameField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public System.DateTime HireDate
        {
            get
            {
                return this.HireDateField;
            }
            set
            {
                this.HireDateField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string HomePhone
        {
            get
            {
                return this.HomePhoneField;
            }
            set
            {
                this.HomePhoneField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public int Id
        {
            get
            {
                return this.IdField;
            }
            set
            {
                this.IdField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public bool IdentificationVerified
        {
            get
            {
                return this.IdentificationVerifiedField;
            }
            set
            {
                this.IdentificationVerifiedField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public Brink.Api.Settings.EmployeeJobs Jobs
        {
            get
            {
                return this.JobsField;
            }
            set
            {
                this.JobsField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string LastName
        {
            get
            {
                return this.LastNameField;
            }
            set
            {
                this.LastNameField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public Brink.Api.Settings.MaritalStatus MaritalStatus
        {
            get
            {
                return this.MaritalStatusField;
            }
            set
            {
                this.MaritalStatusField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string MiddleName
        {
            get
            {
                return this.MiddleNameField;
            }
            set
            {
                this.MiddleNameField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string Name
        {
            get
            {
                return this.NameField;
            }
            set
            {
                this.NameField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string Notes
        {
            get
            {
                return this.NotesField;
            }
            set
            {
                this.NotesField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string PayrollId
        {
            get
            {
                return this.PayrollIdField;
            }
            set
            {
                this.PayrollIdField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string SSN
        {
            get
            {
                return this.SSNField;
            }
            set
            {
                this.SSNField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string State
        {
            get
            {
                return this.StateField;
            }
            set
            {
                this.StateField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public int TaxWithholdingAllowance
        {
            get
            {
                return this.TaxWithholdingAllowanceField;
            }
            set
            {
                this.TaxWithholdingAllowanceField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public bool Terminated
        {
            get
            {
                return this.TerminatedField;
            }
            set
            {
                this.TerminatedField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public System.DateTime TerminationDate
        {
            get
            {
                return this.TerminationDateField;
            }
            set
            {
                this.TerminationDateField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string Zip
        {
            get
            {
                return this.ZipField;
            }
            set
            {
                this.ZipField = value;
            }
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.0.1-preview-30310-0943")]
    [System.Runtime.Serialization.CollectionDataContractAttribute(Name="EmployeeJobs", Namespace="http://schemas.datacontract.org/2004/07/Pos.Web.Service.Data", ItemName="EmployeeJob")]
    public class EmployeeJobs : System.Collections.Generic.List<Brink.Api.Settings.EmployeeJob>
    {
    }
    
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.0.1-preview-30310-0943")]
    [System.Runtime.Serialization.DataContractAttribute(Name="MaritalStatus", Namespace="http://schemas.datacontract.org/2004/07/Pos")]
    public enum MaritalStatus : sbyte
    {
        
        [System.Runtime.Serialization.EnumMemberAttribute()]
        None = 0,
        
        [System.Runtime.Serialization.EnumMemberAttribute()]
        Single = 1,
        
        [System.Runtime.Serialization.EnumMemberAttribute()]
        Married = 2,
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.0.1-preview-30310-0943")]
    [System.Runtime.Serialization.DataContractAttribute(Name="EmployeeJob", Namespace="http://schemas.datacontract.org/2004/07/Pos.Web.Service.Data")]
    public partial class EmployeeJob : object
    {
        
        private int JobIdField;
        
        private decimal PayRateField;
        
        private int SecurityLevelIdField;
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public int JobId
        {
            get
            {
                return this.JobIdField;
            }
            set
            {
                this.JobIdField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public decimal PayRate
        {
            get
            {
                return this.PayRateField;
            }
            set
            {
                this.PayRateField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public int SecurityLevelId
        {
            get
            {
                return this.SecurityLevelIdField;
            }
            set
            {
                this.SecurityLevelIdField = value;
            }
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.0.1-preview-30310-0943")]
    [System.Runtime.Serialization.DataContractAttribute(Name="ItemGroup", Namespace="http://schemas.datacontract.org/2004/07/Pos.Web.Service.Data")]
    public partial class ItemGroup : Brink.Api.Settings.SettingsObject
    {
        
        private string AlternateIdField;
        
        private System.Collections.Generic.List<Brink.Api.Settings.ItemGroupItem> ItemsField;
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string AlternateId
        {
            get
            {
                return this.AlternateIdField;
            }
            set
            {
                this.AlternateIdField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public System.Collections.Generic.List<Brink.Api.Settings.ItemGroupItem> Items
        {
            get
            {
                return this.ItemsField;
            }
            set
            {
                this.ItemsField = value;
            }
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.0.1-preview-30310-0943")]
    [System.Runtime.Serialization.DataContractAttribute(Name="SettingsObject", Namespace="http://schemas.datacontract.org/2004/07/Pos.Web.Service.Data")]
    [System.Runtime.Serialization.KnownTypeAttribute(typeof(Brink.Api.Settings.Item))]
    [System.Runtime.Serialization.KnownTypeAttribute(typeof(Brink.Api.Settings.LoyaltyReward))]
    [System.Runtime.Serialization.KnownTypeAttribute(typeof(Brink.Api.Settings.DiscountReward))]
    [System.Runtime.Serialization.KnownTypeAttribute(typeof(Brink.Api.Settings.GiftCardReward))]
    [System.Runtime.Serialization.KnownTypeAttribute(typeof(Brink.Api.Settings.PromotionReward))]
    [System.Runtime.Serialization.KnownTypeAttribute(typeof(Brink.Api.Settings.VoucherReward))]
    [System.Runtime.Serialization.KnownTypeAttribute(typeof(Brink.Api.Settings.ModifierCode))]
    [System.Runtime.Serialization.KnownTypeAttribute(typeof(Brink.Api.Settings.ModifierGroup))]
    [System.Runtime.Serialization.KnownTypeAttribute(typeof(Brink.Api.Settings.ItemGroup))]
    public partial class SettingsObject : object
    {
        
        private int IdField;
        
        private bool IsDeletedField;
        
        private string NameField;
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public int Id
        {
            get
            {
                return this.IdField;
            }
            set
            {
                this.IdField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public bool IsDeleted
        {
            get
            {
                return this.IsDeletedField;
            }
            set
            {
                this.IsDeletedField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string Name
        {
            get
            {
                return this.NameField;
            }
            set
            {
                this.NameField = value;
            }
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.0.1-preview-30310-0943")]
    [System.Runtime.Serialization.DataContractAttribute(Name="Item", Namespace="http://schemas.datacontract.org/2004/07/Pos.Web.Service.Data")]
    public partial class Item : Brink.Api.Settings.SettingsObject
    {
        
        private bool ActiveField;
        
        private bool AskNameField;
        
        private bool AskPriceField;
        
        private bool AvailableFridayField;
        
        private bool AvailableMondayField;
        
        private bool AvailableSaturdayField;
        
        private bool AvailableSelectDatesField;
        
        private bool AvailableSelectDaysField;
        
        private bool AvailableSundayField;
        
        private bool AvailableThursdayField;
        
        private bool AvailableTuesdayField;
        
        private bool AvailableWednesdayField;
        
        private System.Collections.Generic.List<Brink.Api.Settings.BrandAllocation> BrandAllocationsField;
        
        private System.Collections.Generic.List<Brink.Api.Settings.ItemCompositeComponent> ComponentsField;
        
        private decimal CostField;
        
        private System.DateTime CreatedTimeField;
        
        private Brink.Api.Settings.DateRange DatesAvailableField;
        
        private Brink.Api.Settings.GiftCardItemType GiftCardItemTypeField;
        
        private System.Collections.Generic.List<Brink.Api.Settings.ItemIncludedModifier> IncludedModifiersField;
        
        private System.Collections.Generic.List<int> IngredientItemIdsField;
        
        private bool IsComboField;
        
        private bool IsGiftCardField;
        
        private bool IsQuantityCountedField;
        
        private Brink.Api.Settings.ItemType ItemTypeField;
        
        private string KitchenNameField;
        
        private System.DateTime LastEditedTimeField;
        
        private System.Collections.Generic.List<int> ModifierGroupIdsField;
        
        private int ModifierTierIdField;
        
        private byte ModifierWeightField;
        
        private bool NonRevenueItemField;
        
        private string PLUField;
        
        private decimal PriceField;
        
        private byte PriceLevelField;
        
        private Brink.Api.Settings.PricePer PricePerField;
        
        private int PrinterGroupIdField;
        
        private int RevenueCenterIdField;
        
        private byte SortPriorityField;
        
        private int TareIdField;
        
        private System.Collections.Generic.List<int> TaxIdsField;
        
        private string UnitNameField;
        
        private byte UnitPrecisionField;
        
        private int VideoGroupIdField;
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public bool Active
        {
            get
            {
                return this.ActiveField;
            }
            set
            {
                this.ActiveField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public bool AskName
        {
            get
            {
                return this.AskNameField;
            }
            set
            {
                this.AskNameField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public bool AskPrice
        {
            get
            {
                return this.AskPriceField;
            }
            set
            {
                this.AskPriceField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public bool AvailableFriday
        {
            get
            {
                return this.AvailableFridayField;
            }
            set
            {
                this.AvailableFridayField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public bool AvailableMonday
        {
            get
            {
                return this.AvailableMondayField;
            }
            set
            {
                this.AvailableMondayField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public bool AvailableSaturday
        {
            get
            {
                return this.AvailableSaturdayField;
            }
            set
            {
                this.AvailableSaturdayField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public bool AvailableSelectDates
        {
            get
            {
                return this.AvailableSelectDatesField;
            }
            set
            {
                this.AvailableSelectDatesField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public bool AvailableSelectDays
        {
            get
            {
                return this.AvailableSelectDaysField;
            }
            set
            {
                this.AvailableSelectDaysField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public bool AvailableSunday
        {
            get
            {
                return this.AvailableSundayField;
            }
            set
            {
                this.AvailableSundayField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public bool AvailableThursday
        {
            get
            {
                return this.AvailableThursdayField;
            }
            set
            {
                this.AvailableThursdayField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public bool AvailableTuesday
        {
            get
            {
                return this.AvailableTuesdayField;
            }
            set
            {
                this.AvailableTuesdayField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public bool AvailableWednesday
        {
            get
            {
                return this.AvailableWednesdayField;
            }
            set
            {
                this.AvailableWednesdayField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public System.Collections.Generic.List<Brink.Api.Settings.BrandAllocation> BrandAllocations
        {
            get
            {
                return this.BrandAllocationsField;
            }
            set
            {
                this.BrandAllocationsField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public System.Collections.Generic.List<Brink.Api.Settings.ItemCompositeComponent> Components
        {
            get
            {
                return this.ComponentsField;
            }
            set
            {
                this.ComponentsField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public decimal Cost
        {
            get
            {
                return this.CostField;
            }
            set
            {
                this.CostField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public System.DateTime CreatedTime
        {
            get
            {
                return this.CreatedTimeField;
            }
            set
            {
                this.CreatedTimeField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public Brink.Api.Settings.DateRange DatesAvailable
        {
            get
            {
                return this.DatesAvailableField;
            }
            set
            {
                this.DatesAvailableField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public Brink.Api.Settings.GiftCardItemType GiftCardItemType
        {
            get
            {
                return this.GiftCardItemTypeField;
            }
            set
            {
                this.GiftCardItemTypeField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public System.Collections.Generic.List<Brink.Api.Settings.ItemIncludedModifier> IncludedModifiers
        {
            get
            {
                return this.IncludedModifiersField;
            }
            set
            {
                this.IncludedModifiersField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public System.Collections.Generic.List<int> IngredientItemIds
        {
            get
            {
                return this.IngredientItemIdsField;
            }
            set
            {
                this.IngredientItemIdsField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public bool IsCombo
        {
            get
            {
                return this.IsComboField;
            }
            set
            {
                this.IsComboField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public bool IsGiftCard
        {
            get
            {
                return this.IsGiftCardField;
            }
            set
            {
                this.IsGiftCardField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public bool IsQuantityCounted
        {
            get
            {
                return this.IsQuantityCountedField;
            }
            set
            {
                this.IsQuantityCountedField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public Brink.Api.Settings.ItemType ItemType
        {
            get
            {
                return this.ItemTypeField;
            }
            set
            {
                this.ItemTypeField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string KitchenName
        {
            get
            {
                return this.KitchenNameField;
            }
            set
            {
                this.KitchenNameField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public System.DateTime LastEditedTime
        {
            get
            {
                return this.LastEditedTimeField;
            }
            set
            {
                this.LastEditedTimeField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public System.Collections.Generic.List<int> ModifierGroupIds
        {
            get
            {
                return this.ModifierGroupIdsField;
            }
            set
            {
                this.ModifierGroupIdsField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public int ModifierTierId
        {
            get
            {
                return this.ModifierTierIdField;
            }
            set
            {
                this.ModifierTierIdField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public byte ModifierWeight
        {
            get
            {
                return this.ModifierWeightField;
            }
            set
            {
                this.ModifierWeightField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public bool NonRevenueItem
        {
            get
            {
                return this.NonRevenueItemField;
            }
            set
            {
                this.NonRevenueItemField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string PLU
        {
            get
            {
                return this.PLUField;
            }
            set
            {
                this.PLUField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public decimal Price
        {
            get
            {
                return this.PriceField;
            }
            set
            {
                this.PriceField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public byte PriceLevel
        {
            get
            {
                return this.PriceLevelField;
            }
            set
            {
                this.PriceLevelField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public Brink.Api.Settings.PricePer PricePer
        {
            get
            {
                return this.PricePerField;
            }
            set
            {
                this.PricePerField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public int PrinterGroupId
        {
            get
            {
                return this.PrinterGroupIdField;
            }
            set
            {
                this.PrinterGroupIdField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public int RevenueCenterId
        {
            get
            {
                return this.RevenueCenterIdField;
            }
            set
            {
                this.RevenueCenterIdField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public byte SortPriority
        {
            get
            {
                return this.SortPriorityField;
            }
            set
            {
                this.SortPriorityField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public int TareId
        {
            get
            {
                return this.TareIdField;
            }
            set
            {
                this.TareIdField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public System.Collections.Generic.List<int> TaxIds
        {
            get
            {
                return this.TaxIdsField;
            }
            set
            {
                this.TaxIdsField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string UnitName
        {
            get
            {
                return this.UnitNameField;
            }
            set
            {
                this.UnitNameField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public byte UnitPrecision
        {
            get
            {
                return this.UnitPrecisionField;
            }
            set
            {
                this.UnitPrecisionField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public int VideoGroupId
        {
            get
            {
                return this.VideoGroupIdField;
            }
            set
            {
                this.VideoGroupIdField = value;
            }
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.0.1-preview-30310-0943")]
    [System.Runtime.Serialization.DataContractAttribute(Name="LoyaltyReward", Namespace="http://schemas.datacontract.org/2004/07/Pos.Web.Service.Data")]
    [System.Runtime.Serialization.KnownTypeAttribute(typeof(Brink.Api.Settings.DiscountReward))]
    [System.Runtime.Serialization.KnownTypeAttribute(typeof(Brink.Api.Settings.GiftCardReward))]
    [System.Runtime.Serialization.KnownTypeAttribute(typeof(Brink.Api.Settings.PromotionReward))]
    [System.Runtime.Serialization.KnownTypeAttribute(typeof(Brink.Api.Settings.VoucherReward))]
    public partial class LoyaltyReward : Brink.Api.Settings.SettingsObject
    {
        
        private bool IsActiveField;
        
        private byte TypeField;
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public bool IsActive
        {
            get
            {
                return this.IsActiveField;
            }
            set
            {
                this.IsActiveField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public byte Type
        {
            get
            {
                return this.TypeField;
            }
            set
            {
                this.TypeField = value;
            }
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.0.1-preview-30310-0943")]
    [System.Runtime.Serialization.DataContractAttribute(Name="DiscountReward", Namespace="http://schemas.datacontract.org/2004/07/Pos.Web.Service.Data")]
    public partial class DiscountReward : Brink.Api.Settings.LoyaltyReward
    {
        
        private int DiscountIdField;
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public int DiscountId
        {
            get
            {
                return this.DiscountIdField;
            }
            set
            {
                this.DiscountIdField = value;
            }
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.0.1-preview-30310-0943")]
    [System.Runtime.Serialization.DataContractAttribute(Name="GiftCardReward", Namespace="http://schemas.datacontract.org/2004/07/Pos.Web.Service.Data")]
    public partial class GiftCardReward : Brink.Api.Settings.LoyaltyReward
    {
        
        private decimal AmountField;
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public decimal Amount
        {
            get
            {
                return this.AmountField;
            }
            set
            {
                this.AmountField = value;
            }
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.0.1-preview-30310-0943")]
    [System.Runtime.Serialization.DataContractAttribute(Name="PromotionReward", Namespace="http://schemas.datacontract.org/2004/07/Pos.Web.Service.Data")]
    public partial class PromotionReward : Brink.Api.Settings.LoyaltyReward
    {
        
        private int PromotionIdField;
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public int PromotionId
        {
            get
            {
                return this.PromotionIdField;
            }
            set
            {
                this.PromotionIdField = value;
            }
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.0.1-preview-30310-0943")]
    [System.Runtime.Serialization.DataContractAttribute(Name="VoucherReward", Namespace="http://schemas.datacontract.org/2004/07/Pos.Web.Service.Data")]
    public partial class VoucherReward : Brink.Api.Settings.LoyaltyReward
    {
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.0.1-preview-30310-0943")]
    [System.Runtime.Serialization.DataContractAttribute(Name="ModifierCode", Namespace="http://schemas.datacontract.org/2004/07/Pos.Web.Service.Data")]
    public partial class ModifierCode : Brink.Api.Settings.SettingsObject
    {
        
        private string AbbreviationField;
        
        private bool ActiveField;
        
        private Brink.Api.Settings.ModCodeIngredientType AppliesToField;
        
        private bool IsSystemField;
        
        private Brink.Api.Settings.ModCodeType TypeField;
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string Abbreviation
        {
            get
            {
                return this.AbbreviationField;
            }
            set
            {
                this.AbbreviationField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public bool Active
        {
            get
            {
                return this.ActiveField;
            }
            set
            {
                this.ActiveField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public Brink.Api.Settings.ModCodeIngredientType AppliesTo
        {
            get
            {
                return this.AppliesToField;
            }
            set
            {
                this.AppliesToField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public bool IsSystem
        {
            get
            {
                return this.IsSystemField;
            }
            set
            {
                this.IsSystemField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public Brink.Api.Settings.ModCodeType Type
        {
            get
            {
                return this.TypeField;
            }
            set
            {
                this.TypeField = value;
            }
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.0.1-preview-30310-0943")]
    [System.Runtime.Serialization.DataContractAttribute(Name="ModifierGroup", Namespace="http://schemas.datacontract.org/2004/07/Pos.Web.Service.Data")]
    public partial class ModifierGroup : Brink.Api.Settings.SettingsObject
    {
        
        private string DescriptionField;
        
        private string DisplayNameField;
        
        private bool FlowRequiredField;
        
        private byte FreeField;
        
        private System.Collections.Generic.List<Brink.Api.Settings.ModifierGroupItem> ItemsField;
        
        private byte MaximumField;
        
        private byte MinimumField;
        
        private int PanelIdField;
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string Description
        {
            get
            {
                return this.DescriptionField;
            }
            set
            {
                this.DescriptionField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string DisplayName
        {
            get
            {
                return this.DisplayNameField;
            }
            set
            {
                this.DisplayNameField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public bool FlowRequired
        {
            get
            {
                return this.FlowRequiredField;
            }
            set
            {
                this.FlowRequiredField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public byte Free
        {
            get
            {
                return this.FreeField;
            }
            set
            {
                this.FreeField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public System.Collections.Generic.List<Brink.Api.Settings.ModifierGroupItem> Items
        {
            get
            {
                return this.ItemsField;
            }
            set
            {
                this.ItemsField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public byte Maximum
        {
            get
            {
                return this.MaximumField;
            }
            set
            {
                this.MaximumField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public byte Minimum
        {
            get
            {
                return this.MinimumField;
            }
            set
            {
                this.MinimumField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public int PanelId
        {
            get
            {
                return this.PanelIdField;
            }
            set
            {
                this.PanelIdField = value;
            }
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.0.1-preview-30310-0943")]
    [System.Runtime.Serialization.DataContractAttribute(Name="ItemGroupItem", Namespace="http://schemas.datacontract.org/2004/07/Pos.Web.Service.Data")]
    public partial class ItemGroupItem : object
    {
        
        private int ItemIdField;
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public int ItemId
        {
            get
            {
                return this.ItemIdField;
            }
            set
            {
                this.ItemIdField = value;
            }
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.0.1-preview-30310-0943")]
    [System.Runtime.Serialization.DataContractAttribute(Name="DateRange", Namespace="http://schemas.datacontract.org/2004/07/Brink.Foundation")]
    public partial class DateRange : object
    {
        
        private System.DateTime EndField;
        
        private System.DateTime StartField;
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public System.DateTime End
        {
            get
            {
                return this.EndField;
            }
            set
            {
                this.EndField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public System.DateTime Start
        {
            get
            {
                return this.StartField;
            }
            set
            {
                this.StartField = value;
            }
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.0.1-preview-30310-0943")]
    [System.Runtime.Serialization.DataContractAttribute(Name="BrandAllocation", Namespace="http://schemas.datacontract.org/2004/07/Pos")]
    public partial class BrandAllocation : object
    {
        
        private int BrandIdField;
        
        private int IdField;
        
        private decimal WeightField;
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public int BrandId
        {
            get
            {
                return this.BrandIdField;
            }
            set
            {
                this.BrandIdField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public int Id
        {
            get
            {
                return this.IdField;
            }
            set
            {
                this.IdField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public decimal Weight
        {
            get
            {
                return this.WeightField;
            }
            set
            {
                this.WeightField = value;
            }
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.0.1-preview-30310-0943")]
    [System.Runtime.Serialization.DataContractAttribute(Name="ItemCompositeComponent", Namespace="http://schemas.datacontract.org/2004/07/Pos.Web.Service.Data")]
    public partial class ItemCompositeComponent : object
    {
        
        private int IdField;
        
        private System.Nullable<int> ItemGroupIdField;
        
        private System.Collections.Generic.List<Brink.Api.Settings.ItemCompositeComponentItem> ItemsField;
        
        private string NameField;
        
        private bool OverridePriceField;
        
        private System.Nullable<decimal> PriceField;
        
        private bool RollupPriceField;
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public int Id
        {
            get
            {
                return this.IdField;
            }
            set
            {
                this.IdField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public System.Nullable<int> ItemGroupId
        {
            get
            {
                return this.ItemGroupIdField;
            }
            set
            {
                this.ItemGroupIdField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public System.Collections.Generic.List<Brink.Api.Settings.ItemCompositeComponentItem> Items
        {
            get
            {
                return this.ItemsField;
            }
            set
            {
                this.ItemsField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string Name
        {
            get
            {
                return this.NameField;
            }
            set
            {
                this.NameField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public bool OverridePrice
        {
            get
            {
                return this.OverridePriceField;
            }
            set
            {
                this.OverridePriceField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public System.Nullable<decimal> Price
        {
            get
            {
                return this.PriceField;
            }
            set
            {
                this.PriceField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public bool RollupPrice
        {
            get
            {
                return this.RollupPriceField;
            }
            set
            {
                this.RollupPriceField = value;
            }
        }
    }
    
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.0.1-preview-30310-0943")]
    [System.Runtime.Serialization.DataContractAttribute(Name="GiftCardItemType", Namespace="http://schemas.datacontract.org/2004/07/Pos")]
    public enum GiftCardItemType : byte
    {
        
        [System.Runtime.Serialization.EnumMemberAttribute()]
        None = 0,
        
        [System.Runtime.Serialization.EnumMemberAttribute()]
        CashOut = 1,
        
        [System.Runtime.Serialization.EnumMemberAttribute()]
        Issue = 2,
        
        [System.Runtime.Serialization.EnumMemberAttribute()]
        Reload = 3,
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.0.1-preview-30310-0943")]
    [System.Runtime.Serialization.DataContractAttribute(Name="ItemIncludedModifier", Namespace="http://schemas.datacontract.org/2004/07/Pos.Web.Service.Data")]
    public partial class ItemIncludedModifier : object
    {
        
        private bool AutomaticallyAddField;
        
        private bool IsIncludedField;
        
        private int ItemIdField;
        
        private int ModifierGroupIdField;
        
        private short PositionField;
        
        private bool PrintInKitchenField;
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public bool AutomaticallyAdd
        {
            get
            {
                return this.AutomaticallyAddField;
            }
            set
            {
                this.AutomaticallyAddField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public bool IsIncluded
        {
            get
            {
                return this.IsIncludedField;
            }
            set
            {
                this.IsIncludedField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public int ItemId
        {
            get
            {
                return this.ItemIdField;
            }
            set
            {
                this.ItemIdField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public int ModifierGroupId
        {
            get
            {
                return this.ModifierGroupIdField;
            }
            set
            {
                this.ModifierGroupIdField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public short Position
        {
            get
            {
                return this.PositionField;
            }
            set
            {
                this.PositionField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public bool PrintInKitchen
        {
            get
            {
                return this.PrintInKitchenField;
            }
            set
            {
                this.PrintInKitchenField = value;
            }
        }
    }
    
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.0.1-preview-30310-0943")]
    [System.Runtime.Serialization.DataContractAttribute(Name="ItemType", Namespace="http://schemas.datacontract.org/2004/07/Pos")]
    public enum ItemType : byte
    {
        
        [System.Runtime.Serialization.EnumMemberAttribute()]
        Normal = 0,
        
        [System.Runtime.Serialization.EnumMemberAttribute()]
        Pizza = 1,
        
        [System.Runtime.Serialization.EnumMemberAttribute()]
        Chicken = 2,
        
        [System.Runtime.Serialization.EnumMemberAttribute()]
        GiftCard = 3,
        
        [System.Runtime.Serialization.EnumMemberAttribute()]
        Composite = 4,
        
        [System.Runtime.Serialization.EnumMemberAttribute()]
        LoyaltyItem = 5,
        
        [System.Runtime.Serialization.EnumMemberAttribute()]
        Prepaid = 6,
    }
    
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.0.1-preview-30310-0943")]
    [System.Runtime.Serialization.DataContractAttribute(Name="PricePer", Namespace="http://schemas.datacontract.org/2004/07/Pos")]
    public enum PricePer : byte
    {
        
        [System.Runtime.Serialization.EnumMemberAttribute()]
        Ounce = 0,
        
        [System.Runtime.Serialization.EnumMemberAttribute()]
        Pound = 1,
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.0.1-preview-30310-0943")]
    [System.Runtime.Serialization.DataContractAttribute(Name="ItemCompositeComponentItem", Namespace="http://schemas.datacontract.org/2004/07/Pos.Web.Service.Data")]
    public partial class ItemCompositeComponentItem : object
    {
        
        private int IdField;
        
        private bool IsDefaultField;
        
        private int ItemIdField;
        
        private bool OverridePriceField;
        
        private System.Nullable<decimal> PriceField;
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public int Id
        {
            get
            {
                return this.IdField;
            }
            set
            {
                this.IdField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public bool IsDefault
        {
            get
            {
                return this.IsDefaultField;
            }
            set
            {
                this.IsDefaultField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public int ItemId
        {
            get
            {
                return this.ItemIdField;
            }
            set
            {
                this.ItemIdField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public bool OverridePrice
        {
            get
            {
                return this.OverridePriceField;
            }
            set
            {
                this.OverridePriceField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public System.Nullable<decimal> Price
        {
            get
            {
                return this.PriceField;
            }
            set
            {
                this.PriceField = value;
            }
        }
    }
    
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.0.1-preview-30310-0943")]
    [System.Runtime.Serialization.DataContractAttribute(Name="ModCodeIngredientType", Namespace="http://schemas.datacontract.org/2004/07/Pos")]
    public enum ModCodeIngredientType : sbyte
    {
        
        [System.Runtime.Serialization.EnumMemberAttribute()]
        Ingredients = 0,
        
        [System.Runtime.Serialization.EnumMemberAttribute()]
        NonIngredients = 1,
        
        [System.Runtime.Serialization.EnumMemberAttribute()]
        Both = 2,
    }
    
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.0.1-preview-30310-0943")]
    [System.Runtime.Serialization.DataContractAttribute(Name="ModCodeType", Namespace="http://schemas.datacontract.org/2004/07/Pos")]
    public enum ModCodeType : sbyte
    {
        
        [System.Runtime.Serialization.EnumMemberAttribute()]
        Normal = 0,
        
        [System.Runtime.Serialization.EnumMemberAttribute()]
        No = 1,
        
        [System.Runtime.Serialization.EnumMemberAttribute()]
        For = 2,
        
        [System.Runtime.Serialization.EnumMemberAttribute()]
        Sub = 3,
        
        [System.Runtime.Serialization.EnumMemberAttribute()]
        OnSide = 4,
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.0.1-preview-30310-0943")]
    [System.Runtime.Serialization.DataContractAttribute(Name="ModifierGroupItem", Namespace="http://schemas.datacontract.org/2004/07/Pos.Web.Service.Data")]
    public partial class ModifierGroupItem : object
    {
        
        private int ItemIdField;
        
        private short PositionField;
        
        private decimal PriceField;
        
        private byte PriceMethodField;
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public int ItemId
        {
            get
            {
                return this.ItemIdField;
            }
            set
            {
                this.ItemIdField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public short Position
        {
            get
            {
                return this.PositionField;
            }
            set
            {
                this.PositionField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public decimal Price
        {
            get
            {
                return this.PriceField;
            }
            set
            {
                this.PriceField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public byte PriceMethod
        {
            get
            {
                return this.PriceMethodField;
            }
            set
            {
                this.PriceMethodField = value;
            }
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.0.1-preview-30310-0943")]
    [System.Runtime.Serialization.DataContractAttribute(Name="Job", Namespace="http://schemas.datacontract.org/2004/07/Pos.Web.Service.Data")]
    public partial class Job : object
    {
        
        private int IdField;
        
        private string NameField;
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public int Id
        {
            get
            {
                return this.IdField;
            }
            set
            {
                this.IdField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string Name
        {
            get
            {
                return this.NameField;
            }
            set
            {
                this.NameField = value;
            }
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.0.1-preview-30310-0943")]
    [System.Runtime.Serialization.DataContractAttribute(Name="Options", Namespace="http://schemas.datacontract.org/2004/07/Pos.Web.Service.Data")]
    public partial class Options : object
    {
        
        private Brink.Api.Settings.AccountingOptions AccountingField;
        
        private Brink.Api.Settings.LocationOptions LocationField;
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public Brink.Api.Settings.AccountingOptions Accounting
        {
            get
            {
                return this.AccountingField;
            }
            set
            {
                this.AccountingField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public Brink.Api.Settings.LocationOptions Location
        {
            get
            {
                return this.LocationField;
            }
            set
            {
                this.LocationField = value;
            }
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.0.1-preview-30310-0943")]
    [System.Runtime.Serialization.DataContractAttribute(Name="AccountingOptions", Namespace="http://schemas.datacontract.org/2004/07/Pos.Web.Service.Data")]
    public partial class AccountingOptions : object
    {
        
        private Brink.Api.Settings.GLExport GLExportField;
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public Brink.Api.Settings.GLExport GLExport
        {
            get
            {
                return this.GLExportField;
            }
            set
            {
                this.GLExportField = value;
            }
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.0.1-preview-30310-0943")]
    [System.Runtime.Serialization.DataContractAttribute(Name="LocationOptions", Namespace="http://schemas.datacontract.org/2004/07/Pos.Web.Service.Data")]
    public partial class LocationOptions : object
    {
        
        private string Address1Field;
        
        private string Address2Field;
        
        private string CityField;
        
        private string CountryField;
        
        private string FaxField;
        
        private decimal LatitudeField;
        
        private decimal LongitudeField;
        
        private string NameField;
        
        private string PhoneField;
        
        private string StateField;
        
        private Brink.Api.Settings.TimeZone TimeZoneField;
        
        private string ZipField;
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string Address1
        {
            get
            {
                return this.Address1Field;
            }
            set
            {
                this.Address1Field = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string Address2
        {
            get
            {
                return this.Address2Field;
            }
            set
            {
                this.Address2Field = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string City
        {
            get
            {
                return this.CityField;
            }
            set
            {
                this.CityField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string Country
        {
            get
            {
                return this.CountryField;
            }
            set
            {
                this.CountryField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string Fax
        {
            get
            {
                return this.FaxField;
            }
            set
            {
                this.FaxField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public decimal Latitude
        {
            get
            {
                return this.LatitudeField;
            }
            set
            {
                this.LatitudeField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public decimal Longitude
        {
            get
            {
                return this.LongitudeField;
            }
            set
            {
                this.LongitudeField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string Name
        {
            get
            {
                return this.NameField;
            }
            set
            {
                this.NameField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string Phone
        {
            get
            {
                return this.PhoneField;
            }
            set
            {
                this.PhoneField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string State
        {
            get
            {
                return this.StateField;
            }
            set
            {
                this.StateField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public Brink.Api.Settings.TimeZone TimeZone
        {
            get
            {
                return this.TimeZoneField;
            }
            set
            {
                this.TimeZoneField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string Zip
        {
            get
            {
                return this.ZipField;
            }
            set
            {
                this.ZipField = value;
            }
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.0.1-preview-30310-0943")]
    [System.Runtime.Serialization.DataContractAttribute(Name="GLExport", Namespace="http://schemas.datacontract.org/2004/07/Pos.Web.Service.Data")]
    public partial class GLExport : object
    {
        
        private System.Collections.Generic.List<Brink.Api.Settings.GLExportDiscountAccount> DiscountsField;
        
        private string NameField;
        
        private System.Collections.Generic.List<Brink.Api.Settings.GLExportOtherAccount> OthersField;
        
        private System.Collections.Generic.List<Brink.Api.Settings.GLExportPettyAccount> PettyAccountsField;
        
        private System.Collections.Generic.List<Brink.Api.Settings.GLExportPromotionAccount> PromotionsField;
        
        private System.Collections.Generic.List<Brink.Api.Settings.GLExportRevenueCenterAccount> RevenueCentersField;
        
        private System.Collections.Generic.List<Brink.Api.Settings.GLExportSurchargeAccount> SurchargesField;
        
        private System.Collections.Generic.List<Brink.Api.Settings.GLExportTaxAccount> TaxesField;
        
        private System.Collections.Generic.List<Brink.Api.Settings.GLExportTenderAccount> TendersField;
        
        private System.Collections.Generic.List<Brink.Api.Settings.GLExportTenderAccount> TendersForEcommerceField;
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public System.Collections.Generic.List<Brink.Api.Settings.GLExportDiscountAccount> Discounts
        {
            get
            {
                return this.DiscountsField;
            }
            set
            {
                this.DiscountsField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string Name
        {
            get
            {
                return this.NameField;
            }
            set
            {
                this.NameField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public System.Collections.Generic.List<Brink.Api.Settings.GLExportOtherAccount> Others
        {
            get
            {
                return this.OthersField;
            }
            set
            {
                this.OthersField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public System.Collections.Generic.List<Brink.Api.Settings.GLExportPettyAccount> PettyAccounts
        {
            get
            {
                return this.PettyAccountsField;
            }
            set
            {
                this.PettyAccountsField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public System.Collections.Generic.List<Brink.Api.Settings.GLExportPromotionAccount> Promotions
        {
            get
            {
                return this.PromotionsField;
            }
            set
            {
                this.PromotionsField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public System.Collections.Generic.List<Brink.Api.Settings.GLExportRevenueCenterAccount> RevenueCenters
        {
            get
            {
                return this.RevenueCentersField;
            }
            set
            {
                this.RevenueCentersField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public System.Collections.Generic.List<Brink.Api.Settings.GLExportSurchargeAccount> Surcharges
        {
            get
            {
                return this.SurchargesField;
            }
            set
            {
                this.SurchargesField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public System.Collections.Generic.List<Brink.Api.Settings.GLExportTaxAccount> Taxes
        {
            get
            {
                return this.TaxesField;
            }
            set
            {
                this.TaxesField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public System.Collections.Generic.List<Brink.Api.Settings.GLExportTenderAccount> Tenders
        {
            get
            {
                return this.TendersField;
            }
            set
            {
                this.TendersField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public System.Collections.Generic.List<Brink.Api.Settings.GLExportTenderAccount> TendersForEcommerce
        {
            get
            {
                return this.TendersForEcommerceField;
            }
            set
            {
                this.TendersForEcommerceField = value;
            }
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.0.1-preview-30310-0943")]
    [System.Runtime.Serialization.DataContractAttribute(Name="GLExportDiscountAccount", Namespace="http://schemas.datacontract.org/2004/07/Pos.Web.Service.Data")]
    public partial class GLExportDiscountAccount : Brink.Api.Settings.GLExportAccount
    {
        
        private int DiscountIdField;
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public int DiscountId
        {
            get
            {
                return this.DiscountIdField;
            }
            set
            {
                this.DiscountIdField = value;
            }
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.0.1-preview-30310-0943")]
    [System.Runtime.Serialization.DataContractAttribute(Name="GLExportOtherAccount", Namespace="http://schemas.datacontract.org/2004/07/Pos.Web.Service.Data")]
    public partial class GLExportOtherAccount : Brink.Api.Settings.GLExportAccount
    {
        
        private int TypeField;
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public int Type
        {
            get
            {
                return this.TypeField;
            }
            set
            {
                this.TypeField = value;
            }
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.0.1-preview-30310-0943")]
    [System.Runtime.Serialization.DataContractAttribute(Name="GLExportPettyAccount", Namespace="http://schemas.datacontract.org/2004/07/Pos.Web.Service.Data")]
    public partial class GLExportPettyAccount : Brink.Api.Settings.GLExportAccount
    {
        
        private int PettyAccountIdField;
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public int PettyAccountId
        {
            get
            {
                return this.PettyAccountIdField;
            }
            set
            {
                this.PettyAccountIdField = value;
            }
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.0.1-preview-30310-0943")]
    [System.Runtime.Serialization.DataContractAttribute(Name="GLExportPromotionAccount", Namespace="http://schemas.datacontract.org/2004/07/Pos.Web.Service.Data")]
    public partial class GLExportPromotionAccount : Brink.Api.Settings.GLExportAccount
    {
        
        private int PromotionIdField;
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public int PromotionId
        {
            get
            {
                return this.PromotionIdField;
            }
            set
            {
                this.PromotionIdField = value;
            }
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.0.1-preview-30310-0943")]
    [System.Runtime.Serialization.DataContractAttribute(Name="GLExportRevenueCenterAccount", Namespace="http://schemas.datacontract.org/2004/07/Pos.Web.Service.Data")]
    public partial class GLExportRevenueCenterAccount : Brink.Api.Settings.GLExportAccount
    {
        
        private int RevenueCenterIdField;
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public int RevenueCenterId
        {
            get
            {
                return this.RevenueCenterIdField;
            }
            set
            {
                this.RevenueCenterIdField = value;
            }
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.0.1-preview-30310-0943")]
    [System.Runtime.Serialization.DataContractAttribute(Name="GLExportSurchargeAccount", Namespace="http://schemas.datacontract.org/2004/07/Pos.Web.Service.Data")]
    public partial class GLExportSurchargeAccount : Brink.Api.Settings.GLExportAccount
    {
        
        private int SurchargeIdField;
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public int SurchargeId
        {
            get
            {
                return this.SurchargeIdField;
            }
            set
            {
                this.SurchargeIdField = value;
            }
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.0.1-preview-30310-0943")]
    [System.Runtime.Serialization.DataContractAttribute(Name="GLExportTaxAccount", Namespace="http://schemas.datacontract.org/2004/07/Pos.Web.Service.Data")]
    public partial class GLExportTaxAccount : Brink.Api.Settings.GLExportAccount
    {
        
        private int TaxIdField;
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public int TaxId
        {
            get
            {
                return this.TaxIdField;
            }
            set
            {
                this.TaxIdField = value;
            }
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.0.1-preview-30310-0943")]
    [System.Runtime.Serialization.DataContractAttribute(Name="GLExportTenderAccount", Namespace="http://schemas.datacontract.org/2004/07/Pos.Web.Service.Data")]
    public partial class GLExportTenderAccount : Brink.Api.Settings.GLExportAccount
    {
        
        private int TenderIdField;
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public int TenderId
        {
            get
            {
                return this.TenderIdField;
            }
            set
            {
                this.TenderIdField = value;
            }
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.0.1-preview-30310-0943")]
    [System.Runtime.Serialization.DataContractAttribute(Name="GLExportAccount", Namespace="http://schemas.datacontract.org/2004/07/Pos.Web.Service.Data")]
    [System.Runtime.Serialization.KnownTypeAttribute(typeof(Brink.Api.Settings.GLExportOtherAccount))]
    [System.Runtime.Serialization.KnownTypeAttribute(typeof(Brink.Api.Settings.GLExportPettyAccount))]
    [System.Runtime.Serialization.KnownTypeAttribute(typeof(Brink.Api.Settings.GLExportPromotionAccount))]
    [System.Runtime.Serialization.KnownTypeAttribute(typeof(Brink.Api.Settings.GLExportRevenueCenterAccount))]
    [System.Runtime.Serialization.KnownTypeAttribute(typeof(Brink.Api.Settings.GLExportSurchargeAccount))]
    [System.Runtime.Serialization.KnownTypeAttribute(typeof(Brink.Api.Settings.GLExportTaxAccount))]
    [System.Runtime.Serialization.KnownTypeAttribute(typeof(Brink.Api.Settings.GLExportTenderAccount))]
    [System.Runtime.Serialization.KnownTypeAttribute(typeof(Brink.Api.Settings.GLExportDiscountAccount))]
    public partial class GLExportAccount : object
    {
        
        private string ClassField;
        
        private string CreditAccountField;
        
        private string DebitAccountField;
        
        private string MemoField;
        
        private string NameField;
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string Class
        {
            get
            {
                return this.ClassField;
            }
            set
            {
                this.ClassField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string CreditAccount
        {
            get
            {
                return this.CreditAccountField;
            }
            set
            {
                this.CreditAccountField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string DebitAccount
        {
            get
            {
                return this.DebitAccountField;
            }
            set
            {
                this.DebitAccountField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string Memo
        {
            get
            {
                return this.MemoField;
            }
            set
            {
                this.MemoField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string Name
        {
            get
            {
                return this.NameField;
            }
            set
            {
                this.NameField = value;
            }
        }
    }
    
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.0.1-preview-30310-0943")]
    [System.Runtime.Serialization.DataContractAttribute(Name="TimeZone", Namespace="http://schemas.datacontract.org/2004/07/Pos.Web.Service.Data")]
    public enum TimeZone : int
    {
        
        [System.Runtime.Serialization.EnumMemberAttribute()]
        Hawaii = 1,
        
        [System.Runtime.Serialization.EnumMemberAttribute()]
        Alaska = 2,
        
        [System.Runtime.Serialization.EnumMemberAttribute()]
        Pacific = 3,
        
        [System.Runtime.Serialization.EnumMemberAttribute()]
        Arizona = 4,
        
        [System.Runtime.Serialization.EnumMemberAttribute()]
        Mountain = 5,
        
        [System.Runtime.Serialization.EnumMemberAttribute()]
        Central = 6,
        
        [System.Runtime.Serialization.EnumMemberAttribute()]
        Eastern = 7,
        
        [System.Runtime.Serialization.EnumMemberAttribute()]
        GeorgetownLaPazManausSanJuan = 8,
        
        [System.Runtime.Serialization.EnumMemberAttribute()]
        CanadaCentral = 9,
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.0.1-preview-30310-0943")]
    [System.Runtime.Serialization.DataContractAttribute(Name="PettyAccount", Namespace="http://schemas.datacontract.org/2004/07/Pos.Web.Service.Data")]
    public partial class PettyAccount : object
    {
        
        private Brink.Api.Settings.PettyAccountType AccountTypeField;
        
        private int IdField;
        
        private string NameField;
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public Brink.Api.Settings.PettyAccountType AccountType
        {
            get
            {
                return this.AccountTypeField;
            }
            set
            {
                this.AccountTypeField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public int Id
        {
            get
            {
                return this.IdField;
            }
            set
            {
                this.IdField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string Name
        {
            get
            {
                return this.NameField;
            }
            set
            {
                this.NameField = value;
            }
        }
    }
    
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.0.1-preview-30310-0943")]
    [System.Runtime.Serialization.DataContractAttribute(Name="PettyAccountType", Namespace="http://schemas.datacontract.org/2004/07/Pos")]
    public enum PettyAccountType : byte
    {
        
        [System.Runtime.Serialization.EnumMemberAttribute()]
        CashIn = 0,
        
        [System.Runtime.Serialization.EnumMemberAttribute()]
        CashOut = 1,
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.0.1-preview-30310-0943")]
    [System.Runtime.Serialization.DataContractAttribute(Name="Promotion", Namespace="http://schemas.datacontract.org/2004/07/Pos.Web.Service.Data")]
    public partial class Promotion : object
    {
        
        private bool ActiveField;
        
        private int IdField;
        
        private string NameField;
        
        private Brink.Api.Settings.PromotionType TypeField;
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public bool Active
        {
            get
            {
                return this.ActiveField;
            }
            set
            {
                this.ActiveField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public int Id
        {
            get
            {
                return this.IdField;
            }
            set
            {
                this.IdField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string Name
        {
            get
            {
                return this.NameField;
            }
            set
            {
                this.NameField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public Brink.Api.Settings.PromotionType Type
        {
            get
            {
                return this.TypeField;
            }
            set
            {
                this.TypeField = value;
            }
        }
    }
    
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.0.1-preview-30310-0943")]
    [System.Runtime.Serialization.DataContractAttribute(Name="PromotionType", Namespace="http://schemas.datacontract.org/2004/07/Pos")]
    public enum PromotionType : sbyte
    {
        
        [System.Runtime.Serialization.EnumMemberAttribute()]
        None = 0,
        
        [System.Runtime.Serialization.EnumMemberAttribute()]
        Bogo = 1,
        
        [System.Runtime.Serialization.EnumMemberAttribute()]
        OrderReduction = 2,
        
        [System.Runtime.Serialization.EnumMemberAttribute()]
        Coupon = 3,
        
        [System.Runtime.Serialization.EnumMemberAttribute()]
        GiftCard = 4,
        
        [System.Runtime.Serialization.EnumMemberAttribute()]
        Combo = 5,
        
        [System.Runtime.Serialization.EnumMemberAttribute()]
        Max = 6,
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.0.1-preview-30310-0943")]
    [System.Runtime.Serialization.DataContractAttribute(Name="Register", Namespace="http://schemas.datacontract.org/2004/07/Pos.Web.Service.Data")]
    public partial class Register : object
    {
        
        private int IdField;
        
        private string NameField;
        
        private int NumberField;
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public int Id
        {
            get
            {
                return this.IdField;
            }
            set
            {
                this.IdField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string Name
        {
            get
            {
                return this.NameField;
            }
            set
            {
                this.NameField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public int Number
        {
            get
            {
                return this.NumberField;
            }
            set
            {
                this.NumberField = value;
            }
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.0.1-preview-30310-0943")]
    [System.Runtime.Serialization.DataContractAttribute(Name="RevenueCenter", Namespace="http://schemas.datacontract.org/2004/07/Pos.Web.Service.Data")]
    public partial class RevenueCenter : object
    {
        
        private bool ActiveField;
        
        private int IdField;
        
        private string NameField;
        
        private System.Collections.Generic.List<int> TaxIdsField;
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public bool Active
        {
            get
            {
                return this.ActiveField;
            }
            set
            {
                this.ActiveField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public int Id
        {
            get
            {
                return this.IdField;
            }
            set
            {
                this.IdField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string Name
        {
            get
            {
                return this.NameField;
            }
            set
            {
                this.NameField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public System.Collections.Generic.List<int> TaxIds
        {
            get
            {
                return this.TaxIdsField;
            }
            set
            {
                this.TaxIdsField = value;
            }
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.0.1-preview-30310-0943")]
    [System.Runtime.Serialization.DataContractAttribute(Name="Section", Namespace="http://schemas.datacontract.org/2004/07/Pos.Web.Service.Data")]
    public partial class Section : object
    {
        
        private int IdField;
        
        private string NameField;
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public int Id
        {
            get
            {
                return this.IdField;
            }
            set
            {
                this.IdField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string Name
        {
            get
            {
                return this.NameField;
            }
            set
            {
                this.NameField = value;
            }
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.0.1-preview-30310-0943")]
    [System.Runtime.Serialization.DataContractAttribute(Name="Surcharge", Namespace="http://schemas.datacontract.org/2004/07/Pos.Web.Service.Data")]
    public partial class Surcharge : object
    {
        
        private decimal AmountField;
        
        private bool AutomaticallyApplyField;
        
        private int DaysField;
        
        private System.DateTime EndDateField;
        
        private System.DateTime EndTimeField;
        
        private bool EnforceDateRangesField;
        
        private bool EnforceDaysField;
        
        private bool EnforcePartySizeField;
        
        private bool EnforceTimeRangesField;
        
        private int IdField;
        
        private bool LimitDestinationsField;
        
        private bool LimitSectionsField;
        
        private int MaximumPartySizeField;
        
        private int MethodField;
        
        private int MinimumPartySizeField;
        
        private string NameField;
        
        private bool PromptField;
        
        private System.DateTime StartDateField;
        
        private System.DateTime StartTimeField;
        
        private System.Collections.Generic.List<int> TaxIdsField;
        
        private int TerminalTypesField;
        
        private int TypeField;
        
        private System.Collections.Generic.List<int> ValidDestinationIdsField;
        
        private System.Collections.Generic.List<int> ValidSectionIdsField;
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public decimal Amount
        {
            get
            {
                return this.AmountField;
            }
            set
            {
                this.AmountField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public bool AutomaticallyApply
        {
            get
            {
                return this.AutomaticallyApplyField;
            }
            set
            {
                this.AutomaticallyApplyField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public int Days
        {
            get
            {
                return this.DaysField;
            }
            set
            {
                this.DaysField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public System.DateTime EndDate
        {
            get
            {
                return this.EndDateField;
            }
            set
            {
                this.EndDateField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public System.DateTime EndTime
        {
            get
            {
                return this.EndTimeField;
            }
            set
            {
                this.EndTimeField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public bool EnforceDateRanges
        {
            get
            {
                return this.EnforceDateRangesField;
            }
            set
            {
                this.EnforceDateRangesField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public bool EnforceDays
        {
            get
            {
                return this.EnforceDaysField;
            }
            set
            {
                this.EnforceDaysField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public bool EnforcePartySize
        {
            get
            {
                return this.EnforcePartySizeField;
            }
            set
            {
                this.EnforcePartySizeField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public bool EnforceTimeRanges
        {
            get
            {
                return this.EnforceTimeRangesField;
            }
            set
            {
                this.EnforceTimeRangesField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public int Id
        {
            get
            {
                return this.IdField;
            }
            set
            {
                this.IdField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public bool LimitDestinations
        {
            get
            {
                return this.LimitDestinationsField;
            }
            set
            {
                this.LimitDestinationsField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public bool LimitSections
        {
            get
            {
                return this.LimitSectionsField;
            }
            set
            {
                this.LimitSectionsField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public int MaximumPartySize
        {
            get
            {
                return this.MaximumPartySizeField;
            }
            set
            {
                this.MaximumPartySizeField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public int Method
        {
            get
            {
                return this.MethodField;
            }
            set
            {
                this.MethodField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public int MinimumPartySize
        {
            get
            {
                return this.MinimumPartySizeField;
            }
            set
            {
                this.MinimumPartySizeField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string Name
        {
            get
            {
                return this.NameField;
            }
            set
            {
                this.NameField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public bool Prompt
        {
            get
            {
                return this.PromptField;
            }
            set
            {
                this.PromptField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public System.DateTime StartDate
        {
            get
            {
                return this.StartDateField;
            }
            set
            {
                this.StartDateField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public System.DateTime StartTime
        {
            get
            {
                return this.StartTimeField;
            }
            set
            {
                this.StartTimeField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public System.Collections.Generic.List<int> TaxIds
        {
            get
            {
                return this.TaxIdsField;
            }
            set
            {
                this.TaxIdsField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public int TerminalTypes
        {
            get
            {
                return this.TerminalTypesField;
            }
            set
            {
                this.TerminalTypesField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public int Type
        {
            get
            {
                return this.TypeField;
            }
            set
            {
                this.TypeField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public System.Collections.Generic.List<int> ValidDestinationIds
        {
            get
            {
                return this.ValidDestinationIdsField;
            }
            set
            {
                this.ValidDestinationIdsField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public System.Collections.Generic.List<int> ValidSectionIds
        {
            get
            {
                return this.ValidSectionIdsField;
            }
            set
            {
                this.ValidSectionIdsField = value;
            }
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.0.1-preview-30310-0943")]
    [System.Runtime.Serialization.DataContractAttribute(Name="Tax", Namespace="http://schemas.datacontract.org/2004/07/Pos.Web.Service.Data")]
    public partial class Tax : object
    {
        
        private decimal AmountField;
        
        private int IdField;
        
        private string NameField;
        
        private int TypeField;
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public decimal Amount
        {
            get
            {
                return this.AmountField;
            }
            set
            {
                this.AmountField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public int Id
        {
            get
            {
                return this.IdField;
            }
            set
            {
                this.IdField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string Name
        {
            get
            {
                return this.NameField;
            }
            set
            {
                this.NameField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public int Type
        {
            get
            {
                return this.TypeField;
            }
            set
            {
                this.TypeField = value;
            }
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.0.1-preview-30310-0943")]
    [System.Runtime.Serialization.DataContractAttribute(Name="Tender", Namespace="http://schemas.datacontract.org/2004/07/Pos.Web.Service.Data")]
    public partial class Tender : object
    {
        
        private bool ActiveField;
        
        private bool AllowChangeField;
        
        private bool AllowTipsField;
        
        private Brink.Api.Settings.CardType CardTypeField;
        
        private decimal DefaultAmountField;
        
        private int IdField;
        
        private decimal MaxTipPercentField;
        
        private sbyte MaximumAccountDigitsField;
        
        private sbyte MinimumAccountDigitsField;
        
        private string NameField;
        
        private bool OpenDrawerField;
        
        private Brink.Api.Settings.ReconciliationMethod ReconciliationMethodField;
        
        private bool RequiresAuthorizationField;
        
        private bool SignatureCaptureField;
        
        private Brink.Api.Settings.TenderType TenderTypeField;
        
        private decimal VarianceAmountField;
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public bool Active
        {
            get
            {
                return this.ActiveField;
            }
            set
            {
                this.ActiveField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public bool AllowChange
        {
            get
            {
                return this.AllowChangeField;
            }
            set
            {
                this.AllowChangeField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public bool AllowTips
        {
            get
            {
                return this.AllowTipsField;
            }
            set
            {
                this.AllowTipsField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public Brink.Api.Settings.CardType CardType
        {
            get
            {
                return this.CardTypeField;
            }
            set
            {
                this.CardTypeField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public decimal DefaultAmount
        {
            get
            {
                return this.DefaultAmountField;
            }
            set
            {
                this.DefaultAmountField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public int Id
        {
            get
            {
                return this.IdField;
            }
            set
            {
                this.IdField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public decimal MaxTipPercent
        {
            get
            {
                return this.MaxTipPercentField;
            }
            set
            {
                this.MaxTipPercentField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public sbyte MaximumAccountDigits
        {
            get
            {
                return this.MaximumAccountDigitsField;
            }
            set
            {
                this.MaximumAccountDigitsField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public sbyte MinimumAccountDigits
        {
            get
            {
                return this.MinimumAccountDigitsField;
            }
            set
            {
                this.MinimumAccountDigitsField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string Name
        {
            get
            {
                return this.NameField;
            }
            set
            {
                this.NameField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public bool OpenDrawer
        {
            get
            {
                return this.OpenDrawerField;
            }
            set
            {
                this.OpenDrawerField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public Brink.Api.Settings.ReconciliationMethod ReconciliationMethod
        {
            get
            {
                return this.ReconciliationMethodField;
            }
            set
            {
                this.ReconciliationMethodField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public bool RequiresAuthorization
        {
            get
            {
                return this.RequiresAuthorizationField;
            }
            set
            {
                this.RequiresAuthorizationField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public bool SignatureCapture
        {
            get
            {
                return this.SignatureCaptureField;
            }
            set
            {
                this.SignatureCaptureField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public Brink.Api.Settings.TenderType TenderType
        {
            get
            {
                return this.TenderTypeField;
            }
            set
            {
                this.TenderTypeField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public decimal VarianceAmount
        {
            get
            {
                return this.VarianceAmountField;
            }
            set
            {
                this.VarianceAmountField = value;
            }
        }
    }
    
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.0.1-preview-30310-0943")]
    [System.Runtime.Serialization.DataContractAttribute(Name="CardType", Namespace="http://schemas.datacontract.org/2004/07/Brink.Credit")]
    public enum CardType : byte
    {
        
        [System.Runtime.Serialization.EnumMemberAttribute()]
        None = 0,
        
        [System.Runtime.Serialization.EnumMemberAttribute()]
        MasterCard = 1,
        
        [System.Runtime.Serialization.EnumMemberAttribute()]
        Visa = 2,
        
        [System.Runtime.Serialization.EnumMemberAttribute()]
        AmericanExpress = 3,
        
        [System.Runtime.Serialization.EnumMemberAttribute()]
        Diners = 4,
        
        [System.Runtime.Serialization.EnumMemberAttribute()]
        Discover = 5,
        
        [System.Runtime.Serialization.EnumMemberAttribute()]
        enRoute = 6,
        
        [System.Runtime.Serialization.EnumMemberAttribute()]
        JCB = 7,
        
        [System.Runtime.Serialization.EnumMemberAttribute()]
        Private = 8,
    }
    
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.0.1-preview-30310-0943")]
    [System.Runtime.Serialization.DataContractAttribute(Name="ReconciliationMethod", Namespace="http://schemas.datacontract.org/2004/07/Pos")]
    public enum ReconciliationMethod : sbyte
    {
        
        [System.Runtime.Serialization.EnumMemberAttribute()]
        Details = 0,
        
        [System.Runtime.Serialization.EnumMemberAttribute()]
        Totals = 1,
    }
    
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.0.1-preview-30310-0943")]
    [System.Runtime.Serialization.DataContractAttribute(Name="TenderType", Namespace="http://schemas.datacontract.org/2004/07/Pos")]
    public enum TenderType : byte
    {
        
        [System.Runtime.Serialization.EnumMemberAttribute()]
        None = 0,
        
        [System.Runtime.Serialization.EnumMemberAttribute()]
        Cash = 1,
        
        [System.Runtime.Serialization.EnumMemberAttribute()]
        CreditCard = 2,
        
        [System.Runtime.Serialization.EnumMemberAttribute()]
        GiftCard = 3,
        
        [System.Runtime.Serialization.EnumMemberAttribute()]
        GiftCertificate = 4,
        
        [System.Runtime.Serialization.EnumMemberAttribute()]
        Check = 5,
        
        [System.Runtime.Serialization.EnumMemberAttribute()]
        HouseAccount = 6,
        
        [System.Runtime.Serialization.EnumMemberAttribute()]
        External = 7,
        
        [System.Runtime.Serialization.EnumMemberAttribute()]
        Debit = 8,
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.0.1-preview-30310-0943")]
    [System.Runtime.Serialization.DataContractAttribute(Name="VoidReason", Namespace="http://schemas.datacontract.org/2004/07/Pos.Web.Service.Data")]
    public partial class VoidReason : object
    {
        
        private int IdField;
        
        private string NameField;
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public int Id
        {
            get
            {
                return this.IdField;
            }
            set
            {
                this.IdField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string Name
        {
            get
            {
                return this.NameField;
            }
            set
            {
                this.NameField = value;
            }
        }
    }
    
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.0.1-preview-30310-0943")]
    [System.ServiceModel.ServiceContractAttribute(ConfigurationName="Brink.Api.Settings.ISettingsWebService")]
    public interface ISettingsWebService
    {
        
        [System.ServiceModel.OperationContractAttribute(Action="http://tempuri.org/ISettingsWebService/GetCashDrawers", ReplyAction="http://tempuri.org/ISettingsWebService/GetCashDrawersResponse")]
        [System.ServiceModel.FaultContractAttribute(typeof(Brink.Api.Settings.AccessFault), Action="http://tempuri.org/ISettingsWebService/GetCashDrawersAccessFaultFault", Name="AccessFault", Namespace="http://schemas.datacontract.org/2004/07/Pos.Web.Service.Faults")]
        [System.ServiceModel.FaultContractAttribute(typeof(Brink.Api.Settings.LocationFault), Action="http://tempuri.org/ISettingsWebService/GetCashDrawersLocationFaultFault", Name="LocationFault", Namespace="http://schemas.datacontract.org/2004/07/Pos.Web.Service.Faults")]
        System.Threading.Tasks.Task<System.Collections.Generic.List<Brink.Api.Settings.CashDrawer>> GetCashDrawersAsync(string accessToken, string locationToken);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://tempuri.org/ISettingsWebService/GetDayParts", ReplyAction="http://tempuri.org/ISettingsWebService/GetDayPartsResponse")]
        [System.ServiceModel.FaultContractAttribute(typeof(Brink.Api.Settings.AccessFault), Action="http://tempuri.org/ISettingsWebService/GetDayPartsAccessFaultFault", Name="AccessFault", Namespace="http://schemas.datacontract.org/2004/07/Pos.Web.Service.Faults")]
        [System.ServiceModel.FaultContractAttribute(typeof(Brink.Api.Settings.LocationFault), Action="http://tempuri.org/ISettingsWebService/GetDayPartsLocationFaultFault", Name="LocationFault", Namespace="http://schemas.datacontract.org/2004/07/Pos.Web.Service.Faults")]
        System.Threading.Tasks.Task<System.Collections.Generic.List<Brink.Api.Settings.DayPart>> GetDayPartsAsync(string accessToken, string locationToken);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://tempuri.org/ISettingsWebService/GetDestinations", ReplyAction="http://tempuri.org/ISettingsWebService/GetDestinationsResponse")]
        [System.ServiceModel.FaultContractAttribute(typeof(Brink.Api.Settings.AccessFault), Action="http://tempuri.org/ISettingsWebService/GetDestinationsAccessFaultFault", Name="AccessFault", Namespace="http://schemas.datacontract.org/2004/07/Pos.Web.Service.Faults")]
        [System.ServiceModel.FaultContractAttribute(typeof(Brink.Api.Settings.LocationFault), Action="http://tempuri.org/ISettingsWebService/GetDestinationsLocationFaultFault", Name="LocationFault", Namespace="http://schemas.datacontract.org/2004/07/Pos.Web.Service.Faults")]
        System.Threading.Tasks.Task<System.Collections.Generic.List<Brink.Api.Settings.Destination>> GetDestinationsAsync(string accessToken, string locationToken);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://tempuri.org/ISettingsWebService/GetDiscounts", ReplyAction="http://tempuri.org/ISettingsWebService/GetDiscountsResponse")]
        [System.ServiceModel.FaultContractAttribute(typeof(Brink.Api.Settings.AccessFault), Action="http://tempuri.org/ISettingsWebService/GetDiscountsAccessFaultFault", Name="AccessFault", Namespace="http://schemas.datacontract.org/2004/07/Pos.Web.Service.Faults")]
        [System.ServiceModel.FaultContractAttribute(typeof(Brink.Api.Settings.LocationFault), Action="http://tempuri.org/ISettingsWebService/GetDiscountsLocationFaultFault", Name="LocationFault", Namespace="http://schemas.datacontract.org/2004/07/Pos.Web.Service.Faults")]
        System.Threading.Tasks.Task<System.Collections.Generic.List<Brink.Api.Settings.Discount>> GetDiscountsAsync(string accessToken, string locationToken);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://tempuri.org/ISettingsWebService/GetEmployees", ReplyAction="http://tempuri.org/ISettingsWebService/GetEmployeesResponse")]
        [System.ServiceModel.FaultContractAttribute(typeof(Brink.Api.Settings.AccessFault), Action="http://tempuri.org/ISettingsWebService/GetEmployeesAccessFaultFault", Name="AccessFault", Namespace="http://schemas.datacontract.org/2004/07/Pos.Web.Service.Faults")]
        [System.ServiceModel.FaultContractAttribute(typeof(Brink.Api.Settings.LocationFault), Action="http://tempuri.org/ISettingsWebService/GetEmployeesLocationFaultFault", Name="LocationFault", Namespace="http://schemas.datacontract.org/2004/07/Pos.Web.Service.Faults")]
        System.Threading.Tasks.Task<System.Collections.Generic.List<Brink.Api.Settings.Employee>> GetEmployeesAsync(string accessToken, string locationToken);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://tempuri.org/ISettingsWebService/GetItemGroups", ReplyAction="http://tempuri.org/ISettingsWebService/GetItemGroupsResponse")]
        [System.ServiceModel.FaultContractAttribute(typeof(Brink.Api.Settings.AccessFault), Action="http://tempuri.org/ISettingsWebService/GetItemGroupsAccessFaultFault", Name="AccessFault", Namespace="http://schemas.datacontract.org/2004/07/Pos.Web.Service.Faults")]
        [System.ServiceModel.FaultContractAttribute(typeof(Brink.Api.Settings.LocationFault), Action="http://tempuri.org/ISettingsWebService/GetItemGroupsLocationFaultFault", Name="LocationFault", Namespace="http://schemas.datacontract.org/2004/07/Pos.Web.Service.Faults")]
        System.Threading.Tasks.Task<System.Collections.Generic.List<Brink.Api.Settings.ItemGroup>> GetItemGroupsAsync(string accessToken, string locationToken);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://tempuri.org/ISettingsWebService/GetItems", ReplyAction="http://tempuri.org/ISettingsWebService/GetItemsResponse")]
        [System.ServiceModel.FaultContractAttribute(typeof(Brink.Api.Settings.AccessFault), Action="http://tempuri.org/ISettingsWebService/GetItemsAccessFaultFault", Name="AccessFault", Namespace="http://schemas.datacontract.org/2004/07/Pos.Web.Service.Faults")]
        [System.ServiceModel.FaultContractAttribute(typeof(Brink.Api.Settings.LocationFault), Action="http://tempuri.org/ISettingsWebService/GetItemsLocationFaultFault", Name="LocationFault", Namespace="http://schemas.datacontract.org/2004/07/Pos.Web.Service.Faults")]
        System.Threading.Tasks.Task<System.Collections.Generic.List<Brink.Api.Settings.Item>> GetItemsAsync(string accessToken, string locationToken);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://tempuri.org/ISettingsWebService/GetJobs", ReplyAction="http://tempuri.org/ISettingsWebService/GetJobsResponse")]
        [System.ServiceModel.FaultContractAttribute(typeof(Brink.Api.Settings.AccessFault), Action="http://tempuri.org/ISettingsWebService/GetJobsAccessFaultFault", Name="AccessFault", Namespace="http://schemas.datacontract.org/2004/07/Pos.Web.Service.Faults")]
        [System.ServiceModel.FaultContractAttribute(typeof(Brink.Api.Settings.LocationFault), Action="http://tempuri.org/ISettingsWebService/GetJobsLocationFaultFault", Name="LocationFault", Namespace="http://schemas.datacontract.org/2004/07/Pos.Web.Service.Faults")]
        System.Threading.Tasks.Task<System.Collections.Generic.List<Brink.Api.Settings.Job>> GetJobsAsync(string accessToken, string locationToken);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://tempuri.org/ISettingsWebService/GetLoyaltyRewards", ReplyAction="http://tempuri.org/ISettingsWebService/GetLoyaltyRewardsResponse")]
        [System.ServiceModel.FaultContractAttribute(typeof(Brink.Api.Settings.AccessFault), Action="http://tempuri.org/ISettingsWebService/GetLoyaltyRewardsAccessFaultFault", Name="AccessFault", Namespace="http://schemas.datacontract.org/2004/07/Pos.Web.Service.Faults")]
        [System.ServiceModel.FaultContractAttribute(typeof(Brink.Api.Settings.LocationFault), Action="http://tempuri.org/ISettingsWebService/GetLoyaltyRewardsLocationFaultFault", Name="LocationFault", Namespace="http://schemas.datacontract.org/2004/07/Pos.Web.Service.Faults")]
        System.Threading.Tasks.Task<System.Collections.Generic.List<Brink.Api.Settings.LoyaltyReward>> GetLoyaltyRewardsAsync(string accessToken, string locationToken);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://tempuri.org/ISettingsWebService/GetModifierCodes", ReplyAction="http://tempuri.org/ISettingsWebService/GetModifierCodesResponse")]
        [System.ServiceModel.FaultContractAttribute(typeof(Brink.Api.Settings.AccessFault), Action="http://tempuri.org/ISettingsWebService/GetModifierCodesAccessFaultFault", Name="AccessFault", Namespace="http://schemas.datacontract.org/2004/07/Pos.Web.Service.Faults")]
        [System.ServiceModel.FaultContractAttribute(typeof(Brink.Api.Settings.LocationFault), Action="http://tempuri.org/ISettingsWebService/GetModifierCodesLocationFaultFault", Name="LocationFault", Namespace="http://schemas.datacontract.org/2004/07/Pos.Web.Service.Faults")]
        System.Threading.Tasks.Task<System.Collections.Generic.List<Brink.Api.Settings.ModifierCode>> GetModifierCodesAsync(string accessToken, string locationToken);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://tempuri.org/ISettingsWebService/GetModifierGroups", ReplyAction="http://tempuri.org/ISettingsWebService/GetModifierGroupsResponse")]
        [System.ServiceModel.FaultContractAttribute(typeof(Brink.Api.Settings.AccessFault), Action="http://tempuri.org/ISettingsWebService/GetModifierGroupsAccessFaultFault", Name="AccessFault", Namespace="http://schemas.datacontract.org/2004/07/Pos.Web.Service.Faults")]
        [System.ServiceModel.FaultContractAttribute(typeof(Brink.Api.Settings.LocationFault), Action="http://tempuri.org/ISettingsWebService/GetModifierGroupsLocationFaultFault", Name="LocationFault", Namespace="http://schemas.datacontract.org/2004/07/Pos.Web.Service.Faults")]
        System.Threading.Tasks.Task<System.Collections.Generic.List<Brink.Api.Settings.ModifierGroup>> GetModifierGroupsAsync(string accessToken, string locationToken);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://tempuri.org/ISettingsWebService/GetOptions", ReplyAction="http://tempuri.org/ISettingsWebService/GetOptionsResponse")]
        [System.ServiceModel.FaultContractAttribute(typeof(Brink.Api.Settings.AccessFault), Action="http://tempuri.org/ISettingsWebService/GetOptionsAccessFaultFault", Name="AccessFault", Namespace="http://schemas.datacontract.org/2004/07/Pos.Web.Service.Faults")]
        [System.ServiceModel.FaultContractAttribute(typeof(Brink.Api.Settings.LocationFault), Action="http://tempuri.org/ISettingsWebService/GetOptionsLocationFaultFault", Name="LocationFault", Namespace="http://schemas.datacontract.org/2004/07/Pos.Web.Service.Faults")]
        System.Threading.Tasks.Task<Brink.Api.Settings.Options> GetOptionsAsync(string accessToken, string locationToken);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://tempuri.org/ISettingsWebService/GetPettyAccounts", ReplyAction="http://tempuri.org/ISettingsWebService/GetPettyAccountsResponse")]
        [System.ServiceModel.FaultContractAttribute(typeof(Brink.Api.Settings.AccessFault), Action="http://tempuri.org/ISettingsWebService/GetPettyAccountsAccessFaultFault", Name="AccessFault", Namespace="http://schemas.datacontract.org/2004/07/Pos.Web.Service.Faults")]
        [System.ServiceModel.FaultContractAttribute(typeof(Brink.Api.Settings.LocationFault), Action="http://tempuri.org/ISettingsWebService/GetPettyAccountsLocationFaultFault", Name="LocationFault", Namespace="http://schemas.datacontract.org/2004/07/Pos.Web.Service.Faults")]
        System.Threading.Tasks.Task<System.Collections.Generic.List<Brink.Api.Settings.PettyAccount>> GetPettyAccountsAsync(string accessToken, string locationToken);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://tempuri.org/ISettingsWebService/GetPromotions", ReplyAction="http://tempuri.org/ISettingsWebService/GetPromotionsResponse")]
        [System.ServiceModel.FaultContractAttribute(typeof(Brink.Api.Settings.AccessFault), Action="http://tempuri.org/ISettingsWebService/GetPromotionsAccessFaultFault", Name="AccessFault", Namespace="http://schemas.datacontract.org/2004/07/Pos.Web.Service.Faults")]
        [System.ServiceModel.FaultContractAttribute(typeof(Brink.Api.Settings.LocationFault), Action="http://tempuri.org/ISettingsWebService/GetPromotionsLocationFaultFault", Name="LocationFault", Namespace="http://schemas.datacontract.org/2004/07/Pos.Web.Service.Faults")]
        System.Threading.Tasks.Task<System.Collections.Generic.List<Brink.Api.Settings.Promotion>> GetPromotionsAsync(string accessToken, string locationToken);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://tempuri.org/ISettingsWebService/GetRegisters", ReplyAction="http://tempuri.org/ISettingsWebService/GetRegistersResponse")]
        [System.ServiceModel.FaultContractAttribute(typeof(Brink.Api.Settings.AccessFault), Action="http://tempuri.org/ISettingsWebService/GetRegistersAccessFaultFault", Name="AccessFault", Namespace="http://schemas.datacontract.org/2004/07/Pos.Web.Service.Faults")]
        [System.ServiceModel.FaultContractAttribute(typeof(Brink.Api.Settings.LocationFault), Action="http://tempuri.org/ISettingsWebService/GetRegistersLocationFaultFault", Name="LocationFault", Namespace="http://schemas.datacontract.org/2004/07/Pos.Web.Service.Faults")]
        System.Threading.Tasks.Task<System.Collections.Generic.List<Brink.Api.Settings.Register>> GetRegistersAsync(string accessToken, string locationToken);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://tempuri.org/ISettingsWebService/GetRevenueCenters", ReplyAction="http://tempuri.org/ISettingsWebService/GetRevenueCentersResponse")]
        [System.ServiceModel.FaultContractAttribute(typeof(Brink.Api.Settings.AccessFault), Action="http://tempuri.org/ISettingsWebService/GetRevenueCentersAccessFaultFault", Name="AccessFault", Namespace="http://schemas.datacontract.org/2004/07/Pos.Web.Service.Faults")]
        [System.ServiceModel.FaultContractAttribute(typeof(Brink.Api.Settings.LocationFault), Action="http://tempuri.org/ISettingsWebService/GetRevenueCentersLocationFaultFault", Name="LocationFault", Namespace="http://schemas.datacontract.org/2004/07/Pos.Web.Service.Faults")]
        System.Threading.Tasks.Task<System.Collections.Generic.List<Brink.Api.Settings.RevenueCenter>> GetRevenueCentersAsync(string accessToken, string locationToken);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://tempuri.org/ISettingsWebService/GetSections", ReplyAction="http://tempuri.org/ISettingsWebService/GetSectionsResponse")]
        [System.ServiceModel.FaultContractAttribute(typeof(Brink.Api.Settings.AccessFault), Action="http://tempuri.org/ISettingsWebService/GetSectionsAccessFaultFault", Name="AccessFault", Namespace="http://schemas.datacontract.org/2004/07/Pos.Web.Service.Faults")]
        [System.ServiceModel.FaultContractAttribute(typeof(Brink.Api.Settings.LocationFault), Action="http://tempuri.org/ISettingsWebService/GetSectionsLocationFaultFault", Name="LocationFault", Namespace="http://schemas.datacontract.org/2004/07/Pos.Web.Service.Faults")]
        System.Threading.Tasks.Task<System.Collections.Generic.List<Brink.Api.Settings.Section>> GetSectionsAsync(string accessToken, string locationToken);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://tempuri.org/ISettingsWebService/GetSurcharges", ReplyAction="http://tempuri.org/ISettingsWebService/GetSurchargesResponse")]
        [System.ServiceModel.FaultContractAttribute(typeof(Brink.Api.Settings.AccessFault), Action="http://tempuri.org/ISettingsWebService/GetSurchargesAccessFaultFault", Name="AccessFault", Namespace="http://schemas.datacontract.org/2004/07/Pos.Web.Service.Faults")]
        [System.ServiceModel.FaultContractAttribute(typeof(Brink.Api.Settings.LocationFault), Action="http://tempuri.org/ISettingsWebService/GetSurchargesLocationFaultFault", Name="LocationFault", Namespace="http://schemas.datacontract.org/2004/07/Pos.Web.Service.Faults")]
        System.Threading.Tasks.Task<System.Collections.Generic.List<Brink.Api.Settings.Surcharge>> GetSurchargesAsync(string accessToken, string locationToken);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://tempuri.org/ISettingsWebService/GetTaxes", ReplyAction="http://tempuri.org/ISettingsWebService/GetTaxesResponse")]
        [System.ServiceModel.FaultContractAttribute(typeof(Brink.Api.Settings.AccessFault), Action="http://tempuri.org/ISettingsWebService/GetTaxesAccessFaultFault", Name="AccessFault", Namespace="http://schemas.datacontract.org/2004/07/Pos.Web.Service.Faults")]
        [System.ServiceModel.FaultContractAttribute(typeof(Brink.Api.Settings.LocationFault), Action="http://tempuri.org/ISettingsWebService/GetTaxesLocationFaultFault", Name="LocationFault", Namespace="http://schemas.datacontract.org/2004/07/Pos.Web.Service.Faults")]
        System.Threading.Tasks.Task<System.Collections.Generic.List<Brink.Api.Settings.Tax>> GetTaxesAsync(string accessToken, string locationToken);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://tempuri.org/ISettingsWebService/GetTenders", ReplyAction="http://tempuri.org/ISettingsWebService/GetTendersResponse")]
        [System.ServiceModel.FaultContractAttribute(typeof(Brink.Api.Settings.AccessFault), Action="http://tempuri.org/ISettingsWebService/GetTendersAccessFaultFault", Name="AccessFault", Namespace="http://schemas.datacontract.org/2004/07/Pos.Web.Service.Faults")]
        [System.ServiceModel.FaultContractAttribute(typeof(Brink.Api.Settings.LocationFault), Action="http://tempuri.org/ISettingsWebService/GetTendersLocationFaultFault", Name="LocationFault", Namespace="http://schemas.datacontract.org/2004/07/Pos.Web.Service.Faults")]
        System.Threading.Tasks.Task<System.Collections.Generic.List<Brink.Api.Settings.Tender>> GetTendersAsync(string accessToken, string locationToken);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://tempuri.org/ISettingsWebService/GetVoidReasons", ReplyAction="http://tempuri.org/ISettingsWebService/GetVoidReasonsResponse")]
        [System.ServiceModel.FaultContractAttribute(typeof(Brink.Api.Settings.AccessFault), Action="http://tempuri.org/ISettingsWebService/GetVoidReasonsAccessFaultFault", Name="AccessFault", Namespace="http://schemas.datacontract.org/2004/07/Pos.Web.Service.Faults")]
        [System.ServiceModel.FaultContractAttribute(typeof(Brink.Api.Settings.LocationFault), Action="http://tempuri.org/ISettingsWebService/GetVoidReasonsLocationFaultFault", Name="LocationFault", Namespace="http://schemas.datacontract.org/2004/07/Pos.Web.Service.Faults")]
        System.Threading.Tasks.Task<System.Collections.Generic.List<Brink.Api.Settings.VoidReason>> GetVoidReasonsAsync(string accessToken, string locationToken);
    }
    
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.0.1-preview-30310-0943")]
    public interface ISettingsWebServiceChannel : Brink.Api.Settings.ISettingsWebService, System.ServiceModel.IClientChannel
    {
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.0.1-preview-30310-0943")]
    public partial class SettingsWebServiceClient : System.ServiceModel.ClientBase<Brink.Api.Settings.ISettingsWebService>, Brink.Api.Settings.ISettingsWebService
    {
        
        /// <summary>
        /// Implement this partial method to configure the service endpoint.
        /// </summary>
        /// <param name="serviceEndpoint">The endpoint to configure</param>
        /// <param name="clientCredentials">The client credentials</param>
        static partial void ConfigureEndpoint(System.ServiceModel.Description.ServiceEndpoint serviceEndpoint, System.ServiceModel.Description.ClientCredentials clientCredentials);
        
        public SettingsWebServiceClient() : 
                base(SettingsWebServiceClient.GetDefaultBinding(), SettingsWebServiceClient.GetDefaultEndpointAddress())
        {
            this.Endpoint.Name = EndpointConfiguration.BasicHttpsBinding_ISettingsWebService.ToString();
            ConfigureEndpoint(this.Endpoint, this.ClientCredentials);
        }
        
        public SettingsWebServiceClient(EndpointConfiguration endpointConfiguration) : 
                base(SettingsWebServiceClient.GetBindingForEndpoint(endpointConfiguration), SettingsWebServiceClient.GetEndpointAddress(endpointConfiguration))
        {
            this.Endpoint.Name = endpointConfiguration.ToString();
            ConfigureEndpoint(this.Endpoint, this.ClientCredentials);
        }
        
        public SettingsWebServiceClient(EndpointConfiguration endpointConfiguration, string remoteAddress) : 
                base(SettingsWebServiceClient.GetBindingForEndpoint(endpointConfiguration), new System.ServiceModel.EndpointAddress(remoteAddress))
        {
            this.Endpoint.Name = endpointConfiguration.ToString();
            ConfigureEndpoint(this.Endpoint, this.ClientCredentials);
        }
        
        public SettingsWebServiceClient(EndpointConfiguration endpointConfiguration, System.ServiceModel.EndpointAddress remoteAddress) : 
                base(SettingsWebServiceClient.GetBindingForEndpoint(endpointConfiguration), remoteAddress)
        {
            this.Endpoint.Name = endpointConfiguration.ToString();
            ConfigureEndpoint(this.Endpoint, this.ClientCredentials);
        }
        
        public SettingsWebServiceClient(System.ServiceModel.Channels.Binding binding, System.ServiceModel.EndpointAddress remoteAddress) : 
                base(binding, remoteAddress)
        {
        }
        
        public System.Threading.Tasks.Task<System.Collections.Generic.List<Brink.Api.Settings.CashDrawer>> GetCashDrawersAsync(string accessToken, string locationToken)
        {
            return base.Channel.GetCashDrawersAsync(accessToken, locationToken);
        }
        
        public System.Threading.Tasks.Task<System.Collections.Generic.List<Brink.Api.Settings.DayPart>> GetDayPartsAsync(string accessToken, string locationToken)
        {
            return base.Channel.GetDayPartsAsync(accessToken, locationToken);
        }
        
        public System.Threading.Tasks.Task<System.Collections.Generic.List<Brink.Api.Settings.Destination>> GetDestinationsAsync(string accessToken, string locationToken)
        {
            return base.Channel.GetDestinationsAsync(accessToken, locationToken);
        }
        
        public System.Threading.Tasks.Task<System.Collections.Generic.List<Brink.Api.Settings.Discount>> GetDiscountsAsync(string accessToken, string locationToken)
        {
            return base.Channel.GetDiscountsAsync(accessToken, locationToken);
        }
        
        public System.Threading.Tasks.Task<System.Collections.Generic.List<Brink.Api.Settings.Employee>> GetEmployeesAsync(string accessToken, string locationToken)
        {
            return base.Channel.GetEmployeesAsync(accessToken, locationToken);
        }
        
        public System.Threading.Tasks.Task<System.Collections.Generic.List<Brink.Api.Settings.ItemGroup>> GetItemGroupsAsync(string accessToken, string locationToken)
        {
            return base.Channel.GetItemGroupsAsync(accessToken, locationToken);
        }
        
        public System.Threading.Tasks.Task<System.Collections.Generic.List<Brink.Api.Settings.Item>> GetItemsAsync(string accessToken, string locationToken)
        {
            return base.Channel.GetItemsAsync(accessToken, locationToken);
        }
        
        public System.Threading.Tasks.Task<System.Collections.Generic.List<Brink.Api.Settings.Job>> GetJobsAsync(string accessToken, string locationToken)
        {
            return base.Channel.GetJobsAsync(accessToken, locationToken);
        }
        
        public System.Threading.Tasks.Task<System.Collections.Generic.List<Brink.Api.Settings.LoyaltyReward>> GetLoyaltyRewardsAsync(string accessToken, string locationToken)
        {
            return base.Channel.GetLoyaltyRewardsAsync(accessToken, locationToken);
        }
        
        public System.Threading.Tasks.Task<System.Collections.Generic.List<Brink.Api.Settings.ModifierCode>> GetModifierCodesAsync(string accessToken, string locationToken)
        {
            return base.Channel.GetModifierCodesAsync(accessToken, locationToken);
        }
        
        public System.Threading.Tasks.Task<System.Collections.Generic.List<Brink.Api.Settings.ModifierGroup>> GetModifierGroupsAsync(string accessToken, string locationToken)
        {
            return base.Channel.GetModifierGroupsAsync(accessToken, locationToken);
        }
        
        public System.Threading.Tasks.Task<Brink.Api.Settings.Options> GetOptionsAsync(string accessToken, string locationToken)
        {
            return base.Channel.GetOptionsAsync(accessToken, locationToken);
        }
        
        public System.Threading.Tasks.Task<System.Collections.Generic.List<Brink.Api.Settings.PettyAccount>> GetPettyAccountsAsync(string accessToken, string locationToken)
        {
            return base.Channel.GetPettyAccountsAsync(accessToken, locationToken);
        }
        
        public System.Threading.Tasks.Task<System.Collections.Generic.List<Brink.Api.Settings.Promotion>> GetPromotionsAsync(string accessToken, string locationToken)
        {
            return base.Channel.GetPromotionsAsync(accessToken, locationToken);
        }
        
        public System.Threading.Tasks.Task<System.Collections.Generic.List<Brink.Api.Settings.Register>> GetRegistersAsync(string accessToken, string locationToken)
        {
            return base.Channel.GetRegistersAsync(accessToken, locationToken);
        }
        
        public System.Threading.Tasks.Task<System.Collections.Generic.List<Brink.Api.Settings.RevenueCenter>> GetRevenueCentersAsync(string accessToken, string locationToken)
        {
            return base.Channel.GetRevenueCentersAsync(accessToken, locationToken);
        }
        
        public System.Threading.Tasks.Task<System.Collections.Generic.List<Brink.Api.Settings.Section>> GetSectionsAsync(string accessToken, string locationToken)
        {
            return base.Channel.GetSectionsAsync(accessToken, locationToken);
        }
        
        public System.Threading.Tasks.Task<System.Collections.Generic.List<Brink.Api.Settings.Surcharge>> GetSurchargesAsync(string accessToken, string locationToken)
        {
            return base.Channel.GetSurchargesAsync(accessToken, locationToken);
        }
        
        public System.Threading.Tasks.Task<System.Collections.Generic.List<Brink.Api.Settings.Tax>> GetTaxesAsync(string accessToken, string locationToken)
        {
            return base.Channel.GetTaxesAsync(accessToken, locationToken);
        }
        
        public System.Threading.Tasks.Task<System.Collections.Generic.List<Brink.Api.Settings.Tender>> GetTendersAsync(string accessToken, string locationToken)
        {
            return base.Channel.GetTendersAsync(accessToken, locationToken);
        }
        
        public System.Threading.Tasks.Task<System.Collections.Generic.List<Brink.Api.Settings.VoidReason>> GetVoidReasonsAsync(string accessToken, string locationToken)
        {
            return base.Channel.GetVoidReasonsAsync(accessToken, locationToken);
        }
        
        public virtual System.Threading.Tasks.Task OpenAsync()
        {
            return System.Threading.Tasks.Task.Factory.FromAsync(((System.ServiceModel.ICommunicationObject)(this)).BeginOpen(null, null), new System.Action<System.IAsyncResult>(((System.ServiceModel.ICommunicationObject)(this)).EndOpen));
        }
        
        public virtual System.Threading.Tasks.Task CloseAsync()
        {
            return System.Threading.Tasks.Task.Factory.FromAsync(((System.ServiceModel.ICommunicationObject)(this)).BeginClose(null, null), new System.Action<System.IAsyncResult>(((System.ServiceModel.ICommunicationObject)(this)).EndClose));
        }
        
        private static System.ServiceModel.Channels.Binding GetBindingForEndpoint(EndpointConfiguration endpointConfiguration)
        {
            if ((endpointConfiguration == EndpointConfiguration.BasicHttpsBinding_ISettingsWebService))
            {
                System.ServiceModel.BasicHttpsBinding result = new System.ServiceModel.BasicHttpsBinding();
                result.MaxBufferSize = int.MaxValue;
                result.ReaderQuotas = System.Xml.XmlDictionaryReaderQuotas.Max;
                result.MaxReceivedMessageSize = int.MaxValue;
                result.AllowCookies = true;
                return result;
            }
            throw new System.InvalidOperationException(string.Format("Could not find endpoint with name \'{0}\'.", endpointConfiguration));
        }
        
        private static System.ServiceModel.EndpointAddress GetEndpointAddress(EndpointConfiguration endpointConfiguration)
        {
            if ((endpointConfiguration == EndpointConfiguration.BasicHttpsBinding_ISettingsWebService))
            {
                return new System.ServiceModel.EndpointAddress("http://api-devapi01.brinkpos.net/Settings.svc");
            }
            throw new System.InvalidOperationException(string.Format("Could not find endpoint with name \'{0}\'.", endpointConfiguration));
        }
        
        private static System.ServiceModel.Channels.Binding GetDefaultBinding()
        {
            return SettingsWebServiceClient.GetBindingForEndpoint(EndpointConfiguration.BasicHttpsBinding_ISettingsWebService);
        }
        
        private static System.ServiceModel.EndpointAddress GetDefaultEndpointAddress()
        {
            return SettingsWebServiceClient.GetEndpointAddress(EndpointConfiguration.BasicHttpsBinding_ISettingsWebService);
        }
        
        public enum EndpointConfiguration
        {
            
            BasicHttpsBinding_ISettingsWebService,
        }
    }
}
