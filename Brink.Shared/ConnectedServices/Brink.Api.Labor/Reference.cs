﻿//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

namespace Brink.Api.Labor
{
    using System.Runtime.Serialization;
    
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.0.1-preview-30310-0943")]
    [System.Runtime.Serialization.DataContractAttribute(Name="GetLaborScheduleRequest", Namespace="http://www.brinksoftware.com/webservices/labor/v2")]
    public partial class GetLaborScheduleRequest : object
    {
        
        private System.DateTime EndDateField;
        
        private System.DateTime StartDateField;
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public System.DateTime EndDate
        {
            get
            {
                return this.EndDateField;
            }
            set
            {
                this.EndDateField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public System.DateTime StartDate
        {
            get
            {
                return this.StartDateField;
            }
            set
            {
                this.StartDateField = value;
            }
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.0.1-preview-30310-0943")]
    [System.Runtime.Serialization.DataContractAttribute(Name="Reply", Namespace="http://schemas.datacontract.org/2004/07/Pos.Web.Service")]
    [System.Runtime.Serialization.KnownTypeAttribute(typeof(Brink.Api.Labor.LaborWebService2Reply))]
    [System.Runtime.Serialization.KnownTypeAttribute(typeof(Brink.Api.Labor.GetShiftsReply))]
    [System.Runtime.Serialization.KnownTypeAttribute(typeof(Brink.Api.Labor.SaveLaborScheduleReply))]
    [System.Runtime.Serialization.KnownTypeAttribute(typeof(Brink.Api.Labor.GetLaborScheduleReply))]
    public partial class Reply : object
    {
        
        private string MessageField;
        
        private int ResultCodeField;
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string Message
        {
            get
            {
                return this.MessageField;
            }
            set
            {
                this.MessageField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public int ResultCode
        {
            get
            {
                return this.ResultCodeField;
            }
            set
            {
                this.ResultCodeField = value;
            }
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.0.1-preview-30310-0943")]
    [System.Runtime.Serialization.DataContractAttribute(Name="LaborWebService2Reply", Namespace="http://www.brinksoftware.com/webservices/labor/v2")]
    [System.Runtime.Serialization.KnownTypeAttribute(typeof(Brink.Api.Labor.GetShiftsReply))]
    [System.Runtime.Serialization.KnownTypeAttribute(typeof(Brink.Api.Labor.SaveLaborScheduleReply))]
    [System.Runtime.Serialization.KnownTypeAttribute(typeof(Brink.Api.Labor.GetLaborScheduleReply))]
    public partial class LaborWebService2Reply : Brink.Api.Labor.Reply
    {
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.0.1-preview-30310-0943")]
    [System.Runtime.Serialization.DataContractAttribute(Name="GetShiftsReply", Namespace="http://www.brinksoftware.com/webservices/labor/v2")]
    public partial class GetShiftsReply : Brink.Api.Labor.LaborWebService2Reply
    {
        
        private System.Collections.Generic.List<Brink.Api.Labor.Shift> ShiftsField;
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public System.Collections.Generic.List<Brink.Api.Labor.Shift> Shifts
        {
            get
            {
                return this.ShiftsField;
            }
            set
            {
                this.ShiftsField = value;
            }
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.0.1-preview-30310-0943")]
    [System.Runtime.Serialization.DataContractAttribute(Name="SaveLaborScheduleReply", Namespace="http://www.brinksoftware.com/webservices/labor/v2")]
    public partial class SaveLaborScheduleReply : Brink.Api.Labor.LaborWebService2Reply
    {
        
        private System.Nullable<int> EmployeeIdField;
        
        private System.Nullable<int> JobIdField;
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public System.Nullable<int> EmployeeId
        {
            get
            {
                return this.EmployeeIdField;
            }
            set
            {
                this.EmployeeIdField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public System.Nullable<int> JobId
        {
            get
            {
                return this.JobIdField;
            }
            set
            {
                this.JobIdField = value;
            }
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.0.1-preview-30310-0943")]
    [System.Runtime.Serialization.DataContractAttribute(Name="GetLaborScheduleReply", Namespace="http://www.brinksoftware.com/webservices/labor/v2")]
    public partial class GetLaborScheduleReply : Brink.Api.Labor.LaborWebService2Reply
    {
        
        private Brink.Api.Labor.LaborSchedule LaborScheduleField;
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public Brink.Api.Labor.LaborSchedule LaborSchedule
        {
            get
            {
                return this.LaborScheduleField;
            }
            set
            {
                this.LaborScheduleField = value;
            }
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.0.1-preview-30310-0943")]
    [System.Runtime.Serialization.DataContractAttribute(Name="LaborSchedule", Namespace="http://www.brinksoftware.com/webservices/labor/v2")]
    public partial class LaborSchedule : object
    {
        
        private System.Collections.Generic.List<Brink.Api.Labor.LaborScheduleDay> DaysField;
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public System.Collections.Generic.List<Brink.Api.Labor.LaborScheduleDay> Days
        {
            get
            {
                return this.DaysField;
            }
            set
            {
                this.DaysField = value;
            }
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.0.1-preview-30310-0943")]
    [System.Runtime.Serialization.DataContractAttribute(Name="Shift", Namespace="http://www.brinksoftware.com/webservices/labor/v2")]
    public partial class Shift : object
    {
        
        private System.Collections.Generic.List<Brink.Api.Labor.Break> BreaksField;
        
        private System.DateTime BusinessDateField;
        
        private System.DateTime ClockOutBusinessDateField;
        
        private decimal DeclaredTipsField;
        
        private int EmployeeIdField;
        
        private System.DateTimeOffset EndTimeField;
        
        private int ExtendedMinutesWorkedField;
        
        private System.Guid IdField;
        
        private int JobIdField;
        
        private int MinutesWorkedField;
        
        private System.DateTimeOffset ModifiedTimeField;
        
        private byte NumberField;
        
        private int OvertimeMinutesWorkedField;
        
        private decimal PayRateField;
        
        private int RegularMinutesWorkedField;
        
        private System.DateTimeOffset StartTimeField;
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public System.Collections.Generic.List<Brink.Api.Labor.Break> Breaks
        {
            get
            {
                return this.BreaksField;
            }
            set
            {
                this.BreaksField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public System.DateTime BusinessDate
        {
            get
            {
                return this.BusinessDateField;
            }
            set
            {
                this.BusinessDateField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public System.DateTime ClockOutBusinessDate
        {
            get
            {
                return this.ClockOutBusinessDateField;
            }
            set
            {
                this.ClockOutBusinessDateField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public decimal DeclaredTips
        {
            get
            {
                return this.DeclaredTipsField;
            }
            set
            {
                this.DeclaredTipsField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public int EmployeeId
        {
            get
            {
                return this.EmployeeIdField;
            }
            set
            {
                this.EmployeeIdField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public System.DateTimeOffset EndTime
        {
            get
            {
                return this.EndTimeField;
            }
            set
            {
                this.EndTimeField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public int ExtendedMinutesWorked
        {
            get
            {
                return this.ExtendedMinutesWorkedField;
            }
            set
            {
                this.ExtendedMinutesWorkedField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public System.Guid Id
        {
            get
            {
                return this.IdField;
            }
            set
            {
                this.IdField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public int JobId
        {
            get
            {
                return this.JobIdField;
            }
            set
            {
                this.JobIdField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public int MinutesWorked
        {
            get
            {
                return this.MinutesWorkedField;
            }
            set
            {
                this.MinutesWorkedField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public System.DateTimeOffset ModifiedTime
        {
            get
            {
                return this.ModifiedTimeField;
            }
            set
            {
                this.ModifiedTimeField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public byte Number
        {
            get
            {
                return this.NumberField;
            }
            set
            {
                this.NumberField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public int OvertimeMinutesWorked
        {
            get
            {
                return this.OvertimeMinutesWorkedField;
            }
            set
            {
                this.OvertimeMinutesWorkedField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public decimal PayRate
        {
            get
            {
                return this.PayRateField;
            }
            set
            {
                this.PayRateField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public int RegularMinutesWorked
        {
            get
            {
                return this.RegularMinutesWorkedField;
            }
            set
            {
                this.RegularMinutesWorkedField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public System.DateTimeOffset StartTime
        {
            get
            {
                return this.StartTimeField;
            }
            set
            {
                this.StartTimeField = value;
            }
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.0.1-preview-30310-0943")]
    [System.Runtime.Serialization.DataContractAttribute(Name="Break", Namespace="http://www.brinksoftware.com/webservices/labor/v2")]
    public partial class Break : object
    {
        
        private System.DateTimeOffset EndTimeField;
        
        private bool IsPaidField;
        
        private int MinutesField;
        
        private byte NumberField;
        
        private System.DateTimeOffset StartTimeField;
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public System.DateTimeOffset EndTime
        {
            get
            {
                return this.EndTimeField;
            }
            set
            {
                this.EndTimeField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public bool IsPaid
        {
            get
            {
                return this.IsPaidField;
            }
            set
            {
                this.IsPaidField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public int Minutes
        {
            get
            {
                return this.MinutesField;
            }
            set
            {
                this.MinutesField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public byte Number
        {
            get
            {
                return this.NumberField;
            }
            set
            {
                this.NumberField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public System.DateTimeOffset StartTime
        {
            get
            {
                return this.StartTimeField;
            }
            set
            {
                this.StartTimeField = value;
            }
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.0.1-preview-30310-0943")]
    [System.Runtime.Serialization.DataContractAttribute(Name="LaborScheduleDay", Namespace="http://www.brinksoftware.com/webservices/labor/v2")]
    public partial class LaborScheduleDay : object
    {
        
        private System.DateTime BusinessDateField;
        
        private System.Collections.Generic.List<Brink.Api.Labor.ScheduledShift> ShiftsField;
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public System.DateTime BusinessDate
        {
            get
            {
                return this.BusinessDateField;
            }
            set
            {
                this.BusinessDateField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public System.Collections.Generic.List<Brink.Api.Labor.ScheduledShift> Shifts
        {
            get
            {
                return this.ShiftsField;
            }
            set
            {
                this.ShiftsField = value;
            }
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.0.1-preview-30310-0943")]
    [System.Runtime.Serialization.DataContractAttribute(Name="ScheduledShift", Namespace="http://www.brinksoftware.com/webservices/labor/v2")]
    public partial class ScheduledShift : object
    {
        
        private int EmployeeIdField;
        
        private System.TimeSpan EndTimeField;
        
        private int JobIdField;
        
        private System.TimeSpan StartTimeField;
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public int EmployeeId
        {
            get
            {
                return this.EmployeeIdField;
            }
            set
            {
                this.EmployeeIdField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public System.TimeSpan EndTime
        {
            get
            {
                return this.EndTimeField;
            }
            set
            {
                this.EndTimeField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public int JobId
        {
            get
            {
                return this.JobIdField;
            }
            set
            {
                this.JobIdField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public System.TimeSpan StartTime
        {
            get
            {
                return this.StartTimeField;
            }
            set
            {
                this.StartTimeField = value;
            }
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.0.1-preview-30310-0943")]
    [System.Runtime.Serialization.DataContractAttribute(Name="GetShiftsRequest", Namespace="http://www.brinksoftware.com/webservices/labor/v2")]
    public partial class GetShiftsRequest : object
    {
        
        private System.Nullable<System.DateTime> BusinessDateField;
        
        private System.Nullable<System.DateTimeOffset> ModifiedTimeField;
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public System.Nullable<System.DateTime> BusinessDate
        {
            get
            {
                return this.BusinessDateField;
            }
            set
            {
                this.BusinessDateField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public System.Nullable<System.DateTimeOffset> ModifiedTime
        {
            get
            {
                return this.ModifiedTimeField;
            }
            set
            {
                this.ModifiedTimeField = value;
            }
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.0.1-preview-30310-0943")]
    [System.Runtime.Serialization.DataContractAttribute(Name="SaveLaborScheduleRequest", Namespace="http://www.brinksoftware.com/webservices/labor/v2")]
    public partial class SaveLaborScheduleRequest : object
    {
        
        private Brink.Api.Labor.LaborSchedule LaborScheduleField;
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public Brink.Api.Labor.LaborSchedule LaborSchedule
        {
            get
            {
                return this.LaborScheduleField;
            }
            set
            {
                this.LaborScheduleField = value;
            }
        }
    }
    
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.0.1-preview-30310-0943")]
    [System.ServiceModel.ServiceContractAttribute(Namespace="http://www.brinksoftware.com/webservices/labor/v2", ConfigurationName="Brink.Api.Labor.ILaborWebService2")]
    public interface ILaborWebService2
    {
        
        [System.ServiceModel.OperationContractAttribute(Action="http://www.brinksoftware.com/webservices/labor/v2/ILaborWebService2/GetLaborSched" +
            "ule", ReplyAction="http://www.brinksoftware.com/webservices/labor/v2/ILaborWebService2/GetLaborSched" +
            "uleResponse")]
        System.Threading.Tasks.Task<Brink.Api.Labor.GetLaborScheduleReply> GetLaborScheduleAsync(Brink.Api.Labor.GetLaborScheduleRequest request);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://www.brinksoftware.com/webservices/labor/v2/ILaborWebService2/GetShifts", ReplyAction="http://www.brinksoftware.com/webservices/labor/v2/ILaborWebService2/GetShiftsResp" +
            "onse")]
        System.Threading.Tasks.Task<Brink.Api.Labor.GetShiftsReply> GetShiftsAsync(Brink.Api.Labor.GetShiftsRequest request);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://www.brinksoftware.com/webservices/labor/v2/ILaborWebService2/SaveLaborSche" +
            "dule", ReplyAction="http://www.brinksoftware.com/webservices/labor/v2/ILaborWebService2/SaveLaborSche" +
            "duleResponse")]
        System.Threading.Tasks.Task<Brink.Api.Labor.SaveLaborScheduleReply> SaveLaborScheduleAsync(Brink.Api.Labor.SaveLaborScheduleRequest request);
    }
    
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.0.1-preview-30310-0943")]
    public interface ILaborWebService2Channel : Brink.Api.Labor.ILaborWebService2, System.ServiceModel.IClientChannel
    {
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.0.1-preview-30310-0943")]
    public partial class LaborWebService2Client : System.ServiceModel.ClientBase<Brink.Api.Labor.ILaborWebService2>, Brink.Api.Labor.ILaborWebService2
    {
        
        /// <summary>
        /// Implement this partial method to configure the service endpoint.
        /// </summary>
        /// <param name="serviceEndpoint">The endpoint to configure</param>
        /// <param name="clientCredentials">The client credentials</param>
        static partial void ConfigureEndpoint(System.ServiceModel.Description.ServiceEndpoint serviceEndpoint, System.ServiceModel.Description.ClientCredentials clientCredentials);
        
        public LaborWebService2Client() : 
                base(LaborWebService2Client.GetDefaultBinding(), LaborWebService2Client.GetDefaultEndpointAddress())
        {
            this.Endpoint.Name = EndpointConfiguration.BasicHttpsBinding_ILaborWebService2.ToString();
            ConfigureEndpoint(this.Endpoint, this.ClientCredentials);
        }
        
        public LaborWebService2Client(EndpointConfiguration endpointConfiguration) : 
                base(LaborWebService2Client.GetBindingForEndpoint(endpointConfiguration), LaborWebService2Client.GetEndpointAddress(endpointConfiguration))
        {
            this.Endpoint.Name = endpointConfiguration.ToString();
            ConfigureEndpoint(this.Endpoint, this.ClientCredentials);
        }
        
        public LaborWebService2Client(EndpointConfiguration endpointConfiguration, string remoteAddress) : 
                base(LaborWebService2Client.GetBindingForEndpoint(endpointConfiguration), new System.ServiceModel.EndpointAddress(remoteAddress))
        {
            this.Endpoint.Name = endpointConfiguration.ToString();
            ConfigureEndpoint(this.Endpoint, this.ClientCredentials);
        }
        
        public LaborWebService2Client(EndpointConfiguration endpointConfiguration, System.ServiceModel.EndpointAddress remoteAddress) : 
                base(LaborWebService2Client.GetBindingForEndpoint(endpointConfiguration), remoteAddress)
        {
            this.Endpoint.Name = endpointConfiguration.ToString();
            ConfigureEndpoint(this.Endpoint, this.ClientCredentials);
        }
        
        public LaborWebService2Client(System.ServiceModel.Channels.Binding binding, System.ServiceModel.EndpointAddress remoteAddress) : 
                base(binding, remoteAddress)
        {
        }
        
        public System.Threading.Tasks.Task<Brink.Api.Labor.GetLaborScheduleReply> GetLaborScheduleAsync(Brink.Api.Labor.GetLaborScheduleRequest request)
        {
            return base.Channel.GetLaborScheduleAsync(request);
        }
        
        public System.Threading.Tasks.Task<Brink.Api.Labor.GetShiftsReply> GetShiftsAsync(Brink.Api.Labor.GetShiftsRequest request)
        {
            return base.Channel.GetShiftsAsync(request);
        }
        
        public System.Threading.Tasks.Task<Brink.Api.Labor.SaveLaborScheduleReply> SaveLaborScheduleAsync(Brink.Api.Labor.SaveLaborScheduleRequest request)
        {
            return base.Channel.SaveLaborScheduleAsync(request);
        }
        
        public virtual System.Threading.Tasks.Task OpenAsync()
        {
            return System.Threading.Tasks.Task.Factory.FromAsync(((System.ServiceModel.ICommunicationObject)(this)).BeginOpen(null, null), new System.Action<System.IAsyncResult>(((System.ServiceModel.ICommunicationObject)(this)).EndOpen));
        }
        
        public virtual System.Threading.Tasks.Task CloseAsync()
        {
            return System.Threading.Tasks.Task.Factory.FromAsync(((System.ServiceModel.ICommunicationObject)(this)).BeginClose(null, null), new System.Action<System.IAsyncResult>(((System.ServiceModel.ICommunicationObject)(this)).EndClose));
        }
        
        private static System.ServiceModel.Channels.Binding GetBindingForEndpoint(EndpointConfiguration endpointConfiguration)
        {
            if ((endpointConfiguration == EndpointConfiguration.BasicHttpsBinding_ILaborWebService2))
            {
                System.ServiceModel.BasicHttpsBinding result = new System.ServiceModel.BasicHttpsBinding();
                result.MaxBufferSize = int.MaxValue;
                result.ReaderQuotas = System.Xml.XmlDictionaryReaderQuotas.Max;
                result.MaxReceivedMessageSize = int.MaxValue;
                result.AllowCookies = true;
                return result;
            }
            throw new System.InvalidOperationException(string.Format("Could not find endpoint with name \'{0}\'.", endpointConfiguration));
        }
        
        private static System.ServiceModel.EndpointAddress GetEndpointAddress(EndpointConfiguration endpointConfiguration)
        {
            if ((endpointConfiguration == EndpointConfiguration.BasicHttpsBinding_ILaborWebService2))
            {
                return new System.ServiceModel.EndpointAddress("https://api2.brinkpos.net/Labor2.svc");
            }
            throw new System.InvalidOperationException(string.Format("Could not find endpoint with name \'{0}\'.", endpointConfiguration));
        }
        
        private static System.ServiceModel.Channels.Binding GetDefaultBinding()
        {
            return LaborWebService2Client.GetBindingForEndpoint(EndpointConfiguration.BasicHttpsBinding_ILaborWebService2);
        }
        
        private static System.ServiceModel.EndpointAddress GetDefaultEndpointAddress()
        {
            return LaborWebService2Client.GetEndpointAddress(EndpointConfiguration.BasicHttpsBinding_ILaborWebService2);
        }
        
        public enum EndpointConfiguration
        {
            
            BasicHttpsBinding_ILaborWebService2,
        }
    }
}
