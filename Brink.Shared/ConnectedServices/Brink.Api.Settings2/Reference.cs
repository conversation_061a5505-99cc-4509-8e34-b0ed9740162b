﻿//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

namespace Brink.Api.Settings2
{
    using System.Runtime.Serialization;
    
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.0.1-preview-30310-0943")]
    [System.Runtime.Serialization.DataContractAttribute(Name="Reply", Namespace="http://schemas.datacontract.org/2004/07/Pos.Web.Service")]
    [System.Runtime.Serialization.KnownTypeAttribute(typeof(Brink.Api.Settings2.SettingsWebService2Reply))]
    [System.Runtime.Serialization.KnownTypeAttribute(typeof(Brink.Api.Settings2.GetFutureOrderingOptionsReply))]
    [System.Runtime.Serialization.KnownTypeAttribute(typeof(Brink.Api.Settings2.GetNodeSettingsCollectionReplyOfJobn1jeL_SCY))]
    [System.Runtime.Serialization.KnownTypeAttribute(typeof(Brink.Api.Settings2.GetJobsReply))]
    [System.Runtime.Serialization.KnownTypeAttribute(typeof(Brink.Api.Settings2.GetNodeSettingsCollectionReplyOfKitchenQueuen1jeL_SCY))]
    [System.Runtime.Serialization.KnownTypeAttribute(typeof(Brink.Api.Settings2.GetKitchenQueuesReply))]
    [System.Runtime.Serialization.KnownTypeAttribute(typeof(Brink.Api.Settings2.GetMenuReply))]
    [System.Runtime.Serialization.KnownTypeAttribute(typeof(Brink.Api.Settings2.GetOnlineOrderingMenuOptionsReply))]
    [System.Runtime.Serialization.KnownTypeAttribute(typeof(Brink.Api.Settings2.GetNodeSettingsCollectionReplyOfPermissionn1jeL_SCY))]
    [System.Runtime.Serialization.KnownTypeAttribute(typeof(Brink.Api.Settings2.GetPermissionsReply))]
    [System.Runtime.Serialization.KnownTypeAttribute(typeof(Brink.Api.Settings2.GetNodeSettingsCollectionReplyOfPriceChangen1jeL_SCY))]
    [System.Runtime.Serialization.KnownTypeAttribute(typeof(Brink.Api.Settings2.GetPriceChangesReply))]
    [System.Runtime.Serialization.KnownTypeAttribute(typeof(Brink.Api.Settings2.GetNodeSettingsCollectionReplyOfSecurityLeveln1jeL_SCY))]
    [System.Runtime.Serialization.KnownTypeAttribute(typeof(Brink.Api.Settings2.GetSecurityLevelsReply))]
    [System.Runtime.Serialization.KnownTypeAttribute(typeof(Brink.Api.Settings2.SaveNodeSettingsCollectionReply))]
    [System.Runtime.Serialization.KnownTypeAttribute(typeof(Brink.Api.Settings2.SaveEmployeesReply))]
    [System.Runtime.Serialization.KnownTypeAttribute(typeof(Brink.Api.Settings2.GetModifiedTimeReply))]
    [System.Runtime.Serialization.KnownTypeAttribute(typeof(Brink.Api.Settings2.GetNodeSettingsCollectionReplyOfEmployeen1jeL_SCY))]
    [System.Runtime.Serialization.KnownTypeAttribute(typeof(Brink.Api.Settings2.GetEmployeesReply))]
    public partial class Reply : object
    {
        
        private string MessageField;
        
        private int ResultCodeField;
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string Message
        {
            get
            {
                return this.MessageField;
            }
            set
            {
                this.MessageField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public int ResultCode
        {
            get
            {
                return this.ResultCodeField;
            }
            set
            {
                this.ResultCodeField = value;
            }
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.0.1-preview-30310-0943")]
    [System.Runtime.Serialization.DataContractAttribute(Name="SettingsWebService2Reply", Namespace="http://www.brinksoftware.com/webservices/settings/v2")]
    [System.Runtime.Serialization.KnownTypeAttribute(typeof(Brink.Api.Settings2.GetFutureOrderingOptionsReply))]
    [System.Runtime.Serialization.KnownTypeAttribute(typeof(Brink.Api.Settings2.GetNodeSettingsCollectionReplyOfJobn1jeL_SCY))]
    [System.Runtime.Serialization.KnownTypeAttribute(typeof(Brink.Api.Settings2.GetJobsReply))]
    [System.Runtime.Serialization.KnownTypeAttribute(typeof(Brink.Api.Settings2.GetNodeSettingsCollectionReplyOfKitchenQueuen1jeL_SCY))]
    [System.Runtime.Serialization.KnownTypeAttribute(typeof(Brink.Api.Settings2.GetKitchenQueuesReply))]
    [System.Runtime.Serialization.KnownTypeAttribute(typeof(Brink.Api.Settings2.GetMenuReply))]
    [System.Runtime.Serialization.KnownTypeAttribute(typeof(Brink.Api.Settings2.GetOnlineOrderingMenuOptionsReply))]
    [System.Runtime.Serialization.KnownTypeAttribute(typeof(Brink.Api.Settings2.GetNodeSettingsCollectionReplyOfPermissionn1jeL_SCY))]
    [System.Runtime.Serialization.KnownTypeAttribute(typeof(Brink.Api.Settings2.GetPermissionsReply))]
    [System.Runtime.Serialization.KnownTypeAttribute(typeof(Brink.Api.Settings2.GetNodeSettingsCollectionReplyOfPriceChangen1jeL_SCY))]
    [System.Runtime.Serialization.KnownTypeAttribute(typeof(Brink.Api.Settings2.GetPriceChangesReply))]
    [System.Runtime.Serialization.KnownTypeAttribute(typeof(Brink.Api.Settings2.GetNodeSettingsCollectionReplyOfSecurityLeveln1jeL_SCY))]
    [System.Runtime.Serialization.KnownTypeAttribute(typeof(Brink.Api.Settings2.GetSecurityLevelsReply))]
    [System.Runtime.Serialization.KnownTypeAttribute(typeof(Brink.Api.Settings2.SaveNodeSettingsCollectionReply))]
    [System.Runtime.Serialization.KnownTypeAttribute(typeof(Brink.Api.Settings2.SaveEmployeesReply))]
    [System.Runtime.Serialization.KnownTypeAttribute(typeof(Brink.Api.Settings2.GetModifiedTimeReply))]
    [System.Runtime.Serialization.KnownTypeAttribute(typeof(Brink.Api.Settings2.GetNodeSettingsCollectionReplyOfEmployeen1jeL_SCY))]
    [System.Runtime.Serialization.KnownTypeAttribute(typeof(Brink.Api.Settings2.GetEmployeesReply))]
    public partial class SettingsWebService2Reply : Brink.Api.Settings2.Reply
    {
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.0.1-preview-30310-0943")]
    [System.Runtime.Serialization.DataContractAttribute(Name="GetFutureOrderingOptionsReply", Namespace="http://www.brinksoftware.com/webservices/settings/v2")]
    public partial class GetFutureOrderingOptionsReply : Brink.Api.Settings2.SettingsWebService2Reply
    {
        
        private Brink.Api.Settings2.FutureOrderingOptions FutureOrderingOptionsField;
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public Brink.Api.Settings2.FutureOrderingOptions FutureOrderingOptions
        {
            get
            {
                return this.FutureOrderingOptionsField;
            }
            set
            {
                this.FutureOrderingOptionsField = value;
            }
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.0.1-preview-30310-0943")]
    [System.Runtime.Serialization.DataContractAttribute(Name="GetNodeSettingsCollectionReplyOfJobn1jeL_SCY", Namespace="http://www.brinksoftware.com/webservices/settings/v2")]
    [System.Runtime.Serialization.KnownTypeAttribute(typeof(Brink.Api.Settings2.GetJobsReply))]
    public partial class GetNodeSettingsCollectionReplyOfJobn1jeL_SCY : Brink.Api.Settings2.SettingsWebService2Reply
    {
        
        private System.Collections.Generic.List<Brink.Api.Settings2.Job> CollectionField;
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public System.Collections.Generic.List<Brink.Api.Settings2.Job> Collection
        {
            get
            {
                return this.CollectionField;
            }
            set
            {
                this.CollectionField = value;
            }
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.0.1-preview-30310-0943")]
    [System.Runtime.Serialization.DataContractAttribute(Name="GetJobsReply", Namespace="http://www.brinksoftware.com/webservices/settings/v2")]
    public partial class GetJobsReply : Brink.Api.Settings2.GetNodeSettingsCollectionReplyOfJobn1jeL_SCY
    {
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.0.1-preview-30310-0943")]
    [System.Runtime.Serialization.DataContractAttribute(Name="GetNodeSettingsCollectionReplyOfKitchenQueuen1jeL_SCY", Namespace="http://www.brinksoftware.com/webservices/settings/v2")]
    [System.Runtime.Serialization.KnownTypeAttribute(typeof(Brink.Api.Settings2.GetKitchenQueuesReply))]
    public partial class GetNodeSettingsCollectionReplyOfKitchenQueuen1jeL_SCY : Brink.Api.Settings2.SettingsWebService2Reply
    {
        
        private System.Collections.Generic.List<Brink.Api.Settings2.KitchenQueue> CollectionField;
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public System.Collections.Generic.List<Brink.Api.Settings2.KitchenQueue> Collection
        {
            get
            {
                return this.CollectionField;
            }
            set
            {
                this.CollectionField = value;
            }
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.0.1-preview-30310-0943")]
    [System.Runtime.Serialization.DataContractAttribute(Name="GetKitchenQueuesReply", Namespace="http://www.brinksoftware.com/webservices/settings/v2")]
    public partial class GetKitchenQueuesReply : Brink.Api.Settings2.GetNodeSettingsCollectionReplyOfKitchenQueuen1jeL_SCY
    {
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.0.1-preview-30310-0943")]
    [System.Runtime.Serialization.DataContractAttribute(Name="GetMenuReply", Namespace="http://www.brinksoftware.com/webservices/settings/v2")]
    public partial class GetMenuReply : Brink.Api.Settings2.SettingsWebService2Reply
    {
        
        private Brink.Api.Settings2.Menu MenuField;
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public Brink.Api.Settings2.Menu Menu
        {
            get
            {
                return this.MenuField;
            }
            set
            {
                this.MenuField = value;
            }
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.0.1-preview-30310-0943")]
    [System.Runtime.Serialization.DataContractAttribute(Name="GetOnlineOrderingMenuOptionsReply", Namespace="http://www.brinksoftware.com/webservices/settings/v2")]
    public partial class GetOnlineOrderingMenuOptionsReply : Brink.Api.Settings2.SettingsWebService2Reply
    {
        
        private System.Collections.Generic.List<Brink.Api.Settings2.AlternateMenu> AlternateMenusField;
        
        private System.Nullable<int> OnlineOrderingMenuIdField;
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public System.Collections.Generic.List<Brink.Api.Settings2.AlternateMenu> AlternateMenus
        {
            get
            {
                return this.AlternateMenusField;
            }
            set
            {
                this.AlternateMenusField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public System.Nullable<int> OnlineOrderingMenuId
        {
            get
            {
                return this.OnlineOrderingMenuIdField;
            }
            set
            {
                this.OnlineOrderingMenuIdField = value;
            }
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.0.1-preview-30310-0943")]
    [System.Runtime.Serialization.DataContractAttribute(Name="GetNodeSettingsCollectionReplyOfPermissionn1jeL_SCY", Namespace="http://www.brinksoftware.com/webservices/settings/v2")]
    [System.Runtime.Serialization.KnownTypeAttribute(typeof(Brink.Api.Settings2.GetPermissionsReply))]
    public partial class GetNodeSettingsCollectionReplyOfPermissionn1jeL_SCY : Brink.Api.Settings2.SettingsWebService2Reply
    {
        
        private System.Collections.Generic.List<Brink.Api.Settings2.Permission> CollectionField;
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public System.Collections.Generic.List<Brink.Api.Settings2.Permission> Collection
        {
            get
            {
                return this.CollectionField;
            }
            set
            {
                this.CollectionField = value;
            }
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.0.1-preview-30310-0943")]
    [System.Runtime.Serialization.DataContractAttribute(Name="GetPermissionsReply", Namespace="http://www.brinksoftware.com/webservices/settings/v2")]
    public partial class GetPermissionsReply : Brink.Api.Settings2.GetNodeSettingsCollectionReplyOfPermissionn1jeL_SCY
    {
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.0.1-preview-30310-0943")]
    [System.Runtime.Serialization.DataContractAttribute(Name="GetNodeSettingsCollectionReplyOfPriceChangen1jeL_SCY", Namespace="http://www.brinksoftware.com/webservices/settings/v2")]
    [System.Runtime.Serialization.KnownTypeAttribute(typeof(Brink.Api.Settings2.GetPriceChangesReply))]
    public partial class GetNodeSettingsCollectionReplyOfPriceChangen1jeL_SCY : Brink.Api.Settings2.SettingsWebService2Reply
    {
        
        private System.Collections.Generic.List<Brink.Api.Settings2.PriceChange> CollectionField;
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public System.Collections.Generic.List<Brink.Api.Settings2.PriceChange> Collection
        {
            get
            {
                return this.CollectionField;
            }
            set
            {
                this.CollectionField = value;
            }
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.0.1-preview-30310-0943")]
    [System.Runtime.Serialization.DataContractAttribute(Name="GetPriceChangesReply", Namespace="http://www.brinksoftware.com/webservices/settings/v2")]
    public partial class GetPriceChangesReply : Brink.Api.Settings2.GetNodeSettingsCollectionReplyOfPriceChangen1jeL_SCY
    {
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.0.1-preview-30310-0943")]
    [System.Runtime.Serialization.DataContractAttribute(Name="GetNodeSettingsCollectionReplyOfSecurityLeveln1jeL_SCY", Namespace="http://www.brinksoftware.com/webservices/settings/v2")]
    [System.Runtime.Serialization.KnownTypeAttribute(typeof(Brink.Api.Settings2.GetSecurityLevelsReply))]
    public partial class GetNodeSettingsCollectionReplyOfSecurityLeveln1jeL_SCY : Brink.Api.Settings2.SettingsWebService2Reply
    {
        
        private System.Collections.Generic.List<Brink.Api.Settings2.SecurityLevel> CollectionField;
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public System.Collections.Generic.List<Brink.Api.Settings2.SecurityLevel> Collection
        {
            get
            {
                return this.CollectionField;
            }
            set
            {
                this.CollectionField = value;
            }
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.0.1-preview-30310-0943")]
    [System.Runtime.Serialization.DataContractAttribute(Name="GetSecurityLevelsReply", Namespace="http://www.brinksoftware.com/webservices/settings/v2")]
    public partial class GetSecurityLevelsReply : Brink.Api.Settings2.GetNodeSettingsCollectionReplyOfSecurityLeveln1jeL_SCY
    {
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.0.1-preview-30310-0943")]
    [System.Runtime.Serialization.DataContractAttribute(Name="SaveNodeSettingsCollectionReply", Namespace="http://www.brinksoftware.com/webservices/settings/v2")]
    [System.Runtime.Serialization.KnownTypeAttribute(typeof(Brink.Api.Settings2.SaveEmployeesReply))]
    public partial class SaveNodeSettingsCollectionReply : Brink.Api.Settings2.SettingsWebService2Reply
    {
        
        private System.Collections.Generic.List<Brink.Api.Settings2.KeyedSettingsObjectSaveResult> SaveResultsField;
        
        private System.Collections.Generic.List<string> ValidationMessagesField;
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public System.Collections.Generic.List<Brink.Api.Settings2.KeyedSettingsObjectSaveResult> SaveResults
        {
            get
            {
                return this.SaveResultsField;
            }
            set
            {
                this.SaveResultsField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public System.Collections.Generic.List<string> ValidationMessages
        {
            get
            {
                return this.ValidationMessagesField;
            }
            set
            {
                this.ValidationMessagesField = value;
            }
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.0.1-preview-30310-0943")]
    [System.Runtime.Serialization.DataContractAttribute(Name="SaveEmployeesReply", Namespace="http://www.brinksoftware.com/webservices/settings/v2")]
    public partial class SaveEmployeesReply : Brink.Api.Settings2.SaveNodeSettingsCollectionReply
    {
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.0.1-preview-30310-0943")]
    [System.Runtime.Serialization.DataContractAttribute(Name="GetModifiedTimeReply", Namespace="http://www.brinksoftware.com/webservices/settings/v2")]
    public partial class GetModifiedTimeReply : Brink.Api.Settings2.SettingsWebService2Reply
    {
        
        private System.Nullable<System.DateTimeOffset> SettingsLastModifiedField;
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public System.Nullable<System.DateTimeOffset> SettingsLastModified
        {
            get
            {
                return this.SettingsLastModifiedField;
            }
            set
            {
                this.SettingsLastModifiedField = value;
            }
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.0.1-preview-30310-0943")]
    [System.Runtime.Serialization.DataContractAttribute(Name="GetNodeSettingsCollectionReplyOfEmployeen1jeL_SCY", Namespace="http://www.brinksoftware.com/webservices/settings/v2")]
    [System.Runtime.Serialization.KnownTypeAttribute(typeof(Brink.Api.Settings2.GetEmployeesReply))]
    public partial class GetNodeSettingsCollectionReplyOfEmployeen1jeL_SCY : Brink.Api.Settings2.SettingsWebService2Reply
    {
        
        private System.Collections.Generic.List<Brink.Api.Settings2.Employee> CollectionField;
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public System.Collections.Generic.List<Brink.Api.Settings2.Employee> Collection
        {
            get
            {
                return this.CollectionField;
            }
            set
            {
                this.CollectionField = value;
            }
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.0.1-preview-30310-0943")]
    [System.Runtime.Serialization.DataContractAttribute(Name="GetEmployeesReply", Namespace="http://www.brinksoftware.com/webservices/settings/v2")]
    public partial class GetEmployeesReply : Brink.Api.Settings2.GetNodeSettingsCollectionReplyOfEmployeen1jeL_SCY
    {
    }

    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.0.1-preview-30310-0943")]
    [System.Runtime.Serialization.DataContractAttribute(Name = "Employee", Namespace = "http://www.brinksoftware.com/webservices/settings/v2")]
    public partial class Employee : Brink.Api.Settings2.KeyedSettingsObject
    {
        public Employee()
        {
            this.JobsField = new System.Collections.Generic.List<Api.Settings2.EmployeeJob>();
        }

        private string Address1Field;
        
        private string Address2Field;
        
        private string AlternateIdField;
        
        private System.Nullable<System.DateTime> BirthDateField;
        
        private bool CanLoginWithCardField;
        
        private bool CanLoginWithFingerField;
        
        private bool CanLoginWithPinField;
        
        private string CardNumberField;
        
        private string CellPhoneField;
        
        private string CityField;
        
        private System.Nullable<int> ClockedInDiscountIdField;
        
        private System.Nullable<int> ClockedOutDiscountIdField;
        
        private string DisplayNameField;
        
        private string EmailAddressField;
        
        private bool ExportToPayrollField;
        
        private string FirstNameField;
        
        private System.Nullable<System.DateTime> HealthCardExpirationDateField;
        
        private System.Nullable<System.DateTime> HireDateField;
        
        private System.Nullable<System.Guid> HomeLocationIdField;
        
        private string HomePhoneField;
        
        private bool IdentificationVerifiedField;
        
        private bool IsExemptField;
        
        private bool IsSalariedField;
        
        private System.Collections.Generic.List<Brink.Api.Settings2.EmployeeJob> JobsField;
        
        private string LastNameField;
        
        private bool LimitLocationsField;
        
        private byte MaritalStatusField;
        
        private decimal MaximumDailyDiscountAmountField;
        
        private int MaximumDailyDiscountCountField;
        
        private string NotesField;
        
        private string PayrollIdField;
        
        private System.Collections.Generic.List<Brink.Api.Settings2.EmployeePermission> PermissionsField;
        
        private string PinField;
        
        private string SsnField;
        
        private string StateField;
        
        private int TaxWithholdingAllowanceField;
        
        private bool TerminatedField;
        
        private System.Nullable<System.DateTime> TerminationDateField;
        
        private System.Collections.Generic.List<Brink.Api.Settings2.EmployeeLocation> ValidLocationsField;
        
        private string ZipField;
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string Address1
        {
            get
            {
                return this.Address1Field;
            }
            set
            {
                this.Address1Field = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string Address2
        {
            get
            {
                return this.Address2Field;
            }
            set
            {
                this.Address2Field = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string AlternateId
        {
            get
            {
                return this.AlternateIdField;
            }
            set
            {
                this.AlternateIdField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public System.Nullable<System.DateTime> BirthDate
        {
            get
            {
                return this.BirthDateField;
            }
            set
            {
                this.BirthDateField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public bool CanLoginWithCard
        {
            get
            {
                return this.CanLoginWithCardField;
            }
            set
            {
                this.CanLoginWithCardField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public bool CanLoginWithFinger
        {
            get
            {
                return this.CanLoginWithFingerField;
            }
            set
            {
                this.CanLoginWithFingerField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public bool CanLoginWithPin
        {
            get
            {
                return this.CanLoginWithPinField;
            }
            set
            {
                this.CanLoginWithPinField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string CardNumber
        {
            get
            {
                return this.CardNumberField;
            }
            set
            {
                this.CardNumberField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string CellPhone
        {
            get
            {
                return this.CellPhoneField;
            }
            set
            {
                this.CellPhoneField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string City
        {
            get
            {
                return this.CityField;
            }
            set
            {
                this.CityField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public System.Nullable<int> ClockedInDiscountId
        {
            get
            {
                return this.ClockedInDiscountIdField;
            }
            set
            {
                this.ClockedInDiscountIdField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public System.Nullable<int> ClockedOutDiscountId
        {
            get
            {
                return this.ClockedOutDiscountIdField;
            }
            set
            {
                this.ClockedOutDiscountIdField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string DisplayName
        {
            get
            {
                return this.DisplayNameField;
            }
            set
            {
                this.DisplayNameField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string EmailAddress
        {
            get
            {
                return this.EmailAddressField;
            }
            set
            {
                this.EmailAddressField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public bool ExportToPayroll
        {
            get
            {
                return this.ExportToPayrollField;
            }
            set
            {
                this.ExportToPayrollField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string FirstName
        {
            get
            {
                return this.FirstNameField;
            }
            set
            {
                this.FirstNameField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public System.Nullable<System.DateTime> HealthCardExpirationDate
        {
            get
            {
                return this.HealthCardExpirationDateField;
            }
            set
            {
                this.HealthCardExpirationDateField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public System.Nullable<System.DateTime> HireDate
        {
            get
            {
                return this.HireDateField;
            }
            set
            {
                this.HireDateField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public System.Nullable<System.Guid> HomeLocationId
        {
            get
            {
                return this.HomeLocationIdField;
            }
            set
            {
                this.HomeLocationIdField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string HomePhone
        {
            get
            {
                return this.HomePhoneField;
            }
            set
            {
                this.HomePhoneField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public bool IdentificationVerified
        {
            get
            {
                return this.IdentificationVerifiedField;
            }
            set
            {
                this.IdentificationVerifiedField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public bool IsExempt
        {
            get
            {
                return this.IsExemptField;
            }
            set
            {
                this.IsExemptField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public bool IsSalaried
        {
            get
            {
                return this.IsSalariedField;
            }
            set
            {
                this.IsSalariedField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public System.Collections.Generic.List<Brink.Api.Settings2.EmployeeJob> Jobs
        {
            get
            {
                return this.JobsField;
            }
            set
            {
                this.JobsField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string LastName
        {
            get
            {
                return this.LastNameField;
            }
            set
            {
                this.LastNameField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public bool LimitLocations
        {
            get
            {
                return this.LimitLocationsField;
            }
            set
            {
                this.LimitLocationsField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public byte MaritalStatus
        {
            get
            {
                return this.MaritalStatusField;
            }
            set
            {
                this.MaritalStatusField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public decimal MaximumDailyDiscountAmount
        {
            get
            {
                return this.MaximumDailyDiscountAmountField;
            }
            set
            {
                this.MaximumDailyDiscountAmountField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public int MaximumDailyDiscountCount
        {
            get
            {
                return this.MaximumDailyDiscountCountField;
            }
            set
            {
                this.MaximumDailyDiscountCountField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string Notes
        {
            get
            {
                return this.NotesField;
            }
            set
            {
                this.NotesField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string PayrollId
        {
            get
            {
                return this.PayrollIdField;
            }
            set
            {
                this.PayrollIdField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public System.Collections.Generic.List<Brink.Api.Settings2.EmployeePermission> Permissions
        {
            get
            {
                return this.PermissionsField;
            }
            set
            {
                this.PermissionsField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string Pin
        {
            get
            {
                return this.PinField;
            }
            set
            {
                this.PinField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string Ssn
        {
            get
            {
                return this.SsnField;
            }
            set
            {
                this.SsnField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string State
        {
            get
            {
                return this.StateField;
            }
            set
            {
                this.StateField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public int TaxWithholdingAllowance
        {
            get
            {
                return this.TaxWithholdingAllowanceField;
            }
            set
            {
                this.TaxWithholdingAllowanceField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public bool Terminated
        {
            get
            {
                return this.TerminatedField;
            }
            set
            {
                this.TerminatedField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public System.Nullable<System.DateTime> TerminationDate
        {
            get
            {
                return this.TerminationDateField;
            }
            set
            {
                this.TerminationDateField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public System.Collections.Generic.List<Brink.Api.Settings2.EmployeeLocation> ValidLocations
        {
            get
            {
                return this.ValidLocationsField;
            }
            set
            {
                this.ValidLocationsField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string Zip
        {
            get
            {
                return this.ZipField;
            }
            set
            {
                this.ZipField = value;
            }
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.0.1-preview-30310-0943")]
    [System.Runtime.Serialization.DataContractAttribute(Name="FutureOrderingOptions", Namespace="http://www.brinksoftware.com/webservices/settings/v2")]
    public partial class FutureOrderingOptions : object
    {
        
        private bool AllowFutureDateOrderingField;
        
        private System.Collections.Generic.List<Brink.Api.Settings2.OnlineOrderingDestination> DestinationsField;
        
        private bool IsEnabledField;
        
        private System.Collections.Generic.List<Brink.Api.Settings2.LeadTimeThreshold> LeadTimeThresholdsField;
        
        private short MaximumDaysInAdvanceField;
        
        private short MinimumLeadMinutesField;
        
        private short MinimumPrepMinutesField;
        
        private System.Collections.Generic.List<Brink.Api.Settings2.PrepTimeThreshold> PrepTimeThresholdsField;
        
        private byte RequiredDepositPercentField;
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public bool AllowFutureDateOrdering
        {
            get
            {
                return this.AllowFutureDateOrderingField;
            }
            set
            {
                this.AllowFutureDateOrderingField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public System.Collections.Generic.List<Brink.Api.Settings2.OnlineOrderingDestination> Destinations
        {
            get
            {
                return this.DestinationsField;
            }
            set
            {
                this.DestinationsField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public bool IsEnabled
        {
            get
            {
                return this.IsEnabledField;
            }
            set
            {
                this.IsEnabledField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public System.Collections.Generic.List<Brink.Api.Settings2.LeadTimeThreshold> LeadTimeThresholds
        {
            get
            {
                return this.LeadTimeThresholdsField;
            }
            set
            {
                this.LeadTimeThresholdsField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public short MaximumDaysInAdvance
        {
            get
            {
                return this.MaximumDaysInAdvanceField;
            }
            set
            {
                this.MaximumDaysInAdvanceField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public short MinimumLeadMinutes
        {
            get
            {
                return this.MinimumLeadMinutesField;
            }
            set
            {
                this.MinimumLeadMinutesField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public short MinimumPrepMinutes
        {
            get
            {
                return this.MinimumPrepMinutesField;
            }
            set
            {
                this.MinimumPrepMinutesField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public System.Collections.Generic.List<Brink.Api.Settings2.PrepTimeThreshold> PrepTimeThresholds
        {
            get
            {
                return this.PrepTimeThresholdsField;
            }
            set
            {
                this.PrepTimeThresholdsField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public byte RequiredDepositPercent
        {
            get
            {
                return this.RequiredDepositPercentField;
            }
            set
            {
                this.RequiredDepositPercentField = value;
            }
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.0.1-preview-30310-0943")]
    [System.Runtime.Serialization.DataContractAttribute(Name="OnlineOrderingDestination", Namespace="http://www.brinksoftware.com/webservices/settings/v2")]
    public partial class OnlineOrderingDestination : object
    {
        
        private int DestinationIdField;
        
        private string InstructionsField;
        
        private bool IsDefaultField;
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public int DestinationId
        {
            get
            {
                return this.DestinationIdField;
            }
            set
            {
                this.DestinationIdField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string Instructions
        {
            get
            {
                return this.InstructionsField;
            }
            set
            {
                this.InstructionsField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public bool IsDefault
        {
            get
            {
                return this.IsDefaultField;
            }
            set
            {
                this.IsDefaultField = value;
            }
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.0.1-preview-30310-0943")]
    [System.Runtime.Serialization.DataContractAttribute(Name="LeadTimeThreshold", Namespace="http://www.brinksoftware.com/webservices/settings/v2")]
    public partial class LeadTimeThreshold : object
    {
        
        private int LeadMinutesField;
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public int LeadMinutes
        {
            get
            {
                return this.LeadMinutesField;
            }
            set
            {
                this.LeadMinutesField = value;
            }
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.0.1-preview-30310-0943")]
    [System.Runtime.Serialization.DataContractAttribute(Name="PrepTimeThreshold", Namespace="http://www.brinksoftware.com/webservices/settings/v2")]
    public partial class PrepTimeThreshold : object
    {
        
        private int PrepMinutesField;
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public int PrepMinutes
        {
            get
            {
                return this.PrepMinutesField;
            }
            set
            {
                this.PrepMinutesField = value;
            }
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.0.1-preview-30310-0943")]
    [System.Runtime.Serialization.DataContractAttribute(Name="Job", Namespace="http://www.brinksoftware.com/webservices/settings/v2")]
    public partial class Job : Brink.Api.Settings2.NamedSettingsObject
    {
        
        private bool AllowAddItemField;
        
        private string AlternateIdField;
        
        private bool CanOpenAnyOrderField;
        
        private bool CannotCloseField;
        
        private bool CashDrawerField;
        
        private bool CheckoutRequiresApprovalField;
        
        private bool ClockInRequiresApprovalField;
        
        private bool ClockOutRequiresApprovalField;
        
        private bool DeclareTipsField;
        
        private System.Nullable<int> DefaultScreenIdField;
        
        private int DisplayColorField;
        
        private bool ExcludeOnSalesAndLaborReportsField;
        
        private bool ExemptFromLaborScheduleField;
        
        private string ExportCodeField;
        
        private bool GroupItemsBySeatField;
        
        private bool IsBartenderField;
        
        private bool IsDeliveryDispatcherField;
        
        private bool IsDeliveryDriverField;
        
        private bool IsHostessField;
        
        private bool ItemLookupField;
        
        private System.Nullable<int> LaneIdField;
        
        private bool LimitShiftBreakTypesField;
        
        private System.Nullable<int> LimitedDestinationIdField;
        
        private byte LocationTypeField;
        
        private System.Collections.Generic.List<Brink.Api.Settings2.JobMenu> MenusField;
        
        private bool NoCashTransactionsField;
        
        private bool OrderEntryField;
        
        private System.Nullable<int> OrderScreenIdField;
        
        private System.Nullable<int> SectionIdField;
        
        private bool SelfBankingField;
        
        private bool SelfVoidField;
        
        private System.Collections.Generic.List<Brink.Api.Settings2.JobShiftBreakType> ShiftBreakTypesField;
        
        private bool TabsField;
        
        private bool TrainingField;
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public bool AllowAddItem
        {
            get
            {
                return this.AllowAddItemField;
            }
            set
            {
                this.AllowAddItemField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string AlternateId
        {
            get
            {
                return this.AlternateIdField;
            }
            set
            {
                this.AlternateIdField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public bool CanOpenAnyOrder
        {
            get
            {
                return this.CanOpenAnyOrderField;
            }
            set
            {
                this.CanOpenAnyOrderField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public bool CannotClose
        {
            get
            {
                return this.CannotCloseField;
            }
            set
            {
                this.CannotCloseField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public bool CashDrawer
        {
            get
            {
                return this.CashDrawerField;
            }
            set
            {
                this.CashDrawerField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public bool CheckoutRequiresApproval
        {
            get
            {
                return this.CheckoutRequiresApprovalField;
            }
            set
            {
                this.CheckoutRequiresApprovalField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public bool ClockInRequiresApproval
        {
            get
            {
                return this.ClockInRequiresApprovalField;
            }
            set
            {
                this.ClockInRequiresApprovalField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public bool ClockOutRequiresApproval
        {
            get
            {
                return this.ClockOutRequiresApprovalField;
            }
            set
            {
                this.ClockOutRequiresApprovalField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public bool DeclareTips
        {
            get
            {
                return this.DeclareTipsField;
            }
            set
            {
                this.DeclareTipsField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public System.Nullable<int> DefaultScreenId
        {
            get
            {
                return this.DefaultScreenIdField;
            }
            set
            {
                this.DefaultScreenIdField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public int DisplayColor
        {
            get
            {
                return this.DisplayColorField;
            }
            set
            {
                this.DisplayColorField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public bool ExcludeOnSalesAndLaborReports
        {
            get
            {
                return this.ExcludeOnSalesAndLaborReportsField;
            }
            set
            {
                this.ExcludeOnSalesAndLaborReportsField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public bool ExemptFromLaborSchedule
        {
            get
            {
                return this.ExemptFromLaborScheduleField;
            }
            set
            {
                this.ExemptFromLaborScheduleField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string ExportCode
        {
            get
            {
                return this.ExportCodeField;
            }
            set
            {
                this.ExportCodeField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public bool GroupItemsBySeat
        {
            get
            {
                return this.GroupItemsBySeatField;
            }
            set
            {
                this.GroupItemsBySeatField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public bool IsBartender
        {
            get
            {
                return this.IsBartenderField;
            }
            set
            {
                this.IsBartenderField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public bool IsDeliveryDispatcher
        {
            get
            {
                return this.IsDeliveryDispatcherField;
            }
            set
            {
                this.IsDeliveryDispatcherField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public bool IsDeliveryDriver
        {
            get
            {
                return this.IsDeliveryDriverField;
            }
            set
            {
                this.IsDeliveryDriverField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public bool IsHostess
        {
            get
            {
                return this.IsHostessField;
            }
            set
            {
                this.IsHostessField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public bool ItemLookup
        {
            get
            {
                return this.ItemLookupField;
            }
            set
            {
                this.ItemLookupField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public System.Nullable<int> LaneId
        {
            get
            {
                return this.LaneIdField;
            }
            set
            {
                this.LaneIdField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public bool LimitShiftBreakTypes
        {
            get
            {
                return this.LimitShiftBreakTypesField;
            }
            set
            {
                this.LimitShiftBreakTypesField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public System.Nullable<int> LimitedDestinationId
        {
            get
            {
                return this.LimitedDestinationIdField;
            }
            set
            {
                this.LimitedDestinationIdField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public byte LocationType
        {
            get
            {
                return this.LocationTypeField;
            }
            set
            {
                this.LocationTypeField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public System.Collections.Generic.List<Brink.Api.Settings2.JobMenu> Menus
        {
            get
            {
                return this.MenusField;
            }
            set
            {
                this.MenusField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public bool NoCashTransactions
        {
            get
            {
                return this.NoCashTransactionsField;
            }
            set
            {
                this.NoCashTransactionsField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public bool OrderEntry
        {
            get
            {
                return this.OrderEntryField;
            }
            set
            {
                this.OrderEntryField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public System.Nullable<int> OrderScreenId
        {
            get
            {
                return this.OrderScreenIdField;
            }
            set
            {
                this.OrderScreenIdField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public System.Nullable<int> SectionId
        {
            get
            {
                return this.SectionIdField;
            }
            set
            {
                this.SectionIdField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public bool SelfBanking
        {
            get
            {
                return this.SelfBankingField;
            }
            set
            {
                this.SelfBankingField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public bool SelfVoid
        {
            get
            {
                return this.SelfVoidField;
            }
            set
            {
                this.SelfVoidField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public System.Collections.Generic.List<Brink.Api.Settings2.JobShiftBreakType> ShiftBreakTypes
        {
            get
            {
                return this.ShiftBreakTypesField;
            }
            set
            {
                this.ShiftBreakTypesField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public bool Tabs
        {
            get
            {
                return this.TabsField;
            }
            set
            {
                this.TabsField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public bool Training
        {
            get
            {
                return this.TrainingField;
            }
            set
            {
                this.TrainingField = value;
            }
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.0.1-preview-30310-0943")]
    [System.Runtime.Serialization.DataContractAttribute(Name="KeyedSettingsObject", Namespace="http://www.brinksoftware.com/webservices/settings/v2")]
    [System.Runtime.Serialization.KnownTypeAttribute(typeof(Brink.Api.Settings2.Employee))]
    [System.Runtime.Serialization.KnownTypeAttribute(typeof(Brink.Api.Settings2.EmployeeJob))]
    [System.Runtime.Serialization.KnownTypeAttribute(typeof(Brink.Api.Settings2.EmployeePermission))]
    [System.Runtime.Serialization.KnownTypeAttribute(typeof(Brink.Api.Settings2.EmployeeLocation))]
    [System.Runtime.Serialization.KnownTypeAttribute(typeof(Brink.Api.Settings2.JobShiftBreakType))]
    [System.Runtime.Serialization.KnownTypeAttribute(typeof(Brink.Api.Settings2.AlternateMenu))]
    [System.Runtime.Serialization.KnownTypeAttribute(typeof(Brink.Api.Settings2.ItemPriceChange))]
    [System.Runtime.Serialization.KnownTypeAttribute(typeof(Brink.Api.Settings2.ComponentPriceChange))]
    [System.Runtime.Serialization.KnownTypeAttribute(typeof(Brink.Api.Settings2.ComponentItemPriceChange))]
    [System.Runtime.Serialization.KnownTypeAttribute(typeof(Brink.Api.Settings2.PriceChangeDestination))]
    [System.Runtime.Serialization.KnownTypeAttribute(typeof(Brink.Api.Settings2.SecurityLevelPermission))]
    [System.Runtime.Serialization.KnownTypeAttribute(typeof(Brink.Api.Settings2.NamedSettingsObject))]
    [System.Runtime.Serialization.KnownTypeAttribute(typeof(Brink.Api.Settings2.Menu))]
    [System.Runtime.Serialization.KnownTypeAttribute(typeof(Brink.Api.Settings2.MenuCategory))]
    [System.Runtime.Serialization.KnownTypeAttribute(typeof(Brink.Api.Settings2.MenuItem))]
    [System.Runtime.Serialization.KnownTypeAttribute(typeof(Brink.Api.Settings2.KitchenQueue))]
    [System.Runtime.Serialization.KnownTypeAttribute(typeof(Brink.Api.Settings2.Permission))]
    [System.Runtime.Serialization.KnownTypeAttribute(typeof(Brink.Api.Settings2.PriceChange))]
    [System.Runtime.Serialization.KnownTypeAttribute(typeof(Brink.Api.Settings2.SecurityLevel))]
    [System.Runtime.Serialization.KnownTypeAttribute(typeof(Brink.Api.Settings2.Job))]
    public partial class KeyedSettingsObject : object
    {
        
        private int IdField;
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public int Id
        {
            get
            {
                return this.IdField;
            }
            set
            {
                this.IdField = value;
            }
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.0.1-preview-30310-0943")]
    [System.Runtime.Serialization.DataContractAttribute(Name="EmployeeJob", Namespace="http://www.brinksoftware.com/webservices/settings/v2")]
    public partial class EmployeeJob : Brink.Api.Settings2.KeyedSettingsObject
    {
        
        private int JobIdField;
        
        private decimal PayRateField;
        
        private int SecurityLevelIdField;
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public int JobId
        {
            get
            {
                return this.JobIdField;
            }
            set
            {
                this.JobIdField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public decimal PayRate
        {
            get
            {
                return this.PayRateField;
            }
            set
            {
                this.PayRateField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public int SecurityLevelId
        {
            get
            {
                return this.SecurityLevelIdField;
            }
            set
            {
                this.SecurityLevelIdField = value;
            }
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.0.1-preview-30310-0943")]
    [System.Runtime.Serialization.DataContractAttribute(Name="EmployeePermission", Namespace="http://www.brinksoftware.com/webservices/settings/v2")]
    public partial class EmployeePermission : Brink.Api.Settings2.KeyedSettingsObject
    {
        
        private int PermissionIdField;
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public int PermissionId
        {
            get
            {
                return this.PermissionIdField;
            }
            set
            {
                this.PermissionIdField = value;
            }
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.0.1-preview-30310-0943")]
    [System.Runtime.Serialization.DataContractAttribute(Name="EmployeeLocation", Namespace="http://www.brinksoftware.com/webservices/settings/v2")]
    public partial class EmployeeLocation : Brink.Api.Settings2.KeyedSettingsObject
    {
        
        private System.Guid LocationIdField;
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public System.Guid LocationId
        {
            get
            {
                return this.LocationIdField;
            }
            set
            {
                this.LocationIdField = value;
            }
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.0.1-preview-30310-0943")]
    [System.Runtime.Serialization.DataContractAttribute(Name="JobShiftBreakType", Namespace="http://www.brinksoftware.com/webservices/settings/v2")]
    public partial class JobShiftBreakType : Brink.Api.Settings2.KeyedSettingsObject
    {
        
        private int ShiftBreakTypeIdField;
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public int ShiftBreakTypeId
        {
            get
            {
                return this.ShiftBreakTypeIdField;
            }
            set
            {
                this.ShiftBreakTypeIdField = value;
            }
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.0.1-preview-30310-0943")]
    [System.Runtime.Serialization.DataContractAttribute(Name="AlternateMenu", Namespace="http://www.brinksoftware.com/webservices/settings/v2")]
    public partial class AlternateMenu : Brink.Api.Settings2.KeyedSettingsObject
    {
        
        private System.Nullable<byte> DaysField;
        
        private System.TimeSpan EndTimeField;
        
        private int MenuIdField;
        
        private System.TimeSpan StartTimeField;
        
        private byte TimeTypeField;
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public System.Nullable<byte> Days
        {
            get
            {
                return this.DaysField;
            }
            set
            {
                this.DaysField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public System.TimeSpan EndTime
        {
            get
            {
                return this.EndTimeField;
            }
            set
            {
                this.EndTimeField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public int MenuId
        {
            get
            {
                return this.MenuIdField;
            }
            set
            {
                this.MenuIdField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public System.TimeSpan StartTime
        {
            get
            {
                return this.StartTimeField;
            }
            set
            {
                this.StartTimeField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public byte TimeType
        {
            get
            {
                return this.TimeTypeField;
            }
            set
            {
                this.TimeTypeField = value;
            }
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.0.1-preview-30310-0943")]
    [System.Runtime.Serialization.DataContractAttribute(Name="ItemPriceChange", Namespace="http://www.brinksoftware.com/webservices/settings/v2")]
    public partial class ItemPriceChange : Brink.Api.Settings2.KeyedSettingsObject
    {
        
        private System.Collections.Generic.List<Brink.Api.Settings2.ComponentPriceChange> ComponentPriceChangesField;
        
        private int ItemIdField;
        
        private string NameField;
        
        private System.Nullable<decimal> OriginalPriceField;
        
        private System.Nullable<decimal> PriceField;
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public System.Collections.Generic.List<Brink.Api.Settings2.ComponentPriceChange> ComponentPriceChanges
        {
            get
            {
                return this.ComponentPriceChangesField;
            }
            set
            {
                this.ComponentPriceChangesField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public int ItemId
        {
            get
            {
                return this.ItemIdField;
            }
            set
            {
                this.ItemIdField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string Name
        {
            get
            {
                return this.NameField;
            }
            set
            {
                this.NameField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public System.Nullable<decimal> OriginalPrice
        {
            get
            {
                return this.OriginalPriceField;
            }
            set
            {
                this.OriginalPriceField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public System.Nullable<decimal> Price
        {
            get
            {
                return this.PriceField;
            }
            set
            {
                this.PriceField = value;
            }
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.0.1-preview-30310-0943")]
    [System.Runtime.Serialization.DataContractAttribute(Name="ComponentPriceChange", Namespace="http://www.brinksoftware.com/webservices/settings/v2")]
    public partial class ComponentPriceChange : Brink.Api.Settings2.KeyedSettingsObject
    {
        
        private int ComponentIdField;
        
        private System.Collections.Generic.List<Brink.Api.Settings2.ComponentItemPriceChange> ComponentItemPriceChangesField;
        
        private System.Nullable<decimal> PriceField;
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public int ComponentId
        {
            get
            {
                return this.ComponentIdField;
            }
            set
            {
                this.ComponentIdField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public System.Collections.Generic.List<Brink.Api.Settings2.ComponentItemPriceChange> ComponentItemPriceChanges
        {
            get
            {
                return this.ComponentItemPriceChangesField;
            }
            set
            {
                this.ComponentItemPriceChangesField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public System.Nullable<decimal> Price
        {
            get
            {
                return this.PriceField;
            }
            set
            {
                this.PriceField = value;
            }
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.0.1-preview-30310-0943")]
    [System.Runtime.Serialization.DataContractAttribute(Name="ComponentItemPriceChange", Namespace="http://www.brinksoftware.com/webservices/settings/v2")]
    public partial class ComponentItemPriceChange : Brink.Api.Settings2.KeyedSettingsObject
    {
        
        private int ItemIdField;
        
        private System.Nullable<decimal> PriceField;
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public int ItemId
        {
            get
            {
                return this.ItemIdField;
            }
            set
            {
                this.ItemIdField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public System.Nullable<decimal> Price
        {
            get
            {
                return this.PriceField;
            }
            set
            {
                this.PriceField = value;
            }
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.0.1-preview-30310-0943")]
    [System.Runtime.Serialization.DataContractAttribute(Name="PriceChangeDestination", Namespace="http://www.brinksoftware.com/webservices/settings/v2")]
    public partial class PriceChangeDestination : Brink.Api.Settings2.KeyedSettingsObject
    {
        
        private int DestinationIdField;
        
        private string DestinationNameField;
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public int DestinationId
        {
            get
            {
                return this.DestinationIdField;
            }
            set
            {
                this.DestinationIdField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string DestinationName
        {
            get
            {
                return this.DestinationNameField;
            }
            set
            {
                this.DestinationNameField = value;
            }
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.0.1-preview-30310-0943")]
    [System.Runtime.Serialization.DataContractAttribute(Name="SecurityLevelPermission", Namespace="http://www.brinksoftware.com/webservices/settings/v2")]
    public partial class SecurityLevelPermission : Brink.Api.Settings2.KeyedSettingsObject
    {
        
        private int PermissionIdField;
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public int PermissionId
        {
            get
            {
                return this.PermissionIdField;
            }
            set
            {
                this.PermissionIdField = value;
            }
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.0.1-preview-30310-0943")]
    [System.Runtime.Serialization.DataContractAttribute(Name="NamedSettingsObject", Namespace="http://www.brinksoftware.com/webservices/settings/v2")]
    [System.Runtime.Serialization.KnownTypeAttribute(typeof(Brink.Api.Settings2.Menu))]
    [System.Runtime.Serialization.KnownTypeAttribute(typeof(Brink.Api.Settings2.MenuCategory))]
    [System.Runtime.Serialization.KnownTypeAttribute(typeof(Brink.Api.Settings2.MenuItem))]
    [System.Runtime.Serialization.KnownTypeAttribute(typeof(Brink.Api.Settings2.KitchenQueue))]
    [System.Runtime.Serialization.KnownTypeAttribute(typeof(Brink.Api.Settings2.Permission))]
    [System.Runtime.Serialization.KnownTypeAttribute(typeof(Brink.Api.Settings2.PriceChange))]
    [System.Runtime.Serialization.KnownTypeAttribute(typeof(Brink.Api.Settings2.SecurityLevel))]
    [System.Runtime.Serialization.KnownTypeAttribute(typeof(Brink.Api.Settings2.Job))]
    public partial class NamedSettingsObject : Brink.Api.Settings2.KeyedSettingsObject
    {
        
        private string NameField;
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string Name
        {
            get
            {
                return this.NameField;
            }
            set
            {
                this.NameField = value;
            }
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.0.1-preview-30310-0943")]
    [System.Runtime.Serialization.DataContractAttribute(Name="Menu", Namespace="http://schemas.datacontract.org/2004/07/Pos.Web.Service.Settings.Version2.Data")]
    public partial class Menu : Brink.Api.Settings2.NamedSettingsObject
    {
        
        private System.Collections.Generic.List<Brink.Api.Settings2.MenuCategory> CategoriesField;
        
        private string DescriptionField;
        
        private int ImageIdField;
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public System.Collections.Generic.List<Brink.Api.Settings2.MenuCategory> Categories
        {
            get
            {
                return this.CategoriesField;
            }
            set
            {
                this.CategoriesField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string Description
        {
            get
            {
                return this.DescriptionField;
            }
            set
            {
                this.DescriptionField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public int ImageId
        {
            get
            {
                return this.ImageIdField;
            }
            set
            {
                this.ImageIdField = value;
            }
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.0.1-preview-30310-0943")]
    [System.Runtime.Serialization.DataContractAttribute(Name="MenuCategory", Namespace="http://www.brinksoftware.com/webservices/settings/v2")]
    public partial class MenuCategory : Brink.Api.Settings2.NamedSettingsObject
    {
        
        private string DescriptionField;
        
        private System.Collections.Generic.List<Brink.Api.Settings2.MenuItem> ItemsField;
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string Description
        {
            get
            {
                return this.DescriptionField;
            }
            set
            {
                this.DescriptionField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public System.Collections.Generic.List<Brink.Api.Settings2.MenuItem> Items
        {
            get
            {
                return this.ItemsField;
            }
            set
            {
                this.ItemsField = value;
            }
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.0.1-preview-30310-0943")]
    [System.Runtime.Serialization.DataContractAttribute(Name="MenuItem", Namespace="http://www.brinksoftware.com/webservices/settings/v2")]
    public partial class MenuItem : Brink.Api.Settings2.NamedSettingsObject
    {
        
        private string DescriptionField;
        
        private int ImageIdField;
        
        private int ItemIdField;
        
        private byte ModifierMethodField;
        
        private decimal PriceField;
        
        private byte PriceMethodField;
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string Description
        {
            get
            {
                return this.DescriptionField;
            }
            set
            {
                this.DescriptionField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public int ImageId
        {
            get
            {
                return this.ImageIdField;
            }
            set
            {
                this.ImageIdField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public int ItemId
        {
            get
            {
                return this.ItemIdField;
            }
            set
            {
                this.ItemIdField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public byte ModifierMethod
        {
            get
            {
                return this.ModifierMethodField;
            }
            set
            {
                this.ModifierMethodField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public decimal Price
        {
            get
            {
                return this.PriceField;
            }
            set
            {
                this.PriceField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public byte PriceMethod
        {
            get
            {
                return this.PriceMethodField;
            }
            set
            {
                this.PriceMethodField = value;
            }
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.0.1-preview-30310-0943")]
    [System.Runtime.Serialization.DataContractAttribute(Name="KitchenQueue", Namespace="http://www.brinksoftware.com/webservices/settings/v2")]
    public partial class KitchenQueue : Brink.Api.Settings2.NamedSettingsObject
    {
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.0.1-preview-30310-0943")]
    [System.Runtime.Serialization.DataContractAttribute(Name="Permission", Namespace="http://www.brinksoftware.com/webservices/settings/v2")]
    public partial class Permission : Brink.Api.Settings2.NamedSettingsObject
    {
        
        private bool IsActiveField;
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public bool IsActive
        {
            get
            {
                return this.IsActiveField;
            }
            set
            {
                this.IsActiveField = value;
            }
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.0.1-preview-30310-0943")]
    [System.Runtime.Serialization.DataContractAttribute(Name="PriceChange", Namespace="http://www.brinksoftware.com/webservices/settings/v2")]
    public partial class PriceChange : Brink.Api.Settings2.NamedSettingsObject
    {
        
        private System.Nullable<byte> DaysField;
        
        private System.Nullable<System.DateTime> EndDateField;
        
        private System.Nullable<System.TimeSpan> EndTimeField;
        
        private bool EnforceDateRangesField;
        
        private bool EnforceDaysField;
        
        private bool EnforceTimeRangesField;
        
        private bool IsActiveField;
        
        private System.Collections.Generic.List<Brink.Api.Settings2.ItemPriceChange> ItemPriceChangesField;
        
        private System.Collections.Generic.List<Brink.Api.Settings2.PriceChangeDestination> PriceChangeDestinationsField;
        
        private System.Nullable<System.DateTime> StartDateField;
        
        private System.Nullable<System.TimeSpan> StartTimeField;
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public System.Nullable<byte> Days
        {
            get
            {
                return this.DaysField;
            }
            set
            {
                this.DaysField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public System.Nullable<System.DateTime> EndDate
        {
            get
            {
                return this.EndDateField;
            }
            set
            {
                this.EndDateField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public System.Nullable<System.TimeSpan> EndTime
        {
            get
            {
                return this.EndTimeField;
            }
            set
            {
                this.EndTimeField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public bool EnforceDateRanges
        {
            get
            {
                return this.EnforceDateRangesField;
            }
            set
            {
                this.EnforceDateRangesField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public bool EnforceDays
        {
            get
            {
                return this.EnforceDaysField;
            }
            set
            {
                this.EnforceDaysField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public bool EnforceTimeRanges
        {
            get
            {
                return this.EnforceTimeRangesField;
            }
            set
            {
                this.EnforceTimeRangesField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public bool IsActive
        {
            get
            {
                return this.IsActiveField;
            }
            set
            {
                this.IsActiveField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public System.Collections.Generic.List<Brink.Api.Settings2.ItemPriceChange> ItemPriceChanges
        {
            get
            {
                return this.ItemPriceChangesField;
            }
            set
            {
                this.ItemPriceChangesField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public System.Collections.Generic.List<Brink.Api.Settings2.PriceChangeDestination> PriceChangeDestinations
        {
            get
            {
                return this.PriceChangeDestinationsField;
            }
            set
            {
                this.PriceChangeDestinationsField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public System.Nullable<System.DateTime> StartDate
        {
            get
            {
                return this.StartDateField;
            }
            set
            {
                this.StartDateField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public System.Nullable<System.TimeSpan> StartTime
        {
            get
            {
                return this.StartTimeField;
            }
            set
            {
                this.StartTimeField = value;
            }
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.0.1-preview-30310-0943")]
    [System.Runtime.Serialization.DataContractAttribute(Name="SecurityLevel", Namespace="http://www.brinksoftware.com/webservices/settings/v2")]
    public partial class SecurityLevel : Brink.Api.Settings2.NamedSettingsObject
    {
        
        private bool AddSurchargesField;
        
        private bool AllowExcessEmployeeCardUsageField;
        
        private bool ApproveCheckoutField;
        
        private bool ApproveClockInField;
        
        private bool ApproveClockOutField;
        
        private bool ApproveDiscountsField;
        
        private bool ApproveLoyaltyCardsField;
        
        private bool ApproveOrderRequestTimeField;
        
        private bool ApprovePromotionsField;
        
        private bool AssignCharityField;
        
        private bool CanAdjustTipsFromAnyTillField;
        
        private bool CanCloseOrdersAssignedToDeliveryDriversField;
        
        private bool CanOpenAnyDrawerField;
        
        private bool DeleteDepositsField;
        
        private bool DeleteDiscountsField;
        
        private bool DeleteDonationsField;
        
        private bool DeletePaymentsField;
        
        private bool DeletePromotionsField;
        
        private bool DeleteSurchargesField;
        
        private bool ForceAuthorizationField;
        
        private bool ForceReconciliationField;
        
        private bool ManageCashDrawersField;
        
        private bool OverrideDailyLoyaltyCardLimitField;
        
        private bool OverrideMaximumTipPercentField;
        
        private System.Collections.Generic.List<Brink.Api.Settings2.SecurityLevelPermission> PermissionsField;
        
        private bool ReopenOrdersField;
        
        private bool SplitCheckField;
        
        private bool VoidItemsField;
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public bool AddSurcharges
        {
            get
            {
                return this.AddSurchargesField;
            }
            set
            {
                this.AddSurchargesField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public bool AllowExcessEmployeeCardUsage
        {
            get
            {
                return this.AllowExcessEmployeeCardUsageField;
            }
            set
            {
                this.AllowExcessEmployeeCardUsageField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public bool ApproveCheckout
        {
            get
            {
                return this.ApproveCheckoutField;
            }
            set
            {
                this.ApproveCheckoutField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public bool ApproveClockIn
        {
            get
            {
                return this.ApproveClockInField;
            }
            set
            {
                this.ApproveClockInField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public bool ApproveClockOut
        {
            get
            {
                return this.ApproveClockOutField;
            }
            set
            {
                this.ApproveClockOutField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public bool ApproveDiscounts
        {
            get
            {
                return this.ApproveDiscountsField;
            }
            set
            {
                this.ApproveDiscountsField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public bool ApproveLoyaltyCards
        {
            get
            {
                return this.ApproveLoyaltyCardsField;
            }
            set
            {
                this.ApproveLoyaltyCardsField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public bool ApproveOrderRequestTime
        {
            get
            {
                return this.ApproveOrderRequestTimeField;
            }
            set
            {
                this.ApproveOrderRequestTimeField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public bool ApprovePromotions
        {
            get
            {
                return this.ApprovePromotionsField;
            }
            set
            {
                this.ApprovePromotionsField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public bool AssignCharity
        {
            get
            {
                return this.AssignCharityField;
            }
            set
            {
                this.AssignCharityField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public bool CanAdjustTipsFromAnyTill
        {
            get
            {
                return this.CanAdjustTipsFromAnyTillField;
            }
            set
            {
                this.CanAdjustTipsFromAnyTillField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public bool CanCloseOrdersAssignedToDeliveryDrivers
        {
            get
            {
                return this.CanCloseOrdersAssignedToDeliveryDriversField;
            }
            set
            {
                this.CanCloseOrdersAssignedToDeliveryDriversField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public bool CanOpenAnyDrawer
        {
            get
            {
                return this.CanOpenAnyDrawerField;
            }
            set
            {
                this.CanOpenAnyDrawerField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public bool DeleteDeposits
        {
            get
            {
                return this.DeleteDepositsField;
            }
            set
            {
                this.DeleteDepositsField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public bool DeleteDiscounts
        {
            get
            {
                return this.DeleteDiscountsField;
            }
            set
            {
                this.DeleteDiscountsField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public bool DeleteDonations
        {
            get
            {
                return this.DeleteDonationsField;
            }
            set
            {
                this.DeleteDonationsField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public bool DeletePayments
        {
            get
            {
                return this.DeletePaymentsField;
            }
            set
            {
                this.DeletePaymentsField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public bool DeletePromotions
        {
            get
            {
                return this.DeletePromotionsField;
            }
            set
            {
                this.DeletePromotionsField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public bool DeleteSurcharges
        {
            get
            {
                return this.DeleteSurchargesField;
            }
            set
            {
                this.DeleteSurchargesField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public bool ForceAuthorization
        {
            get
            {
                return this.ForceAuthorizationField;
            }
            set
            {
                this.ForceAuthorizationField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public bool ForceReconciliation
        {
            get
            {
                return this.ForceReconciliationField;
            }
            set
            {
                this.ForceReconciliationField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public bool ManageCashDrawers
        {
            get
            {
                return this.ManageCashDrawersField;
            }
            set
            {
                this.ManageCashDrawersField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public bool OverrideDailyLoyaltyCardLimit
        {
            get
            {
                return this.OverrideDailyLoyaltyCardLimitField;
            }
            set
            {
                this.OverrideDailyLoyaltyCardLimitField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public bool OverrideMaximumTipPercent
        {
            get
            {
                return this.OverrideMaximumTipPercentField;
            }
            set
            {
                this.OverrideMaximumTipPercentField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public System.Collections.Generic.List<Brink.Api.Settings2.SecurityLevelPermission> Permissions
        {
            get
            {
                return this.PermissionsField;
            }
            set
            {
                this.PermissionsField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public bool ReopenOrders
        {
            get
            {
                return this.ReopenOrdersField;
            }
            set
            {
                this.ReopenOrdersField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public bool SplitCheck
        {
            get
            {
                return this.SplitCheckField;
            }
            set
            {
                this.SplitCheckField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public bool VoidItems
        {
            get
            {
                return this.VoidItemsField;
            }
            set
            {
                this.VoidItemsField = value;
            }
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.0.1-preview-30310-0943")]
    [System.Runtime.Serialization.DataContractAttribute(Name="JobMenu", Namespace="http://www.brinksoftware.com/webservices/settings/v2")]
    public partial class JobMenu : object
    {
        
        private System.Nullable<byte> DaysField;
        
        private int MenuIdField;
        
        private System.Nullable<System.TimeSpan> StartTimeField;
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public System.Nullable<byte> Days
        {
            get
            {
                return this.DaysField;
            }
            set
            {
                this.DaysField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public int MenuId
        {
            get
            {
                return this.MenuIdField;
            }
            set
            {
                this.MenuIdField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public System.Nullable<System.TimeSpan> StartTime
        {
            get
            {
                return this.StartTimeField;
            }
            set
            {
                this.StartTimeField = value;
            }
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.0.1-preview-30310-0943")]
    [System.Runtime.Serialization.DataContractAttribute(Name="KeyedSettingsObjectSaveResult", Namespace="http://www.brinksoftware.com/webservices/settings/v2")]
    public partial class KeyedSettingsObjectSaveResult : object
    {
        
        private int IdField;
        
        private int OriginalIdField;
        
        private System.Collections.Generic.List<string> ValidationMessagesField;
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public int Id
        {
            get
            {
                return this.IdField;
            }
            set
            {
                this.IdField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public int OriginalId
        {
            get
            {
                return this.OriginalIdField;
            }
            set
            {
                this.OriginalIdField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public System.Collections.Generic.List<string> ValidationMessages
        {
            get
            {
                return this.ValidationMessagesField;
            }
            set
            {
                this.ValidationMessagesField = value;
            }
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.0.1-preview-30310-0943")]
    [System.Runtime.Serialization.DataContractAttribute(Name="GetMenuRequest", Namespace="http://www.brinksoftware.com/webservices/settings/v2")]
    public partial class GetMenuRequest : object
    {
        
        private int MenuIdField;
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public int MenuId
        {
            get
            {
                return this.MenuIdField;
            }
            set
            {
                this.MenuIdField = value;
            }
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.0.1-preview-30310-0943")]
    [System.Runtime.Serialization.DataContractAttribute(Name="SaveEmployeesRequest", Namespace="http://www.brinksoftware.com/webservices/settings/v2")]
    public partial class SaveEmployeesRequest : object
    {
        
        private System.Collections.Generic.List<Brink.Api.Settings2.Employee> EmployeesField;
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public System.Collections.Generic.List<Brink.Api.Settings2.Employee> Employees
        {
            get
            {
                return this.EmployeesField;
            }
            set
            {
                this.EmployeesField = value;
            }
        }
    }
    
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.0.1-preview-30310-0943")]
    [System.ServiceModel.ServiceContractAttribute(Namespace="http://www.brinksoftware.com/webservices/settings/v2", ConfigurationName="Brink.Api.Settings.ISettingsWebService2")]
    public interface ISettingsWebService2
    {
        
        [System.ServiceModel.OperationContractAttribute(Action="http://www.brinksoftware.com/webservices/settings/v2/ISettingsWebService2/GetEmpl" +
            "oyees", ReplyAction="http://www.brinksoftware.com/webservices/settings/v2/ISettingsWebService2/GetEmpl" +
            "oyeesResponse")]
        System.Threading.Tasks.Task<Brink.Api.Settings2.GetEmployeesReply> GetEmployeesAsync();
        
        [System.ServiceModel.OperationContractAttribute(Action="http://www.brinksoftware.com/webservices/settings/v2/ISettingsWebService2/GetFutu" +
            "reOrderingOptions", ReplyAction="http://www.brinksoftware.com/webservices/settings/v2/ISettingsWebService2/GetFutu" +
            "reOrderingOptionsResponse")]
        System.Threading.Tasks.Task<Brink.Api.Settings2.GetFutureOrderingOptionsReply> GetFutureOrderingOptionsAsync();
        
        [System.ServiceModel.OperationContractAttribute(Action="http://www.brinksoftware.com/webservices/settings/v2/ISettingsWebService2/GetJobs" +
            "", ReplyAction="http://www.brinksoftware.com/webservices/settings/v2/ISettingsWebService2/GetJobs" +
            "Response")]
        System.Threading.Tasks.Task<Brink.Api.Settings2.GetJobsReply> GetJobsAsync();
        
        [System.ServiceModel.OperationContractAttribute(Action="http://www.brinksoftware.com/webservices/settings/v2/ISettingsWebService2/GetKitc" +
            "henQueues", ReplyAction="http://www.brinksoftware.com/webservices/settings/v2/ISettingsWebService2/GetKitc" +
            "henQueuesResponse")]
        System.Threading.Tasks.Task<Brink.Api.Settings2.GetKitchenQueuesReply> GetKitchenQueuesAsync();
        
        [System.ServiceModel.OperationContractAttribute(Action="http://www.brinksoftware.com/webservices/settings/v2/ISettingsWebService2/GetMenu" +
            "", ReplyAction="http://www.brinksoftware.com/webservices/settings/v2/ISettingsWebService2/GetMenu" +
            "Response")]
        System.Threading.Tasks.Task<Brink.Api.Settings2.GetMenuReply> GetMenuAsync(Brink.Api.Settings2.GetMenuRequest request);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://www.brinksoftware.com/webservices/settings/v2/ISettingsWebService2/GetOnli" +
            "neOrderingMenuOptions", ReplyAction="http://www.brinksoftware.com/webservices/settings/v2/ISettingsWebService2/GetOnli" +
            "neOrderingMenuOptionsResponse")]
        System.Threading.Tasks.Task<Brink.Api.Settings2.GetOnlineOrderingMenuOptionsReply> GetOnlineOrderingMenuOptionsAsync();
        
        [System.ServiceModel.OperationContractAttribute(Action="http://www.brinksoftware.com/webservices/settings/v2/ISettingsWebService2/GetPerm" +
            "issions", ReplyAction="http://www.brinksoftware.com/webservices/settings/v2/ISettingsWebService2/GetPerm" +
            "issionsResponse")]
        System.Threading.Tasks.Task<Brink.Api.Settings2.GetPermissionsReply> GetPermissionsAsync();
        
        [System.ServiceModel.OperationContractAttribute(Action="http://www.brinksoftware.com/webservices/settings/v2/ISettingsWebService2/GetPric" +
            "eChanges", ReplyAction="http://www.brinksoftware.com/webservices/settings/v2/ISettingsWebService2/GetPric" +
            "eChangesResponse")]
        System.Threading.Tasks.Task<Brink.Api.Settings2.GetPriceChangesReply> GetPriceChangesAsync();
        
        [System.ServiceModel.OperationContractAttribute(Action="http://www.brinksoftware.com/webservices/settings/v2/ISettingsWebService2/GetSecu" +
            "rityLevels", ReplyAction="http://www.brinksoftware.com/webservices/settings/v2/ISettingsWebService2/GetSecu" +
            "rityLevelsResponse")]
        System.Threading.Tasks.Task<Brink.Api.Settings2.GetSecurityLevelsReply> GetSecurityLevelsAsync();
        
        [System.ServiceModel.OperationContractAttribute(Action="http://www.brinksoftware.com/webservices/settings/v2/ISettingsWebService2/SaveEmp" +
            "loyees", ReplyAction="http://www.brinksoftware.com/webservices/settings/v2/ISettingsWebService2/SaveEmp" +
            "loyeesResponse")]
        System.Threading.Tasks.Task<Brink.Api.Settings2.SaveEmployeesReply> SaveEmployeesAsync(Brink.Api.Settings2.SaveEmployeesRequest request);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://www.brinksoftware.com/webservices/settings/v2/ISettingsWebService2/GetLast" +
            "ModifiedTime", ReplyAction="http://www.brinksoftware.com/webservices/settings/v2/ISettingsWebService2/GetLast" +
            "ModifiedTimeResponse")]
        System.Threading.Tasks.Task<Brink.Api.Settings2.GetModifiedTimeReply> GetLastModifiedTimeAsync();
    }
    
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.0.1-preview-30310-0943")]
    public interface ISettingsWebService2Channel : Brink.Api.Settings2.ISettingsWebService2, System.ServiceModel.IClientChannel
    {
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.0.1-preview-30310-0943")]
    public partial class SettingsWebService2Client : System.ServiceModel.ClientBase<Brink.Api.Settings2.ISettingsWebService2>, Brink.Api.Settings2.ISettingsWebService2
    {
        
        /// <summary>
        /// Implement this partial method to configure the service endpoint.
        /// </summary>
        /// <param name="serviceEndpoint">The endpoint to configure</param>
        /// <param name="clientCredentials">The client credentials</param>
        static partial void ConfigureEndpoint(System.ServiceModel.Description.ServiceEndpoint serviceEndpoint, System.ServiceModel.Description.ClientCredentials clientCredentials);
        
        public SettingsWebService2Client() : 
                base(SettingsWebService2Client.GetDefaultBinding(), SettingsWebService2Client.GetDefaultEndpointAddress())
        {
            this.Endpoint.Name = EndpointConfiguration.BasicHttpsBinding_ISettingsWebService2.ToString();
            ConfigureEndpoint(this.Endpoint, this.ClientCredentials);
        }
        
        public SettingsWebService2Client(EndpointConfiguration endpointConfiguration) : 
                base(SettingsWebService2Client.GetBindingForEndpoint(endpointConfiguration), SettingsWebService2Client.GetEndpointAddress(endpointConfiguration))
        {
            this.Endpoint.Name = endpointConfiguration.ToString();
            ConfigureEndpoint(this.Endpoint, this.ClientCredentials);
        }
        
        public SettingsWebService2Client(EndpointConfiguration endpointConfiguration, string remoteAddress) : 
                base(SettingsWebService2Client.GetBindingForEndpoint(endpointConfiguration), new System.ServiceModel.EndpointAddress(remoteAddress))
        {
            this.Endpoint.Name = endpointConfiguration.ToString();
            ConfigureEndpoint(this.Endpoint, this.ClientCredentials);
        }
        
        public SettingsWebService2Client(EndpointConfiguration endpointConfiguration, System.ServiceModel.EndpointAddress remoteAddress) : 
                base(SettingsWebService2Client.GetBindingForEndpoint(endpointConfiguration), remoteAddress)
        {
            this.Endpoint.Name = endpointConfiguration.ToString();
            ConfigureEndpoint(this.Endpoint, this.ClientCredentials);
        }
        
        public SettingsWebService2Client(System.ServiceModel.Channels.Binding binding, System.ServiceModel.EndpointAddress remoteAddress) : 
                base(binding, remoteAddress)
        {
        }
        
        public System.Threading.Tasks.Task<Brink.Api.Settings2.GetEmployeesReply> GetEmployeesAsync()
        {
            return base.Channel.GetEmployeesAsync();
        }
        
        public System.Threading.Tasks.Task<Brink.Api.Settings2.GetFutureOrderingOptionsReply> GetFutureOrderingOptionsAsync()
        {
            return base.Channel.GetFutureOrderingOptionsAsync();
        }
        
        public System.Threading.Tasks.Task<Brink.Api.Settings2.GetJobsReply> GetJobsAsync()
        {
            return base.Channel.GetJobsAsync();
        }
        
        public System.Threading.Tasks.Task<Brink.Api.Settings2.GetKitchenQueuesReply> GetKitchenQueuesAsync()
        {
            return base.Channel.GetKitchenQueuesAsync();
        }
        
        public System.Threading.Tasks.Task<Brink.Api.Settings2.GetMenuReply> GetMenuAsync(Brink.Api.Settings2.GetMenuRequest request)
        {
            return base.Channel.GetMenuAsync(request);
        }
        
        public System.Threading.Tasks.Task<Brink.Api.Settings2.GetOnlineOrderingMenuOptionsReply> GetOnlineOrderingMenuOptionsAsync()
        {
            return base.Channel.GetOnlineOrderingMenuOptionsAsync();
        }
        
        public System.Threading.Tasks.Task<Brink.Api.Settings2.GetPermissionsReply> GetPermissionsAsync()
        {
            return base.Channel.GetPermissionsAsync();
        }
        
        public System.Threading.Tasks.Task<Brink.Api.Settings2.GetPriceChangesReply> GetPriceChangesAsync()
        {
            return base.Channel.GetPriceChangesAsync();
        }
        
        public System.Threading.Tasks.Task<Brink.Api.Settings2.GetSecurityLevelsReply> GetSecurityLevelsAsync()
        {
            return base.Channel.GetSecurityLevelsAsync();
        }
        
        public System.Threading.Tasks.Task<Brink.Api.Settings2.SaveEmployeesReply> SaveEmployeesAsync(Brink.Api.Settings2.SaveEmployeesRequest request)
        {
            return base.Channel.SaveEmployeesAsync(request);
        }
        
        public System.Threading.Tasks.Task<Brink.Api.Settings2.GetModifiedTimeReply> GetLastModifiedTimeAsync()
        {
            return base.Channel.GetLastModifiedTimeAsync();
        }
        
        public virtual System.Threading.Tasks.Task OpenAsync()
        {
            return System.Threading.Tasks.Task.Factory.FromAsync(((System.ServiceModel.ICommunicationObject)(this)).BeginOpen(null, null), new System.Action<System.IAsyncResult>(((System.ServiceModel.ICommunicationObject)(this)).EndOpen));
        }
        
        public virtual System.Threading.Tasks.Task CloseAsync()
        {
            return System.Threading.Tasks.Task.Factory.FromAsync(((System.ServiceModel.ICommunicationObject)(this)).BeginClose(null, null), new System.Action<System.IAsyncResult>(((System.ServiceModel.ICommunicationObject)(this)).EndClose));
        }
        
        private static System.ServiceModel.Channels.Binding GetBindingForEndpoint(EndpointConfiguration endpointConfiguration)
        {
            if ((endpointConfiguration == EndpointConfiguration.BasicHttpsBinding_ISettingsWebService2))
            {
                System.ServiceModel.BasicHttpsBinding result = new System.ServiceModel.BasicHttpsBinding();
                result.MaxBufferSize = int.MaxValue;
                result.ReaderQuotas = System.Xml.XmlDictionaryReaderQuotas.Max;
                result.MaxReceivedMessageSize = int.MaxValue;
                result.AllowCookies = true;
                return result;
            }
            throw new System.InvalidOperationException(string.Format("Could not find endpoint with name \'{0}\'.", endpointConfiguration));
        }
        
        private static System.ServiceModel.EndpointAddress GetEndpointAddress(EndpointConfiguration endpointConfiguration)
        {
            if ((endpointConfiguration == EndpointConfiguration.BasicHttpsBinding_ISettingsWebService2))
            {
                return new System.ServiceModel.EndpointAddress("https://api2.brinkpos.net/Settings2.svc");
            }
            throw new System.InvalidOperationException(string.Format("Could not find endpoint with name \'{0}\'.", endpointConfiguration));
        }
        
        public static System.ServiceModel.Channels.Binding GetDefaultBinding()
        {
            return SettingsWebService2Client.GetBindingForEndpoint(EndpointConfiguration.BasicHttpsBinding_ISettingsWebService2);
        }
        
        private static System.ServiceModel.EndpointAddress GetDefaultEndpointAddress()
        {
            return SettingsWebService2Client.GetEndpointAddress(EndpointConfiguration.BasicHttpsBinding_ISettingsWebService2);
        }
        
        public enum EndpointConfiguration
        {
            
            BasicHttpsBinding_ISettingsWebService2,
        }
    }
}
