﻿using Payroll.Shared;
using Serilog;
using System;
using System.Collections.Generic;
using System.Linq;

namespace Brink.Shared
{
    public static class BrinkConfig
    {
        private const string SettingSection = "brink";
        public const string LocationSection = "brink_location_tokens";
        private const string LocationGuids = "brink_location_guids";
        private const string ManagerSection = "brink_manager_jobs";
        private const string SkipWageSyncSection = "skip_wage_sync";
        private static readonly bool _betaMode = false;
        private static readonly bool _excludeManagerPunches = false;
        
        // in repair mode we do NOT terminate employees and we never change a payrate to be lower then it is today
        private static readonly bool _repairMode = false;

        private static readonly int _pinLength = 4;

        static BrinkConfig()
        {
            Setting.Init();

            {
                var bmode = Setting.Get(SettingSection, "beta");
                _betaMode = (bmode == "1" || bmode == "true");
            }

            {
                // in repair mode we do NOT terminate employees and we never change a payrate
                // to be lower then it is today. This defaults to OFF.
                var rmode = Setting.Get(SettingSection, "repair");
                if (rmode == "1" || rmode == "on")
                    _repairMode = true;
            }

            var mgrPunches = Setting.Get(SettingSection, "mgrpunch");
            _excludeManagerPunches = (mgrPunches == "exclude");

            var pinLen = Setting.Get(SettingSection, "pin_length");
            if (!string.IsNullOrEmpty(pinLen))
                _pinLength = Convert.ToInt32(pinLen);
        }

        public static string TokenForLocation(string location)
        {
            return Setting.Get(LocationSection, location);
        }

        public static bool ExcludeManagerPunches()
        {
            return _excludeManagerPunches;
        }

        public static int PinLength()
        {
            return _pinLength;
        }

        public static bool RepairMode()
        {
            return _repairMode;
        }

        public static Guid GuidForLocation(string location)
        {
            var setting = Setting.Get("brink_location_guids", location);
            if (string.IsNullOrEmpty(setting)) return Guid.Empty;
            return new Guid(setting);
        }

        public static string LocationCodeFromEnv()
        {
            return Environment.GetEnvironmentVariable("BRINK_LOCATION_CODE");
        }

        public static string Host()
        {
            var host = Setting.Get(SettingSection, "host");
            if (!string.IsNullOrEmpty(host)) return host;
            return Environment.GetEnvironmentVariable("BRINK_HOST");
        }

        public static List<string> ManagerJobIds()
        {
            var managerJobs = new List<string>();
            var section = Setting.ListSection(ManagerSection);
            if (section == null) return managerJobs;
            return section.Keys.ToList();
        }

        public static List<string> SkipWageSyncJobIds()
        {
            var skipWageSyncJobs = new List<string>();
            var section = Setting.ListSection(SkipWageSyncSection);
            if (section == null) return skipWageSyncJobs;
            return section.Keys.ToList();
        }

        public static string SettingsUrl()
        {
            var host = Host();
            return $"https://{host}/Settings.svc";
        }

        public static string Settings2Url()
        {
            var host = Host();
            return $"https://{host}/Settings2.svc";
        }

        public static string Labor2Url()
        {
            var host = Host();
            return $"https://{host}/Labor2.svc";
        }

        public static string AccessToken()
        {
            var token = Setting.Get(SettingSection, "token");
            if (!string.IsNullOrEmpty(token)) return token;
            return Environment.GetEnvironmentVariable("BRINK_ACCESS_TOKEN");
        }

        public static Dictionary<string, string> RestaurantsByGuid()
        {
            var map = new Dictionary<string, string>();
            var section = Setting.ListSection(LocationGuids);

            foreach (var item in section)
            {
                map.Add(item.Value, item.Key);
            }

            return map;
        }

        public static bool IsLocationEnabled(string location)
        {
            var token = TokenForLocation(location);

            if (string.IsNullOrEmpty(token))
            {
                Log.Logger.Debug("Skipping location {location}, no location token found", location);
                return false;
            }

            // in production mode, locations must be excluded to skip them
            if (!BetaMode())
            {
                var excludedLocation = Setting.Get("brink_location_exclude", location);
                if (!string.IsNullOrEmpty(excludedLocation))
                {
                    Log.Logger.Debug("Skipping location {location}, found in brink_location_exclude blacklist", location);
                    return false;
                }
            }
            // in beta mode, locations must be INCLUDED 
            else
            {
                var enabledLocation = Setting.Get("brink_location_include", location);
                if (string.IsNullOrEmpty(enabledLocation))
                {
                    Log.Logger.Debug("Skipping location {location}, not in brink_location_include whitelist", location);
                    return false;
                }
            }

            return true;
        }

        public static bool DebugMode()
        {
            return !string.IsNullOrEmpty(Environment.GetEnvironmentVariable("DEBUG"));
        }

        public static bool BetaMode()
        {
            return _betaMode;
        }
    }
}
