﻿using System;
using System.IO;
using System.Linq;
using System.Collections.Generic;
using System.ServiceModel;
using System.ServiceModel.Channels;
using Brink.Api.Settings2;
using Newtonsoft.Json;
using Payroll.Shared;
using Brink.Api.Labor;
using Brink.Api.Settings;
using TimeZoneConverter;
using Serilog;
using Employee = Payroll.Shared.Employee;

namespace Brink.Shared
{
    public class LocationKey
    {
        public string Name { get; set; }
        public string Token { get; set; }
    }

    public class BrinkService
    {
        readonly Random RandomNumberGenerator = new Random();

        private string CurrentTimeZoneIanaName { get; set; }
        private string CurrentTimeZoneCode { get; set; }
        private Dictionary<int, Api.Settings2.Job> JobDirectory { get; set; }
        private Dictionary<string, Api.Settings2.Job> JobsByExportCode { get; set; }
        private Dictionary<int, Api.Settings2.Employee> EmployeeDirectory { get; set; }
        private List<string> ManagerJobIds { get; set; }
        private List<string> SkipWageSyncJobIds { get; set; }

        private bool _initialized = false;
        private string ConfigLocation { get; set; }

        public BrinkService(string locationCode = null)
        {
            ConfigLocation = locationCode ?? BrinkConfig.LocationCodeFromEnv();
        }

        public void InitializeDirectories()
        {
            if (_initialized) return;

            CurrentTimeZoneIanaName = IanaNameForLocation();
            CurrentTimeZoneCode = TimeZoneFromIanaName(CurrentTimeZoneIanaName);

            EmployeeDirectory = SyncableBrinkEmployees();
            JobDirectory = SyncableBrinkJobs();

            JobsByExportCode = JobDirectory.Values.ToDictionary(job => job.ExportCode);
            ManagerJobIds = BrinkConfig.ManagerJobIds();
            SkipWageSyncJobIds = BrinkConfig.SkipWageSyncJobIds();

            Log.Logger.Debug("Using LocationCode: {Code}", ConfigLocation);
            _initialized = true;
        }

        private void SetAccessTokensForDefaultLocation()
        {
            var locationToken = BrinkConfig.TokenForLocation(ConfigLocation);
            SetAccessTokensForLocation(locationToken);
        }

        private void SetAccessTokensForLocation(string locationToken)
        {
            Log.Logger.Debug("Location {loc}, location token: {at}, api key: {lkey}", 
                ConfigLocation, locationToken, BrinkConfig.AccessToken());
            HttpRequestMessageProperty requestMessageProperty;
            if (!OperationContext.Current.OutgoingMessageProperties.ContainsKey(HttpRequestMessageProperty.Name))
            {
                requestMessageProperty = new HttpRequestMessageProperty();
                OperationContext.Current.OutgoingMessageProperties[HttpRequestMessageProperty.Name] = requestMessageProperty;
            }
            else
            {
                requestMessageProperty = (HttpRequestMessageProperty)OperationContext.Current.OutgoingMessageProperties[HttpRequestMessageProperty.Name];
            }

            requestMessageProperty.Headers["AccessToken"] = BrinkConfig.AccessToken();
            requestMessageProperty.Headers["LocationToken"] = locationToken;
        }

        private Dictionary<int, Api.Settings2.Job> SyncableBrinkJobs()
        {
            return BrinkJobsTakeWhile(x => !string.IsNullOrEmpty(x.ExportCode));
        }

        public Dictionary<int, Api.Settings2.Job> AllBrinkJobs()
        {
            return BrinkJobsTakeWhile();
        }

        private Dictionary<int, Api.Settings2.Job> BrinkJobsTakeWhile(Predicate<Api.Settings2.Job> predicate = null)
        {
            var jobs = new Dictionary<int, Api.Settings2.Job>();
            var endptConfig = SettingsWebService2Client.EndpointConfiguration.BasicHttpsBinding_ISettingsWebService2;
            var service = new SettingsWebService2Client(endptConfig, BrinkConfig.Settings2Url());

            using (OperationContextScope scope = new OperationContextScope(service.InnerChannel))
            {
                SetAccessTokensForDefaultLocation();
                var brinkJobs = service.GetJobsAsync().Result;

                if (brinkJobs != null && brinkJobs.Collection != null)
                {
                    foreach (var job in brinkJobs.Collection)
                    {
                        if (predicate == null || predicate(job))
                            jobs.Add(job.Id, job);
                    }

                    Log.Logger.Debug("Fetched {brinkJobsCollectionCount} jobs, found {jobsCount} connected jobs", brinkJobs.Collection.Count, jobs.Count());
                }
            }

            service.CloseAsync().Wait();
            return jobs;
        }

        public string TimeZoneFromIanaName(string ianaCode)
        {
            switch (ianaCode)
            {
                case "America/New_York":
                    return "EST";
                case "America/Chicago":
                    return "CST";
                case "America/Denver":
                    return "MST";
                case "America/Los_Angeles":
                    return "PST";
                case "America/Phoenix":
                    return "MST";
            }

            return ianaCode;
        }

        public string IanaNameForLocation()
        {
            try
            {
                var endptConfig = SettingsWebServiceClient.EndpointConfiguration.BasicHttpsBinding_ISettingsWebService;
                var service = new SettingsWebServiceClient(endptConfig, BrinkConfig.SettingsUrl());

                using (OperationContextScope scope = new OperationContextScope(service.InnerChannel))
                {
                    SetAccessTokensForDefaultLocation();
                    var locationToken = BrinkConfig.TokenForLocation(ConfigLocation);
                    var options = service.GetOptionsAsync(BrinkConfig.AccessToken(), locationToken).Result;
                    var timezone = options.Location.TimeZone.ToString();

                    service.CloseAsync().Wait();

                    if (timezone == "Arizona")
                    {
                        return TZConvert.WindowsToIana("US Mountain Standard Time");
                    }

                    var tzFull = $"{timezone} Standard Time";
                    return TZConvert.WindowsToIana(tzFull);
                }
            }
            catch (Exception e)
            {
                Log.Logger.Error("Endpoint: {endpt}", BrinkConfig.SettingsUrl());
                Log.Logger.Error(e.Message);
                return string.Empty;
            }
        }

        private Dictionary<int, Api.Settings2.Employee> BrinkEmployeesTakeWhile(Predicate<Api.Settings2.Employee> matchFunc, bool warnOnSkips = false)
        {
            var syncEmployees = new Dictionary<int, Api.Settings2.Employee>();
            var endptConfig = SettingsWebService2Client.EndpointConfiguration.BasicHttpsBinding_ISettingsWebService2;
            var service = new SettingsWebService2Client(endptConfig, BrinkConfig.Settings2Url());

            using (var scope = new OperationContextScope(service.InnerChannel))
            {
                // the "1" below is a placeholder, until we flesh this out...
                SetAccessTokensForDefaultLocation();

                Log.Logger.Debug("Fetching employees from {endpt} for {loc}", BrinkConfig.Settings2Url(), ConfigLocation);
                var brinkEmployees = service.GetEmployeesAsync().Result;
                if (brinkEmployees.ResultCode != 0)
                {
                    Log.Logger.Fatal("Failed to connect to Brink POS, {msg}", brinkEmployees.Message);
                    Log.Logger.Debug("Token: {token}, Location: {location}",
                        BrinkConfig.AccessToken(), BrinkConfig.TokenForLocation(ConfigLocation));
                }

                if (brinkEmployees.Collection != null)
                { 
                    foreach (var employee in brinkEmployees.Collection)
                    {
                        if (matchFunc == null || matchFunc(employee))
                            syncEmployees.Add(employee.Id, employee);
                        else
                        {
                            if (warnOnSkips)
                                Log.Logger.Warning("Skipping: {eid} {fn} {ln}", employee.Id, employee.FirstName, employee.LastName);
                        }
                    }

                    Log.Logger.Debug("Fetched {count1} employees, found {count2} connected employees", brinkEmployees.Collection.Count, syncEmployees.Count());
                }
            }

            service.CloseAsync().Wait();
            return syncEmployees;
        }

        public Dictionary<int, Api.Settings2.Employee> SyncableBrinkEmployees(bool warnOnSkips = false)
        {
            return BrinkEmployeesTakeWhile(x => !string.IsNullOrEmpty(x.PayrollId), warnOnSkips);
        }

        public IEnumerable<Employee> EmployeesTakeWhile(Predicate<Api.Settings2.Employee> matchFunc)
        {
            var jobs = BrinkJobsTakeWhile();
            var brinkEmployees = BrinkEmployeesTakeWhile(matchFunc);
            var employees = new List<Employee>();

            foreach (var brinkEmployee in brinkEmployees.Values)
            {
                var employee = new Employee()
                {
                    FirstName = brinkEmployee.FirstName,
                    LastName = brinkEmployee.LastName,
                    Id = brinkEmployee.Id.ToString(),
                    CellPhone = brinkEmployee.CellPhone,
                    WorkEmail = brinkEmployee.EmailAddress,
                    StreetAddress = brinkEmployee.Address1,
                    CityAddress = brinkEmployee.City,
                    State = brinkEmployee.State,
                    Zip = brinkEmployee.Zip,
                    ClockSeq = brinkEmployee.PayrollId,
                    Active = !brinkEmployee.Terminated,
                    HireDate = brinkEmployee.HireDate.GetValueOrDefault(DateTime.MinValue),
                    TermDate = brinkEmployee.TerminationDate.GetValueOrDefault(DateTime.MinValue),
                };

                employee.PrimaryWorkLocation = brinkEmployee.HomeLocationId?.ToString();

                foreach (var employeeJob in brinkEmployee.Jobs)
                {
                    if (jobs.ContainsKey(employeeJob.JobId))
                    {
                        var thisJob = jobs[employeeJob.JobId];
                        employee.Jobs.Add(new Payroll.Shared.Job()
                        {
                            Name = thisJob.Name,
                            Code = thisJob.ExportCode,
                            Rate = employeeJob.PayRate
                        });
                    }
                    else
                    {
                        Log.Logger.Warning("Adding unknown job {jid} to employee {id}", employeeJob.Id, employee.Id);
                        employee.Jobs.Add(new Payroll.Shared.Job()
                        {
                            Name = employeeJob.JobId.ToString(),
                            Code = employeeJob.Id.ToString(),
                            Rate = employeeJob.PayRate
                        });
                    }
                }

                employees.Add(employee);
            }

            return employees;
        }

        public IEnumerable<Payroll.Shared.Employee> SyncableEmployees()
        {
            return EmployeesTakeWhile(x => x.Terminated == false && !string.IsNullOrEmpty(x.PayrollId));
        }

        public IEnumerable<Payroll.Shared.Employee> AllActiveEmployees()
        {
            return EmployeesTakeWhile(x => x.Terminated == false);
        }

        public IEnumerable<Payroll.Shared.Employee> AllTerminatedEmployees()
        {
            return EmployeesTakeWhile(x => x.Terminated == true);
        }

        public List<SecurityLevel> AllLevels()
        {
            var listOfLevels = new List<SecurityLevel>();
            var endptConfig = SettingsWebService2Client.EndpointConfiguration.BasicHttpsBinding_ISettingsWebService2;
            var service = new SettingsWebService2Client(endptConfig, BrinkConfig.Settings2Url());

            using (OperationContextScope scope = new OperationContextScope(service.InnerChannel))
            {
                // the "1" below is a placeholder, until we flesh this out...
                SetAccessTokensForDefaultLocation();

                Log.Logger.Debug("Fetching security levels from {endpt}", BrinkConfig.Settings2Url());
                var levels = service.GetSecurityLevelsAsync().Result;

                if (levels.ResultCode != 0)
                {
                    Log.Logger.Fatal("Failed to connect to Brink POS, {msg}", levels.Message);
                }

                if (levels.Collection != null)
                    listOfLevels = levels.Collection;
            }

            service.CloseAsync().Wait();
            return listOfLevels;
        }

        public List<Permission> AllPermissions()
        {
            var listOfPermissions = new List<Permission>();
            var endptConfig = SettingsWebService2Client.EndpointConfiguration.BasicHttpsBinding_ISettingsWebService2;
            var service = new SettingsWebService2Client(endptConfig, BrinkConfig.Settings2Url());

            using (OperationContextScope scope = new OperationContextScope(service.InnerChannel))
            {
                // the "1" below is a placeholder, until we flesh this out...
                SetAccessTokensForDefaultLocation();

                Log.Logger.Debug("Fetching permission levels from {endpt}", BrinkConfig.Settings2Url());
                var perms = service.GetPermissionsAsync().Result;

                if (perms.ResultCode != 0)
                {
                    Log.Logger.Fatal("Failed to connect to Brink POS, {msg}", perms.Message);
                }

                if (perms.Collection != null)
                    listOfPermissions = perms.Collection;
            }

            service.CloseAsync().Wait();
            return listOfPermissions;
        }

        // this is a transition method for older code, remove when no longer needed
        public List<PunchPair> TimePunches(DateTime businessDate)
        {
            return TimePunches(BrinkConfig.LocationCodeFromEnv(), businessDate);
        }

        public List<PunchPair> TimePunches(string locationCode, DateTime businessDate)
        {
            InitializeDirectories();
            var excludeManagerPunches = BrinkConfig.ExcludeManagerPunches();
            var punches = new List<PunchPair>();

            Log.Logger.Information("Fetching punches for {location} on {businessDate}...", 
                locationCode, businessDate);

            // call service and convert brink objects to punch pairs...
            var endptConfig = LaborWebService2Client.EndpointConfiguration.BasicHttpsBinding_ILaborWebService2;
            var service = new LaborWebService2Client(endptConfig, BrinkConfig.Labor2Url());

            using (var scope = new OperationContextScope(service.InnerChannel))
            {
                // the "1" below is a placeholder, until we flesh this out...
                SetAccessTokensForDefaultLocation();

                var request = new GetShiftsRequest()
                {
                    BusinessDate = businessDate
                };

                var shifts = service.GetShiftsAsync(request).Result;

                Log.Logger.Debug(JsonConvert.SerializeObject(request));
                Log.Logger.Debug(JsonConvert.SerializeObject(shifts));

                if (shifts.ResultCode != 0)
                {
                    Log.Logger.Error("GetShiftsAsync returned error code {rc}", shifts.ResultCode);
                }

                if (shifts != null)
                {
                    TimeZoneInfo tZone;
                    if (string.IsNullOrEmpty(CurrentTimeZoneIanaName))
                    {
                        tZone = TimeZoneInfo.Local;
                        Log.Logger.Warning("Null CurrentTimeZoneIanaName, using local time zone {tz}", tZone.DisplayName);
                    }
                    else tZone = TimeZoneInfo.FindSystemTimeZoneById(CurrentTimeZoneIanaName);

                    foreach (var shift in shifts.Shifts)
                    {
                        // is this employee someone who can be synced
                        if (!EmployeeDirectory.TryGetValue(shift.EmployeeId, out Api.Settings2.Employee employee))
                        {
                            Log.Logger.Warning($"Skipping punch record for employee {shift.EmployeeId} (shift #: {shift.Number}, job: {shift.JobId}) no payroll id");
                            continue;
                        }

                        // is this a job we can sync
                        var jobExportCode = string.Empty;
                        if (JobDirectory.TryGetValue(shift.JobId, out Api.Settings2.Job job))
                        {
                            jobExportCode = job.ExportCode;
                            if (string.IsNullOrEmpty(jobExportCode))
                            {
                                Log.Logger.Warning($"WARNING: Skipping punch record for employee {shift.EmployeeId} for job {shift.JobId}, no export code on job");
                                continue;
                            }

                            // skip manager job time punches if desired
                            if (excludeManagerPunches && ManagerJobIds.Contains(jobExportCode))
                            {
                                Log.Logger.Information($"WARNING: Skipping manager job punch record for employee {shift.EmployeeId} for job {shift.JobId}");
                                continue;
                            }
                        }
                        else
                        {
                            // for debugging purposes, at least put the job id here
                            if (BrinkConfig.DebugMode())
                                jobExportCode = $"NOTFOUND-{shift.JobId}";
                        }

                        DateTime timeIn = TimeZoneInfo.ConvertTimeFromUtc(shift.StartTime.DateTime, tZone);
                        DateTime timeOut = TimeZoneInfo.ConvertTimeFromUtc(shift.EndTime.DateTime, tZone);

                        // this is a Modern Market request, hopefully useful to others
                        // skip time in/outs that are less than a minute
                        var timeDiff = timeOut - timeIn;
                        if (timeDiff.TotalMinutes < 1) continue;

                        var tc = new PunchPair
                        {
                            ClockSeq = employee.PayrollId,
                            JobCode = jobExportCode,
                            TimeIn = shift.StartTime.DateTime,
                            TimeOut = shift.EndTime.DateTime,
                            CashTip = shift.DeclaredTips,
                            Date = shift.BusinessDate,
                            Description = "BRINK",
                            //Hours = shift.MinutesWorked / 60,
                            Rate = shift.PayRate,
                            Location = locationCode,
                            TimeZone = CurrentTimeZoneCode
                        };

                        // add breaks if present
                        if (shift.Breaks != null)
                        {
                            foreach (var shiftBreak in shift.Breaks)
                            {
                                tc.Breaks.Add(new Payroll.Shared.Break()
                                {
                                    TimeIn = shiftBreak.StartTime.DateTime,
                                    TimeOut = shiftBreak.EndTime.DateTime,
                                    Paid = shiftBreak.IsPaid
                                });
                            }
                        }

                        punches.Add(tc);
                    }
                }
            }

            Log.Logger.Debug($"  Retrieved {punches.Count} punches.");

            service.CloseAsync().Wait();
            return punches;
        }

        public void UpdateBrinkEmployeeRecord(Api.Settings2.Employee brinkEmployee, Payroll.Shared.Employee employee)
        {
            // if employee no longer active, we don't update anything else...
            if (!employee.Active)
            {
                if (BrinkConfig.RepairMode())
                {
                    Log.Logger.Information("  Repair Mode: skipping termination of employee {eid}", employee.Id);
                    return;
                }

                brinkEmployee.Terminated = true;
                if (employee.TermDate != DateTime.MinValue)
                {
                    Log.Logger.Information("  Terminating employee {eid}, setting termination date to {date}", employee.Id, employee.TermDate);
                    brinkEmployee.TerminationDate = employee.TermDate;
                }
                else
                {
                    Log.Logger.Warning("  Terminating employee {eid}, no termination date provided, using today as termination date.", employee.Id);
                    brinkEmployee.TerminationDate = DateTime.Now;
                }

                return;
            }

            InitializeDirectories();

            Log.Logger.Debug(JsonConvert.SerializeObject(brinkEmployee));

            if (IsUpdatedProperty("Address1", brinkEmployee.Address1, employee.StreetAddress))
            {
                brinkEmployee.Address1 = employee.StreetAddress;
            }

            if (IsUpdatedProperty("City", brinkEmployee.City, employee.CityAddress))
            {
                brinkEmployee.City = employee.CityAddress;
            }

            if (IsUpdatedProperty("State", brinkEmployee.State, employee.State))
            {
                brinkEmployee.State = employee.State;
            }

            if (IsUpdatedProperty("Zip", brinkEmployee.Zip, employee.Zip))
            {
                brinkEmployee.Zip = employee.Zip;
            }

            if (IsUpdatedProperty("FirstName", brinkEmployee.FirstName, employee.FirstName))
            {
                brinkEmployee.FirstName = employee.FirstName;
            }

            if (IsUpdatedProperty("LastName", brinkEmployee.LastName, employee.LastName))
            {
                brinkEmployee.LastName = employee.LastName;
                brinkEmployee.DisplayName = employee.LastName;
            }

            if (IsUpdatedProperty("Phone", brinkEmployee.HomePhone, employee.CellPhone))
            {
                brinkEmployee.HomePhone = employee.CellPhone;
            }

            if (IsUpdatedProperty("EmailAddress", brinkEmployee.EmailAddress, employee.WorkEmail))
            {
                brinkEmployee.EmailAddress = employee.WorkEmail;
            }

            if (IsUpdatedProperty("HireDate", brinkEmployee.HireDate, employee.HireDate))
            {
                brinkEmployee.HireDate = employee.HireDate;
            }

            if (IsUpdatedProperty("DOB", brinkEmployee.BirthDate, employee.DateOfBirth))
            {
                brinkEmployee.BirthDate = employee.DateOfBirth;
            }

            UpdateBrinkEmployeeJobs(brinkEmployee, employee);
        }

        public void UpdateBrinkEmployeeJobs(Api.Settings2.Employee brinkEmployee, Payroll.Shared.Employee employee)
        {
            var bJobs = brinkEmployee.Jobs.ToDictionary(x => x.JobId);
            var vLocations = new List<string>();

            Log.Logger.Debug(JsonConvert.SerializeObject(bJobs, Formatting.Indented));

            // pass 1 - first add location specific rates
            var locationJobs = new SortedDictionary<string, Payroll.Shared.Job>();
            foreach (var job in employee.Jobs)
            {
                if (string.IsNullOrEmpty(job.Code))
                {
                    Log.Logger.Warning("Skipping {jobName} with rate {jobRate} as it does not have a job code", job.Name, job.Rate);
                    continue;
                }

                // is this a location specific job/rate?
                if (string.IsNullOrEmpty(job.Location))
                    continue;

                // a job for location we are processing?
                if (job.Location != ConfigLocation)
                {
                    Log.Logger.Debug("Skipping job for alternate location {job}", JsonConvert.SerializeObject(job));
                    continue;
                }

                // have we seen already?
                if (locationJobs.ContainsKey(job.Code))
                {
                    Log.Logger.Error("Duplicate default job rate for {job}", JsonConvert.SerializeObject(job));
                    continue;
                }

                locationJobs.Add(job.Code, job);
            }

            // pass 2 - add default jobs
            foreach (var job in employee.Jobs)
            {
                if (string.IsNullOrEmpty(job.Code))
                {
                    Log.Logger.Warning("Skipping {jobName} with rate {jobRate} as it does not have a job code", job.Name, job.Rate);
                    continue;
                }

                if (!string.IsNullOrEmpty(job.Location)) continue;

                if (locationJobs.ContainsKey(job.Code))
                {
                    Log.Logger.Information("Skipping default job rate for {job}", JsonConvert.SerializeObject(job));
                    continue;
                }

                locationJobs.Add(job.Code, job);
            }

            foreach (var job in locationJobs.Values)
            {
                // is this a job we can sync?
                string jobExportCode = String.Empty;
                if (!JobsByExportCode.TryGetValue(job.Code, out Api.Settings2.Job brinkJob))
                {
                    Log.Logger.Warning("Skipping {jobName}, invalid export code {jobCode}", job.Name, job.Code);
                    continue;
                }

                // manager jobs don't have their rates synchronized
                // also jobs in the skip_wage_sync don't have their rates synchronized
                var isManagerJob = ManagerJobIds.Contains(brinkJob.ExportCode);
                var isSkipWageJob = SkipWageSyncJobIds.Contains(brinkJob.ExportCode);
                var nonSensitiveJobRate = (isManagerJob || isSkipWageJob) ? 0 : job.Rate;

                var empJob = new Api.Settings2.EmployeeJob()
                {
                    PayRate = nonSensitiveJobRate,
                    JobId = brinkJob.Id,
                    SecurityLevelId = MapJobLevelToBrinkSecurityId(job.JobLevel),
                    Id = -RandomNumberGenerator.Next()
                };

                if (!bJobs.ContainsKey(brinkJob.Id))
                {
                    Log.Logger.Information("  Adding job '{brinkJobName}', seclevel: {level}, export code: {brinkJobExportCode}, rate: {rate}",
                        brinkJob.Name, empJob.SecurityLevelId, brinkJob.ExportCode, nonSensitiveJobRate);
                    bJobs.Add(brinkJob.Id, empJob);
                }
                else
                {
                    // are the rates different?
                    if (bJobs[brinkJob.Id].PayRate != nonSensitiveJobRate)
                    {
                        // if the new pay rate is lower than the current rate and we are in repair mode
                        // skip this somewhat suspicious update requests
                        if (bJobs[brinkJob.Id].PayRate > nonSensitiveJobRate && BrinkConfig.RepairMode())
                        {
                            Log.Logger.Information("  Repair Mode: Not updating payrate on existing job '{brinkJobName}', export code: {brinkJobExportCode}. Was {oldrate}, now {newrate}",
                                brinkJob.Name, brinkJob.ExportCode, bJobs[brinkJob.Id].PayRate, nonSensitiveJobRate);
                        }
                        else
                        {
                            Log.Logger.Information(
                                "  Updating payrate on existing job '{brinkJobName}', export code: {brinkJobExportCode}. Was {oldrate}, now {newrate}",
                                brinkJob.Name, brinkJob.ExportCode, bJobs[brinkJob.Id].PayRate, nonSensitiveJobRate);
                            bJobs[brinkJob.Id].PayRate = nonSensitiveJobRate;
                        }
                    }
                }
            }

            brinkEmployee.Jobs = bJobs.Values.ToList();
        }
        
        private int MapJobLevelToBrinkSecurityId(JobLevel jobLevel)
        {
            /* Id	Name          	ManageCashDrawers
                1	CASHIER        	True
                2	MANAGERS       	True
                7	ONLY CLOCK IN  	False
             */

            // most frequent
            if (jobLevel == JobLevel.None) return 7;

            // cashier
            if (jobLevel == JobLevel.Cashier) return 1;

            // manager
            return 2;
        }

        private bool IsUpdatedProperty(string propName, string x, string y)
        {
            if (string.IsNullOrEmpty(y)) return false;
            if (x == y) return false;

            if (x == null)
                Log.Logger.Information("  Setting {propName} = {newVal}", propName, y);
            else
                Log.Logger.Information("  Setting {propName} from {oldVal} to {newVal}", propName, x, y);

            return true;
        }

        private bool IsUpdatedProperty(string propName, Guid? x, Guid y)
        {
            if (y == Guid.Empty) return false;
            if (x == y) return false;

            Log.Logger.Information("  Setting {propName} from {oldVal} to {newVal}", propName, x, y);
            return true;
        }

        private bool IsUpdatedProperty(string propName, DateTime? x, DateTime y)
        {
            if (y == DateTime.MinValue) return false;
            if (x == y) return false;

            Log.Logger.Information("  Setting {propName} from {oldVal} to {newVal}", propName, x, y);
            return true;
        }

        private bool IsUpdatedProperty(string propName, DateTime? x, DateTime? y)
        {
            if (y == null) return false;
            if (x == y) return false;

            Log.Logger.Information("  Setting {propName} from {oldVal} to {newVal}", propName, x, y);
            return true;
        }

        public bool IsEmployeeSyncable(Payroll.Shared.Employee employee)
        {
            if (string.IsNullOrEmpty(employee.ClockSeq))
            {
                Log.Logger.Error("Non-syncable employee record - empty ClockSeq. Id: {employee.Id}", employee.Id);
                return false;
            }

            if (employee.PrimaryWorkLocation == ConfigLocation) return true;

            var locations = employee.PrimaryWorkLocation;

            foreach (var job in employee.Jobs)
            {
                if (job.Location == ConfigLocation) return true;
                locations += ", " + job.Location;
            }

            Log.Logger.Debug("Non-syncable employee record - no location match. Locations='{locations}', Current location={ConfigLocation}", locations, ConfigLocation);
            return false;
        }

        /*
        Adds or updates one or more employees. An error encountered with a single employee will fail the
        entire operation. It is recommended to always make a single call with all employee updates wherever
        possible, rather than making a single call per employee.

        When adding a new employee, specify a unique negative number for the employee’s Id. This will be
        used to reference that employee for errors and when returning the system assigned id for the new
        employee. To add an employee that already exists in another location to the current location, and to
        have the two employees be associated, specify the system assigned Id returned when the employee was
        added to the original location for the employee’s Id.

        When processing a successful (ResultCode = 0) SaveEmployeeReply, inspect the SaveResults collection
        for any new system-generated unique identifiers created as part of the save operation. When processing
        a reply that failed due to an invalid request (ResultCode = 2), the SaveResults collection will contain
        entries for the employee(s) that failed to validate.
        */
        public bool UpdateEmployees(List<Payroll.Shared.Employee> employees)
        {
            var brinkEmployees = new List<Api.Settings2.Employee>();
            foreach (var employee in employees)
            {
                var brinkEmployee = new Api.Settings2.Employee();
                UpdateBrinkEmployeeRecord(brinkEmployee, employee);
                brinkEmployees.Add(brinkEmployee);
            }

            return UpdateBrinkEmployees(brinkEmployees);
        }

        public bool  UpdateBrinkEmployees(List<Api.Settings2.Employee> employees)
        {
            var endptConfig = SettingsWebService2Client.EndpointConfiguration.BasicHttpsBinding_ISettingsWebService2;
            var service = new SettingsWebService2Client(endptConfig, BrinkConfig.Settings2Url());

            InitializeDirectories();
            
            using (OperationContextScope scope = new OperationContextScope(service.InnerChannel))
            {
                // the "1" below is a placeholder, until we flesh this out...
                SetAccessTokensForDefaultLocation();

                var settings = new JsonSerializerSettings()
                {
                    DefaultValueHandling = DefaultValueHandling.Ignore,
                    NullValueHandling = NullValueHandling.Ignore
                };

                var request = new SaveEmployeesRequest()
                {
                    Employees = employees
                };

                Log.Logger.Debug(JsonConvert.SerializeObject(request, Formatting.Indented));
                var result = service.SaveEmployeesAsync(request).Result;
                Log.Logger.Debug(JsonConvert.SerializeObject(result, Formatting.Indented));

                if (result.ResultCode == 2)
                {
                    Log.Logger.Error(result.Message);
                    Log.Logger.Error(JsonConvert.SerializeObject(result.SaveResults));
                }
            }

            service.CloseAsync().Wait();
            return true;
        }
    }
}

