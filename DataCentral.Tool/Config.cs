using Payroll.Shared;
using System.Linq;
using System.Collections.Generic;
using System;
using System.Collections.Immutable;
using System.Text.Json;
using DataCentral.Tool.Models;
using Serilog; 

namespace DataCentral.Tool
{
    public static class Config
    {
        public static readonly string SettingSection = "dc";
        public static readonly int HireLimit;
        public static readonly int SyncLimit;
        public static readonly int TermLimit;

        public static readonly string ENV_KEY_DC_KEY = "DATA_CENTRAL_KEY";
        public static readonly string POS_LOCATIONS_CACHE_COLLECTION = "pos_locations";
        
        private static readonly string StatusMapSection = "job_status_change_reasons";
        private static readonly string JobNameMapSection = "job_name_map";
        private static readonly string JobMapSection = "jobs_map";
        public static int ApiDelayInSeconds { get; private set; }
        public static bool AdjustForTimeZone { get; private set; }

        public static string Endpoint {get; private set;}
        public static string DC_Key {get; private set;}
        public static string TimecardReport {get; private set;}
        public static string PaymemoReport {get; private set;}
        public static string PtoMemoType { get; private set; }
        public static ExecutionMode HireMode {get; private set;}
        public static ExecutionMode SyncMode {get; private set;}
        public static ExecutionMode TermMode {get; private set;}
        public static ExecutionMode PtoMode { get; private set; }

        static Config()
        {
            Setting.Init();
            DC_Key = Setting.GetFromConfigOrEnv(SettingSection, "dc_key", ENV_KEY_DC_KEY);
            Endpoint = Setting.Get(SettingSection, "endpoint");
            PtoMemoType = Setting.Get(SettingSection, "pto_memo_type");
            TimecardReport = Setting.Get(SettingSection, "timecard_report");
            PaymemoReport = Setting.Get(SettingSection, "payment_report");
            HireMode = Setting.ExecutionMode(SettingSection, "hire_mode");
            SyncMode =Setting.ExecutionMode(SettingSection, "sync_mode");
            TermMode = Setting.ExecutionMode(SettingSection, "term_mode");
            PtoMode = Setting.ExecutionMode(SettingSection, "pto_mode");

            AdjustForTimeZone = Setting.GetBoolean(SettingSection, "adjust_for_timezone");
            {
                string limit = Setting.Get(SettingSection, "hire_limit");
                HireLimit = string.IsNullOrEmpty(limit) ? 0 : int.Parse(limit);
            }

            {
                string limit = Setting.Get(SettingSection, "sync_limit");
                SyncLimit = string.IsNullOrEmpty(limit) ? 0 : int.Parse(limit);
            }

            {
                string limit = Setting.Get(SettingSection, "term_limit");
                TermLimit = string.IsNullOrEmpty(limit) ? 0 : int.Parse(limit);
            }

            {
                var delay = Setting.Get(SettingSection, "delay");
                ApiDelayInSeconds = (string.IsNullOrEmpty(delay)) ? 0 : Convert.ToInt32(delay);
            }
        }

        public static Dictionary<string, string> JobsById()
        {
            var section = Setting.ListSection(JobMapSection);
            var sectionJson = JsonSerializer.Serialize(section);
            //Log.Logger.Debug(sectionJson);
            

            return section;
        }
        public static Dictionary<string, string> JobNameMap()
        {
            var section = Setting.ListSection(JobNameMapSection);
            var sectionJson = JsonSerializer.Serialize(section);
            //Log.Logger.Debug(sectionJson);
            

            return section;
        }
        public static Dictionary<string, string> StatusMap()
        {
            var section = Setting.ListSection(StatusMapSection);
            var sectionJson = JsonSerializer.Serialize(section);
            Log.Logger.Debug(sectionJson);
            

            return section;
        }
    }

}