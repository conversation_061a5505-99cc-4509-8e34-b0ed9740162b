﻿using System;
using System.Collections.Generic;
using Serilog;
using System.Text.Json;
using Payroll.Shared;

namespace DataCentral.Tool
{
    class CacheCommand
    {
        public void Clear(List<string> args)
        {
            if (args == null || args.Count < 1)
            {
                Console.WriteLine("Usage: DataCentral.Tool.exe cache clear [locations]");
                return;
            }

            var scmd = args[0];
            switch (scmd)
            {
                // dont let users just pass any random collection name
                case "locations":
                    CacheService.RemoveRecords<Location>(Config.POS_LOCATIONS_CACHE_COLLECTION);
                    break;
                default:
                    Console.WriteLine("Error: unrecognized cache dump object: {0}", scmd);
                    break;
            }
        }

        private void CacheLocations(IEnumerable<Location> locations)
        {
            CacheService.CacheLastUpdatedTime(DateTime.Now);
            if (locations == null) return;

            try
            {
                using (var cache = CacheService.OpenCache(SystemConfig.CacheName()))
                {
                    var locCache = cache.GetCollection<Location>(Config.POS_LOCATIONS_CACHE_COLLECTION);
                    foreach (var loc in locations)
                    {
                        locCache.Upsert(loc);
                    }

                    locCache.EnsureIndex(x => x.Id);
                }
            }
            catch (Exception e)
            {
                Log.Logger.Fatal(e.Message);
                Log.Logger.Fatal(e.StackTrace);
            }
        }

        public void Init(List<string> args)
        {
            try
            {
                using (var dataCentralService = new DataCentralService())
                {
                    var locations = dataCentralService.GetLocationsAsync().Result;
                    CacheLocations(locations);
                }
            }
            catch (Exception e)
            {
                Log.Logger.Fatal(e.Message);
                Log.Logger.Fatal(e.StackTrace);
            }
        }

        public void Update(List<string> args)
        {
            Init(args);
        }

        // for testing purposes only
        public void Dump(List<string> args)
        {
            try
            {
                using (var cache = CacheService.OpenCache(SystemConfig.CacheName()))
                {
                    var locations = cache.GetCollection<Location>(Config.POS_LOCATIONS_CACHE_COLLECTION).FindAll();
                    var formattedJson = JsonSerializer.Serialize(locations, Json.DefaultSerializerOutputStyle);
                    Console.WriteLine(formattedJson);
                }
            }
            catch (Exception e)
            {
                Log.Logger.Fatal(e.Message);
                Log.Logger.Fatal(e.StackTrace);
            }
        }
    }
}
