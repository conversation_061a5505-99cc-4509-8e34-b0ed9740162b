using System;
using System.Collections.Generic;
using System.Linq;
using Serilog;
using DataCentral.Tool.Models;
using Payroll.Shared;
using System.Text.Json;
using System.Text.Json.Serialization;

namespace DataCentral.Tool;

public class ImportCommand
{
    public void Pto(List<string> args)
    {
        try
        {
            if (!ConsoleService.TryGetPunchesFromInput(out var ptoPunches))
            {
                Log.Logger.Error("Failed to parse PTO punches");
                return;
            }

            if (ptoPunches == null)
            {
                Log.Logger.Error("Failed to load PTO punches");
                return;
            }

            if (args == null || args.Count < 1)
            {
                Console.WriteLine("Usage: DataCentral.Tool.exe import pto <store-number|all>");
                return;
            }

            string unitId = args[0];
            ExecutionMode executionMode = Config.SyncMode;

            if (executionMode == ExecutionMode.Execute)
            {
                // both the config file and the command line must confirm execution
                if (args.Count < 2) executionMode = ExecutionMode.DryRun;
                else if (args[1] != "doit") executionMode = ExecutionMode.DryRun;
            }

            Log.Logger.Information("DataCentral.Tool Mode={mode}, Target={loc}, Op=PTO", executionMode, unitId);
            List<PayMemoTransactionV2> payMemoTransactions = new List<PayMemoTransactionV2>();

            using (var service = new DataCentralService())
            {
                var locations = service.GetLocationsAsync().Result;
                if (unitId != "all") locations.RemoveAll(x => x.Code != unitId);

                var storeCodeToId = locations.ToDictionary(x => x.Code, x => x.Id);
                var employeeDirectory = service.GetAllStoreEmployees(locations).Result;

                foreach (var punch in ptoPunches)
                {
                    if (punch.Hours.Equals(0))
                        punch.Hours = punch.TimeOut.Subtract(punch.TimeIn).Hours;

                    if (!storeCodeToId.TryGetValue(punch.Location, out var locationId))
                    {
                        // these are only debug level because none of these should actually be sync'd
                        Log.Logger.Error("  Failed to locate a store for employee {id}/{cseq} @ store number {rid}", punch.EECode, punch.ClockSeq, punch.Location); 
                        continue;
                    }

                    if (!employeeDirectory.TryGetValue(locationId, out var directoryForLocation))
                    {
                        Log.Logger.Error("  Failed to locate employee directory for store id {rid}", locationId);
                        continue;
                    }

                    if (!directoryForLocation.TryGetValue(punch.ClockSeq, out Models.StoreEmployee employee))
                    {
                        Log.Logger.Error("  Failed to locate employee {id}/{cseq} @ {rid}", punch.EECode, punch.ClockSeq, locationId);
                        continue;
                    }

                    Log.Logger.Information("Importing {x} hours of PTO for {y} on {z}", punch.Hours, punch.EECode, punch.TimeIn);
                    PayMemoTransactionV2 payMemo = Converter.PunchToPayMemoTransaction(punch);
                    if (Guid.TryParse(employee.GlobalEmployeeId, out Guid employeeGuid))
                    {
                        payMemo.StoreEmployeeId = Guid.Parse(employee.LocalEmployeeId);
                        payMemo.MemoId = Guid.Parse(Config.PtoMemoType);
                        payMemo.JobId = Guid.Parse(employee.PrimaryJobId);
                        payMemo.UnitId = int.Parse(locationId);
                    }
                    else
                    {
                        // Handle the case where the string is not a valid Guid
                        Log.Logger.Warning("Invalid GlobalEmployeeId for employee: {EECode}", punch.EECode);
                    }
                    
                    payMemoTransactions.Add(payMemo);
                }


                foreach (var payMemo in payMemoTransactions)
                {
                    if (executionMode == ExecutionMode.Execute)
                    {
                        bool success = service.AddPayMemoTransaction(payMemo).Result;
                        if (success)
                        {
                            Log.Logger.Information("Successfully added pay memo transaction for employee {EECode}", payMemo.StoreEmployeeId);
                        }
                        else
                        {
                            Log.Logger.Error("Failed to add pay memo transaction for employee {EECode}", payMemo.StoreEmployeeId);
                        }
                    }
                    else
                    {
                        Log.Logger.Information("Dry run: Would add pay memo transaction for employee {EECode}", payMemo.StoreEmployeeId);
                    }
                  
                }

                ConsoleService.PrintFormattedJson(payMemoTransactions);

                
            }
        }
        catch (Exception e)
        {
            Log.Logger.Fatal(e.Message);
            if (!string.IsNullOrEmpty(e.StackTrace)) Log.Logger.Fatal(e.StackTrace);
        }
    }

    private void ProcessImportCommand(ExecutionMode executionMode, List<string> args, string syncLabel, int syncLimit,
        Func<DataCentralService, string, IDictionary<string, Models.StoreEmployee>, ExecutionMode, Payroll.Shared.Employee, bool> func)
    {
        try
        {
            if (!ConsoleService.TryGetEmployeesFromInput(out var employeesToImport))
            {
                Log.Logger.Error("Failed to parse employee list");
                return;
            }

            if (employeesToImport == null)
            {
                Log.Logger.Error("Failed to load employee list");
                return;
            }

            if (args == null || args.Count < 1)
            {
                Console.WriteLine("Usage: DataCentral.Tool.exe import [hires|terms|sync] <store-number|all>");
                return;
            }

            string unitId = args[0];

            if (executionMode == ExecutionMode.Execute)
            {
                // both the config file and the command line must confirm execution
                if (args.Count < 2) executionMode = ExecutionMode.DryRun;
                else if (args[1] != "doit") executionMode = ExecutionMode.DryRun;
            }

            Log.Logger.Information("DataCentral.Tool Mode={mode}, Target={loc}, Op={op}, Limit={lmt}", executionMode, unitId, syncLabel, syncLimit);
            
            using (var service = new DataCentralService())
            {
                var locations = service.GetLocationsAsync().Result;
                if (unitId != "all") locations.RemoveAll(x => x.Code != unitId);

                var storeCodeToId = locations.ToDictionary(x => x.Code, x => x.Id);
                var employeeDirectory = service.GetAllStoreEmployees(locations).Result;
                var syncCnt = 0;

                foreach (var employee in employeesToImport)
                {
                    if (unitId != "all" && employee.PrimaryWorkLocation != unitId)
                        continue;

                    //Log.Logger.Information("Processing employee {0}/{1}", employee.ClockSeq, employee.LastName);

                    if (string.IsNullOrEmpty(employee.PrimaryWorkLocation))
                    {
                        Log.Logger.Error("Skipping employee {fname} {lname} - {cseq} - no primary work location assigned", employee.FirstName, employee.LastName, employee.Id);
                        continue;
                    }

                    if (!storeCodeToId.TryGetValue(employee.PrimaryWorkLocation, out var locationId))
                        {
                            // these are only debug level because none of these should actually be sync'd
                            Log.Logger.Debug("  Failed to locate a store with code {rid}, skipping {fname} {lname} - {cseq}", employee.PrimaryWorkLocation, employee.FirstName, employee.LastName, employee.Id);
                            continue;
                        }

                    if (!employeeDirectory.TryGetValue(locationId, out var directoryForLocation))
                    {
                        Log.Logger.Error("  Failed to locate employee directory for {rid}, skipping {fname} {lname} - {cseq}", locationId, employee.FirstName, employee.LastName, employee.Id);
                        continue;
                    }

                    // now set the primary work location to the mapped store id, instead of the store number we received in the JSON input
                    employee.PrimaryWorkLocation = locationId;
                    
                    var processed = func(service, locationId, directoryForLocation, executionMode, employee);
                    if (processed) syncCnt++;

                    if (syncLimit > 0 && syncCnt >= syncLimit)
                    {
                        Log.Logger.Warning("  {lbl} limit {x} reached, exiting...", syncLabel, syncLimit);
                        return;
                    }
                }
            }
        }
        catch (Exception e)
        {
            Log.Logger.Fatal(e.Message);
            if (!string.IsNullOrEmpty(e.StackTrace)) Log.Logger.Fatal(e.StackTrace);
        }
    }

    private bool UpdateEmployeeProperty<T>(T dcProperty, T newValue, ref StoreEmployee updatedEmployeeRecord, string propertyName, Employee employee)
    {
        try
        {
            // Skip if new value is null/default
            if (newValue == null || EqualityComparer<T>.Default.Equals(newValue, default(T)))
                return false;

            // Skip if new value is empty string
            if (typeof(T) == typeof(string) && string.IsNullOrEmpty(newValue as string))
                return false;

            // Compare values - if they're different, update and return true
            if (!Equals(dcProperty, newValue))
            {
                Log.Logger.Information("  Updating {property} from '{old}' to '{new}' for {eid}/{cseq}",
                    propertyName, dcProperty, newValue, employee.Id, employee.ClockSeq);

                // Set the property directly without reflection
                switch (propertyName)
                {
                    case "first name":
                        updatedEmployeeRecord.FirstName = (string)(object)newValue;
                        break;
                    case "last name":
                        updatedEmployeeRecord.LastName = (string)(object)newValue;
                        break;
                    case "nickname":
                        updatedEmployeeRecord.NickName = (string)(object)newValue;
                        break;
                    case "email":
                        updatedEmployeeRecord.Email = (string)(object)newValue;
                        break;
                    case "phone":
                        updatedEmployeeRecord.PhoneNumber = (string)(object)newValue; // different for POST
                        break;
                    case "birthday":
                        updatedEmployeeRecord.Birthday = (string)(object)newValue;
                        break;
                    case "primary job":
                        updatedEmployeeRecord.PrimaryJobDescription = (string)(object)newValue;
                        break;
                    case "primary job id":
                        updatedEmployeeRecord.PrimaryJobId = (string)(object)newValue;
                        break;
                    default:
                        Log.Logger.Warning("Unknown property name: {propertyName}", propertyName);
                        return false;
                }
                return true;
            }

            return false;
        }
        catch (Exception ex)
        {
            Log.Logger.Error(ex, "Error in UpdateEmployeeProperty for property {property}", propertyName);
            return false;
        }
    }

    public void Hires(List<string> args)
    {
        var results = new List<Result>();
    
        // get the ID of status "A" (active)
        var statusMap = Config.StatusMap();
        if (!statusMap.TryGetValue("A", out string statusId))
        {
            Log.Logger.Fatal("Failed to find status {status}, cannot process new hires.", "A");
            return;
        }

        ProcessImportCommand(Config.HireMode, args, "Hire", Config.HireLimit,
         (DataCentralService service, string restaurant, IDictionary<string, Models.StoreEmployee> employeeDirectory,
            ExecutionMode executionMode, Payroll.Shared.Employee employee) =>
        {
            if (!employee.Active)
            {
                Log.Logger.Warning("Skipping creation of inactive employee {cseq}", employee.ClockSeq);
                return false;
            }

            var result = new Result();
            result.AddArg("pkey", employee.Id);
            result.AddArg("date", employee.HireDate?.ToShortDateString());
            result.AddArg(key: "name", $"{employee.FirstName} {employee.LastName}");
            result.AddArg("restaurant", val: restaurant);

            if (employeeDirectory.TryGetValue(employee.ClockSeq, out Models.StoreEmployee dcEmployee))
            {
                // if already active, no need to do anything
                if (dcEmployee.Status == 'A') return false;
            
                Log.Logger.Information("Re-enabling an existing employee account {fname} {lname} - {id}/{cseq} at location {loc}, hired: {h}",
                    employee.FirstName, employee.LastName, employee.Id, employee.ClockSeq, restaurant, employee.HireDate?.ToShortDateString());

                HRManagementEmployee hrEmployee = Converter.StoreEmployeeToHrManagementEmployee(dcEmployee);
        
                MuEmployeeStatus status = new MuEmployeeStatus()
                {
                    LocalEmployeeId = hrEmployee.StoreEmployeeId,
                    Status = "A",
                    RecordId = statusId,
                    ReasonId = statusId,
                    BusinessDate = DateTime.Now,
                    Comment = "Changed via DataCentral.Tool",
                };

                if (executionMode == ExecutionMode.Execute)
                {
                    bool activated = service.ChangeEmployeeStatus(hrEmployee.StoreEmployeeId, status).Result;

                    if(!activated)
                    {
                        Log.Logger.Warning("Failed to activate employee {cseq}", employee.ClockSeq);
                    } 
                }
                else
                {
                    Log.Logger.Information("Would have reactivated employee {cseq}", employee.ClockSeq);
                }
                
                result.ResultType = ResultType.ReHire;
                results.Add(result);
                
                return true;
            }

            Log.Logger.Information("Creating new employee {fname} {lname} - {id}/{cseq} at location {loc}, hired: {h}",
                employee.FirstName, employee.LastName, employee.Id, employee.ClockSeq, restaurant, employee.HireDate?.ToShortDateString());

            
                HRManagementEmployee hrEmployee2 = Converter.EmployeeToHRManagementEmployee(employee);
                if (executionMode == ExecutionMode.Execute)
                {
                    Log.Logger.Debug("hrEmployee2: {employee}", JsonSerializer.Serialize(hrEmployee2, new JsonSerializerOptions { WriteIndented = true }));
                    EmployeeData addedEmployee = service.AddEmployee(hrEmployee2).Result;

                    if (addedEmployee == null)
                    {
                        Log.Logger.Warning("Failed to add employee {cseq}", employee.ClockSeq);
                    }
                    else
                    {

                        Dictionary<string, string> attrs = employee.Attributes;
                        string jobTitle = attrs?.GetValueOrDefault(key: "business_title") ?? "";
                        
                        Log.Logger.Information("Adding primary job to new employee: {job}", jobTitle);

                        List<Models.Job> jobs = new List<Models.Job>();
                        var jobNameMap = Config.JobNameMap();

                        if (!string.IsNullOrEmpty(jobTitle))
                        {
                            if (!jobNameMap.TryGetValue(jobTitle, out string jobName))
                            {
                                Log.Logger.Warning("Failed to find external job '{job}' for employee {cseq}", jobTitle,
                                    employee.ClockSeq);
                            }

                            var jobMap = Config.JobsById();
                            var jobEntry = jobMap.FirstOrDefault(j => j.Value == jobName);

                            if (jobEntry.Key == null)
                            {
                                Log.Logger.Warning("Job mapping failed for {job}, for employee {cseq}", jobName,
                                    employee.ClockSeq);
                            } 
                            else
                            {
                                StoreEmployee storeEmployee =
                                    service.GetStoreEmployeeAsync(addedEmployee.StoreEmployeeID).Result;
                                var jobbed = service.SetEmployeePrimaryJob(storeEmployee, jobEntry.Key).Result;
                                if (!jobbed)
                                {
                                    Log.Logger.Warning("Failed to add job {job} to employee {cseq}", jobName,
                                        employee.ClockSeq);
                                }
                       
                            }
                        }
                        else
                        {
                            Log.Logger.Warning("No job title found for employee {cseq}", employee.ClockSeq);
                        }

                    }
                }
                else
                {
                    Log.Logger.Information("Would have added employee {cseq}", employee.ClockSeq);
                    Log.Logger.Information(JsonSerializer.Serialize(hrEmployee2, Json.DefaultSerializerOutputStyle));
                }
            
            return true;
        });
    }
    
    public void Sync(List<string> args)
    {
        var results = new List<Result>();
        var jobMap = Config.JobsById();
        var jobNameMap = Config.JobNameMap();

        var defaultSerializerOptions 
            = new JsonSerializerOptions { WriteIndented = true, DefaultIgnoreCondition = JsonIgnoreCondition.WhenWritingNull };

        ProcessImportCommand(Config.SyncMode, args, "Sync", Config.SyncLimit,
         (DataCentralService service, string locationId, IDictionary<string, Models.StoreEmployee> employeeDirectory, ExecutionMode executionMode, Payroll.Shared.Employee employee) =>
        {
            if (employee.Jobs == null)
            {
                Log.Logger.Warning("Employee {EmployeeId} has no jobs", employee.Id);
                return false;
            }
            StoreEmployee updatedEmployeeRecord = null;

            StoreEmployee fullStoreEmployeeRecord = null;
            {
                if (!employeeDirectory.TryGetValue(employee.ClockSeq, out Models.StoreEmployee dcEmployee))
                {
                    Log.Logger.Information("Skipping sync of a non-existent employee {id}/{cseq} - {fn} {ln} @ loc {loc}",
                        employee.Id, employee.ClockSeq, employee.FirstName, employee.LastName, employee.PrimaryWorkLocation);
                    return false;
                }

                if (!employee.Active)
                {
                    Log.Logger.Warning("Skipping sync of inactive employee {id}/{cseq}", employee.Id, employee.ClockSeq);
                    return false;
                }

                if (dcEmployee.IgnoreForPayroll)
                {
                    Log.Logger.Warning("Skipping sync of 'ignore for payroll' employee {id}/{cseq}", employee.Id, employee.ClockSeq);
                    return false;
                }
                fullStoreEmployeeRecord = service.GetStoreEmployeeAsync(dcEmployee.LocalEmployeeId).Result;

                if (fullStoreEmployeeRecord == null || string.IsNullOrEmpty(fullStoreEmployeeRecord.LocalEmployeeId))
                {
                    Log.Logger.Error("fullStoreEmployeeRecord or LocalEmployeeId is null for employee {ClockSeq}", employee.ClockSeq);
                    return false;
                }

                if (string.IsNullOrEmpty(fullStoreEmployeeRecord.PrimaryJobId))
                {
                    Log.Logger.Error("PrimaryJobId is null for employee {ClockSeq}", employee.ClockSeq);
                    return false;
                }

              updatedEmployeeRecord = new Models.StoreEmployee() { LocalEmployeeId = dcEmployee.LocalEmployeeId, PrimaryJobId = dcEmployee.PrimaryJobId, GlobalEmployeeId = dcEmployee.GlobalEmployeeId };

                // the 24.22 release which released to the Graeter's on 10/24 does not require/allow the full store employee record
                // fullStoreEmployeeRecord = service.GetStoreEmployeeAsync(dcEmployee.LocalEmployeeId).Result;

                Log.Logger.Debug("fullStoreEmployeeRecord: " + JsonSerializer.Serialize(fullStoreEmployeeRecord, defaultSerializerOptions));
                Log.Logger.Debug("LocalEmployeeId: {LocalEmployeeId}, PrimaryJobId: {PrimaryJobId}", 
                    fullStoreEmployeeRecord?.LocalEmployeeId, 
                    fullStoreEmployeeRecord?.PrimaryJobId);
            }

            // update toast record as needed
            bool employeeUpdate = false;
            bool jobUpdate = false;
            
            Dictionary<string, string> attrs = employee.Attributes;
            string nickName = attrs?.GetValueOrDefault(key: "nickname") ?? "";
            string jobTitle = attrs?.GetValueOrDefault(key: "business_title") ?? "";

            employeeUpdate |= UpdateEmployeeProperty(fullStoreEmployeeRecord.FirstName, employee.FirstName, ref updatedEmployeeRecord, "first name", employee);
            employeeUpdate |= UpdateEmployeeProperty(fullStoreEmployeeRecord.LastName, employee.LastName, ref updatedEmployeeRecord,    "last name", employee);
            employeeUpdate |= UpdateEmployeeProperty(fullStoreEmployeeRecord.NickName, nickName, ref updatedEmployeeRecord, "nickname", employee);
            employeeUpdate |= UpdateEmployeeProperty(fullStoreEmployeeRecord.Email, FieldService.GetEmailAddress(employee), ref updatedEmployeeRecord, "email", employee);
            employeeUpdate |= UpdateEmployeeProperty(fullStoreEmployeeRecord.PrimaryPhone, employee.OfficePhone, ref updatedEmployeeRecord, "phone", employee);
            employeeUpdate |= UpdateEmployeeProperty(fullStoreEmployeeRecord.Birthday, employee.DateOfBirth?.ToString("yyyy-MM-dd") +"T00:00:00", ref updatedEmployeeRecord, "birthday", employee);

            Log.Logger.Debug("fullStoreEmployeeRecord: " + JsonSerializer.Serialize(fullStoreEmployeeRecord, defaultSerializerOptions));

            if (employeeUpdate) //'Test Employee'	55609d41-b108-4662-9f8c-82fd8524c08d
            {
                Log.Logger.Information("Updating employee {cseq}/{fn} {ln} @ {loc}", employee.ClockSeq, employee.FirstName, employee.LastName, employee.PrimaryWorkLocation);

                if (executionMode == ExecutionMode.Execute)
                {
                    bool updated = service.PatchEmployee(updatedEmployeeRecord).Result;
                }
                else
                {
                    Log.Logger.Information("Would update employee {cseq}/{fn} {ln} @ {loc}", employee.ClockSeq, employee.FirstName, employee.LastName, employee.PrimaryWorkLocation);
                    //debug log the fullStore
                }
            }

            // now handle updating jobs
            if (!string.IsNullOrEmpty(jobTitle))
            {
                if (!jobNameMap.TryGetValue(jobTitle, out string jobName))
                {
                    Log.Logger.Warning("Failed to find job {job} for employee {cseq}", jobTitle, employee.ClockSeq);
                    return false;
                }

                var jobEntry = jobMap.FirstOrDefault(j => j.Value == jobName);
                if (jobEntry.Key == null)
                {
                    Log.Logger.Warning("Failed to find job id for {job}", jobName);
                    return false;
                }

                jobUpdate |= UpdateEmployeeProperty(fullStoreEmployeeRecord.PrimaryJobDescription, jobName, ref updatedEmployeeRecord, "primary job", employee);
                jobUpdate |= UpdateEmployeeProperty(fullStoreEmployeeRecord.PrimaryJobId, jobEntry.Key, ref updatedEmployeeRecord, "primary job id", employee);

                if (jobUpdate)
                {
                    Log.Logger.Information("Updating job for employee {cseq}", employee.ClockSeq);

                    if (executionMode == ExecutionMode.Execute)
                    {
                        bool updated = service.SetEmployeePrimaryJob( updatedEmployeeRecord, jobEntry.Key).Result;
                        if (!updated)
                        {
                            Log.Logger.Warning("Failed to update job for employee {cseq}", employee.ClockSeq);
                        }
                        else
                        {
                            Log.Logger.Information("Updated job to {job} for employee {cseq}", jobName,
                                employee.ClockSeq);
                        }
                    }
                    else
                    {
                        Log.Logger.Information("Would update job to {job} for employee {cseq}", jobName, employee.ClockSeq);
                    }
                }
            }
            
            //check if we need to update the job rates
            // Log.Logger.Information("Checking job rates for employee {ClockSeq} ({FirstName} {LastName}), LocalEmployeeId: {LocalEmployeeId}", 
            //     employee.ClockSeq, employee.FirstName, employee.LastName, fullStoreEmployeeRecord.LocalEmployeeId);
            // get the job rates for this employee
            var jobRates = service.GetEmployeeJobRates(Guid.Parse(fullStoreEmployeeRecord.LocalEmployeeId), Guid.Parse(fullStoreEmployeeRecord.PrimaryJobId)).Result;
            Log.Logger.Debug("jobRates: " + JsonSerializer.Serialize(jobRates, new JsonSerializerOptions { WriteIndented = true  }));
            // Get the RecordID of the JobRate that has Approved=="Y"
            var approvedJobRates = jobRates
                .Where(rate => rate.Approved == "Y" && rate.EndDate > DateTime.Now)
                .ToList();

            if (approvedJobRates.Count > 1)
            {
                Log.Logger.Error("Multiple approved job rates found for the employee.");
                return false;
            }

            var approvedJobRateId = approvedJobRates.FirstOrDefault()?.RecordId ?? Guid.Empty;
            var employeeJobName = Config.JobsById().TryGetValue(fullStoreEmployeeRecord.PrimaryJobId, out var name) ? name : null;
            // Map job names using the jobNameMap
            if (employee.Jobs != null)
            {
                foreach (var job in employee.Jobs)
                {
                    if (job.Name != null && jobNameMap.TryGetValue(job.Name, out var mappedName))
                    {
                        job.Name = mappedName;
                    }
                }
            }

            bool rateChanged = false;

            if (approvedJobRateId != Guid.Empty)
            {
                // Check if the employee's job rate is the same as the approved job rate
                var approvedJobRate = jobRates.FirstOrDefault(rate => rate.RecordId == approvedJobRateId);
                if (approvedJobRate != null)
                {
                    // Get the job name from the config using the primary job ID

                    if (employeeJobName != null)
                    {
                        
                        // Find the matching job in employee.Jobs using the job name
                        var employeeJob = employee.Jobs?.FirstOrDefault(j => j.Name?.Equals(employeeJobName, StringComparison.OrdinalIgnoreCase) ?? false);

                        if (employeeJob != null)
                        {
                            rateChanged = employeeJob.Rate != approvedJobRate.Rate;
                            bool effectiveDateChanged = employeeJob.EffectiveDate != approvedJobRate.EffectiveDate;

                            if (rateChanged)
                            {
                                string changes = rateChanged && effectiveDateChanged ? "rate and effective date" :
                                                 rateChanged ? "rate" : "effective date";

                                Log.Logger.Information("Employee {EmployeeId} {Changes} for job {JobName} differs from approved job. Current: Rate={CurrentRate}, EffectiveDate={CurrentDate}. Approved: Rate={ApprovedRate}, EffectiveDate={ApprovedDate}",
                                    fullStoreEmployeeRecord.LocalEmployeeId, changes, employeeJobName, employeeJob.Rate, employeeJob.EffectiveDate, approvedJobRate.Rate, approvedJobRate.EffectiveDate);

                                if (executionMode == ExecutionMode.Execute)
                                {
                                    bool updated = service.PatchEmployeeJobRate(approvedJobRateId, 
                                        Guid.Parse(fullStoreEmployeeRecord.LocalEmployeeId), 
                                        Guid.Parse(fullStoreEmployeeRecord.PrimaryJobId), 
                                        employeeJob.Rate,
                                        employeeJob.EffectiveDate ?? approvedJobRate.EffectiveDate).Result;

                                    if (updated)
                                    {
                                        Log.Logger.Information("Updated job {Changes} for employee {EmployeeId}, job {JobName}. New Rate: {NewRate}, New EffectiveDate: {NewDate}", 
                                            changes, fullStoreEmployeeRecord.LocalEmployeeId, employeeJobName, employeeJob.Rate, employeeJob.EffectiveDate ?? approvedJobRate.EffectiveDate);
                                    }
                                    else
                                    {
                                        Log.Logger.Warning("Failed to update job {Changes} for employee {EmployeeId}, job {JobName}", 
                                            changes, fullStoreEmployeeRecord.LocalEmployeeId, employeeJobName);
                                    }
                                }
                                else
                                {
                                    Log.Logger.Information("Would update job {Changes} for employee {FirstName} {LastName} (DC Local ID: {EmployeeId} | ClockSeq: {ClockSeq} | Id: {Id}), job {JobName}. New Rate: {NewRate}, New EffectiveDate: {NewDate}", 
                                        changes, employee.FirstName, employee.LastName, fullStoreEmployeeRecord.LocalEmployeeId, employee.ClockSeq, employee.Id, employeeJobName, employeeJob.Rate, employeeJob.EffectiveDate ?? approvedJobRate.EffectiveDate);
                                }
                            }
                        }
                        else
                        {
                            Log.Logger.Warning("Job {JobName} not found for employee {EmployeeId} or employee has no jobs. Employee: {@Employee}", 
                                employeeJobName, fullStoreEmployeeRecord.LocalEmployeeId, employee);
                        }
                    }
                    else
                    {
                        Log.Logger.Warning("Job name not found for primary job ID {JobId} of employee {EmployeeId}", 
                            fullStoreEmployeeRecord.PrimaryJobId, fullStoreEmployeeRecord.LocalEmployeeId);
                    }
                }
                else
                {
                    Log.Logger.Warning("Approved job rate not found for employee {EmployeeId}, job {JobName}, job ID {JobId}", 
                        fullStoreEmployeeRecord.LocalEmployeeId, employeeJobName, fullStoreEmployeeRecord.PrimaryJobId);
                }
            }
            else
            {
                Log.Logger.Warning("No approved job rate found for employee {EmployeeId}, job {JobName} (ID: {JobId}).", 
                    fullStoreEmployeeRecord.LocalEmployeeId,
                    employeeJobName,
                    fullStoreEmployeeRecord.PrimaryJobId);
                var employeeJob = employee.Jobs?.FirstOrDefault(j => j.Name == employeeJobName);

                if (employeeJob != null)
                {
                    
                    if (executionMode == ExecutionMode.Execute)
                    {
                        // Attempt to post a new job rate
                        DateTime effectiveDate = DateTime.Now; // Default to current date
                        if (!string.IsNullOrEmpty(fullStoreEmployeeRecord.OriginalHireDate))
                        {
                            if (DateTime.TryParse(fullStoreEmployeeRecord.OriginalHireDate, out DateTime parsedDate))
                            {
                                effectiveDate = parsedDate;
                            }
                            else
                            {
                                Log.Logger.Warning("Failed to parse OriginalHireDate: {OriginalHireDate}. Using current date instead.", fullStoreEmployeeRecord.OriginalHireDate);
                            }
                        }
                        else
                        {
                            Log.Logger.Warning("OriginalHireDate is null or empty. Using current date instead.");
                        }
                        

                        bool posted = service.PostEmployeeJobRate(
                            Guid.Parse(fullStoreEmployeeRecord.LocalEmployeeId),
                            Guid.Parse(fullStoreEmployeeRecord.PrimaryJobId),
                            Guid.NewGuid(), // Generate a new RecordID
                            effectiveDate,
                            employeeJob.Rate,
                            employeeJob.Rate
                        ).Result;

                        if (posted)
                        {
                            Log.Logger.Information("Posted new job rate for employee {EmployeeId}, job {JobName}. Rate: {Rate}, EffectiveDate: {EffectiveDate}",
                                fullStoreEmployeeRecord.LocalEmployeeId, employeeJobName, employeeJob.Rate, effectiveDate);
                        }
                        else
                        {
                            Log.Logger.Warning("Failed to post new job rate for employee {EmployeeId}, job {JobName}",
                                fullStoreEmployeeRecord.LocalEmployeeId, employeeJobName);
                        }
                    }
                    else
                    {
                        Log.Logger.Information("Would post new job rate for employee {EmployeeId}, job {JobName}. Rate: {Rate}, EffectiveDate: {EffectiveDate}",
                            fullStoreEmployeeRecord.LocalEmployeeId, employeeJobName, employeeJob.Rate, DateTime.Now);
                    }
                    
                }
                else
                {
                    Log.Logger.Warning("Job {JobName} not found for employee {EmployeeId}", employeeJobName, fullStoreEmployeeRecord.LocalEmployeeId);
                }
            }


            return rateChanged || employeeUpdate || jobUpdate;
        });

        ConsoleService.PrintFormattedJson(results);
    }

    public void Terms(List<string> args)
    {
        var results = new List<Result>();

        var statusMap = Config.StatusMap();
        if (!statusMap.TryGetValue("T", out string statusId))
        {
            Log.Logger.Warning(
                "Failed to find status {status}",
                "T");
        }

        ProcessImportCommand(Config.TermMode, args, "Term", Config.TermLimit,
        (DataCentralService service, string location, IDictionary<string, Models.StoreEmployee> employeeDirectory, ExecutionMode executionMode, Payroll.Shared.Employee employee) =>
        {
            if (employee.Active) return false; // only interested in terminated employees

            if (employee.TermDate > DateTime.Now)
            {
                Log.Logger.Information("Skipping termination of employee {cseq} with future term date {date}",
                    employee.ClockSeq, employee.TermDate?.ToShortDateString());
                return false;
            }

            Log.Logger.Debug("Terminating employee {0}/{1} - {2} {3}", employee.Id, employee.ClockSeq, employee.FirstName, employee.LastName);
            if (employeeDirectory.TryGetValue(employee.ClockSeq, out Models.StoreEmployee dcEmployee))
            {   
                HRManagementEmployee hrEmployee = Converter.StoreEmployeeToHrManagementEmployee(dcEmployee);

                if (executionMode == ExecutionMode.Execute)
                {             
                    MuEmployeeStatus status = new MuEmployeeStatus()
                    {
                        LocalEmployeeId = hrEmployee.StoreEmployeeId,
                        Status = "T",
                        RecordId = statusId,
                        ReasonId = statusId,
                        BusinessDate = DateTime.Now,
                        Comment = "Changed via DataCentral.Tool",
                    };                    
                    
                    bool terminated = service.ChangeEmployeeStatus(hrEmployee.StoreEmployeeId, status).Result;
                    Log.Logger.Information(terminated ? "Terminated employee {0}/{1} - {2} {3}" : "Failed to terminate employee {0}/{1} - {2} {3}",
                        employee.Id, employee.ClockSeq, employee.FirstName, employee.LastName);
                }
                else
                {
                    Log.Logger.Information("Would have terminated employee {0}/{1} - {2} {3}", employee.Id, employee.ClockSeq, employee.FirstName, employee.LastName);
                    Log.Logger.Debug(JsonSerializer.Serialize(hrEmployee, Json.DefaultSerializerOutputStyle));
                }       
            }
            
            return true;
            
        });

        if (results.Count > 0)
        {
            ConsoleService.PrintFormattedJson(results);
        }
    }

}
