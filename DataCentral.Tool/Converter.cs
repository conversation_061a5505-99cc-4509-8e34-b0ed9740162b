using System;
using System.Collections.Generic;
using Serilog;
using Payroll.Shared;
using System.Globalization;
using System.Linq;
using DataCentral.Tool.Models;
using Job = Payroll.Shared.Job;
using System.Text.Json;
using System.Text.Json.Serialization;

namespace DataCentral.Tool;

static class Converter
{
    public static Location StoreToLocation(Models.Store store)
    {
        var location = new Location()
        {
            Id = Convert.ToString(store.Id),
            Name = store.Description,
            StreetAddress = store.Address1,
            CityAddress = store.City,
            Zip = store.Zip,
            TimeZone = store.TimeZoneDescription.Head(3),
            Code = store.Number,  // for <PERSON>rae<PERSON> the number is the "external id", not sure if this will be true for other customers
            Active = (store.Hidden == "N")
        };

        return location;
    }

    public static Models.StoreEmployee EmployeeToStoreEmployee(int storeID, Employee employee)
    {
        var storeEmployee = new Models.StoreEmployee()
        {
            StoreId = storeID,
            LocalEmployeeId = employee.Id,
            UniquePersonalID = employee.ClockSeq,
            FirstName = employee.FirstName,
            LastName = employee.LastName,
            Address = employee.StreetAddress,
            City = employee.CityAddress,
            StateId = employee.State,
            Zip = employee.Zip,
            Email = FieldService.GetEmailAddress(employee),
            PhoneNumber = employee.OfficePhone,
            PrimaryPhone = employee.OfficePhone,
            Status = employee.Active ? 'A' : 'T',
            IsManager = employee.Salaried ? 1 : 0,
        };

        return storeEmployee;
    }

    public static Models.HRManagementEmployee EmployeeToHRManagementEmployee(Employee employee)
    {
        Dictionary<string, string> attrs = employee.Attributes;
        string jobTitle = attrs?.GetValueOrDefault(key: "business_title") ?? "";

        List<Models.Job> jobs = new List<Models.Job>();
        var jobNameMap = Config.JobNameMap();

        if (!string.IsNullOrEmpty(jobTitle))
        {
            if (!jobNameMap.TryGetValue(jobTitle, out string jobName))
            {
                Log.Logger.Warning("Failed to find external job '{job}' for employee {cseq}", jobTitle, employee.ClockSeq);
            }

            var jobMap = Config.JobsById();
            var jobEntry = jobMap.FirstOrDefault(j => j.Value == jobName);

            if (jobEntry.Key == null)
            {
                Log.Logger.Warning("Job mapping failed for {job}, for employee {cseq}", jobName, employee.ClockSeq);
            }
            else
            {
                jobs.Add(new Models.Job()
                {
                    JobId = jobEntry.Key,
                    Description = jobName,
                    Schedule = true, //TODO - this property is not used by Graeters but we may need to "know" the scheduled property in the future for other clients/jobs
                    Hide = false,
                    SkillLevel = 0
                });
            }
        }
        
        //set the employee status
        string statusId = employee.Active ? "A" : "T";
        string todayWithoutOffset = DateTime.Today.ToString("yyyy-MM-ddTHH:mm:ss");
        
        var statusMap = Config.StatusMap();
        if (!statusMap.TryGetValue(statusId, out string statusGuid))
        {
            Log.Logger.Warning(
                "Failed to find status {status}",
                statusId);
        }
        
        EmployeeStatus status = new EmployeeStatus()
        {
            StatusId = statusId,
            StatusReasonID = statusGuid,
            EffectiveDate = todayWithoutOffset,
            Hide = false
        };
        
        //set hireDate to mployee.HireDate. in this format"yyyy-MM-ddTHH:mm:ss"
        DateTime? hireDate = employee.HireDate;
        string formattedDate = hireDate?.ToString("yyyy-MM-ddTHH:mm:ss");
        
        var hrManagementEmployee = new Models.HRManagementEmployee()
        {
            TypeId = "P", 
            StoreId = employee.PrimaryWorkLocation,
            UniquePersonalNumber = employee.ClockSeq,
            PosPayrollID = employee.ClockSeq,
            FirstName = employee.FirstName,
            LastName = employee.LastName,
            Address = employee.StreetAddress,
            City = employee.CityAddress,
            Zip = employee.Zip,
            Email = FieldService.GetEmailAddress(employee),
            PrimaryPhone = employee.OfficePhone,
            Status = status,
            //IsManager = employee.Salaried,
            Jobs = jobs,
            HireDate = formattedDate,
            Borrowed = false, //since this is only used when adding New Employees (hires) then we can set this to false
            Birthdate = employee.DateOfBirth?.ToString("yyyy-MM-ddTHH:mm:ss")
            
        };
        
        return hrManagementEmployee;
    }
    
    public static HRManagementEmployee StoreEmployeeToHrManagementEmployee(StoreEmployee storeEmployee)
    {
        //set the employee status
        string statusId = storeEmployee.Status == 'A' ? "A" : "T";
        string todayWithoutOffset = DateTime.Today.ToString("yyyy-MM-ddTHH:mm:ss");

        var statusMap = Config.StatusMap();
        if (!statusMap.TryGetValue(statusId, out string statusGuid))
        {
            Log.Logger.Warning(
                "Failed to find status {status}",
                statusId);
        }

        EmployeeStatus status = new EmployeeStatus()
        {
            StatusId = statusId,
            StatusReasonID = statusGuid,
            EffectiveDate = todayWithoutOffset,
            Hide = false
        };

        string storeId = storeEmployee.StoreId.ToString() == "0" ? storeEmployee.UnitId.ToString() : storeEmployee.StoreId.ToString();

        var hrManagementEmployee = new Models.HRManagementEmployee()
        {
            TypeId = "P",
            StoreEmployeeId = storeEmployee.LocalEmployeeId,
            //if storeEmployee.StoreId is empty, we can use storeEmployee.UnitId
            StoreId = storeId,
            //UnitId = storeEmployee.StoreId.ToString(),
            UniquePersonalNumber = storeEmployee.UniquePersonalID,
            PosPayrollID = storeEmployee.PosPayrollId,
            FirstName = storeEmployee.FirstName,
            LastName = storeEmployee.LastName,
            Address = storeEmployee.Address,
            City = storeEmployee.City,
            Zip = storeEmployee.Zip,
            Email = storeEmployee.Email,
            PrimaryPhone = storeEmployee.PrimaryPhone,
            Status = status,
            //IsManager = employee.Salaried,
            HireDate = storeEmployee.OriginalHireDate,
            Borrowed = storeEmployee.Borrowed == 'Y',
        };

        if (!string.IsNullOrEmpty(storeEmployee.Birthday))
        {
            if (DateTime.TryParse(storeEmployee.Birthday, out DateTime birthdate))
            {
                hrManagementEmployee.Birthdate = birthdate.ToString("yyyy-MM-ddTHH:mm:ss");
            }
            else
            {
                Log.Logger.Warning("Failed to parse birthday for employee {UniquePersonalID}: {Birthday}", 
                    storeEmployee.UniquePersonalID, storeEmployee.Birthday);
            }
        }

        List<Models.Job> jobs = new List<Models.Job>();
        var jobNameMap = Config.JobNameMap();

        if (string.IsNullOrEmpty(storeEmployee.PrimaryJobDescription))
            return hrManagementEmployee;

        if (!jobNameMap.TryGetValue(storeEmployee.PrimaryJobDescription, out string jobName))
        {
            Log.Logger.Warning(
                "Failed to find DC job {job} for employee {cseq}",
                storeEmployee.PrimaryJobDescription, storeEmployee.UniquePersonalID);
            return hrManagementEmployee;
        }

        var jobMap = Config.JobsById();
        var jobEntry = jobMap.FirstOrDefault(j => j.Value == jobName);
        if (jobEntry.Key == null)
        {
            Log.Logger.Warning("Job mapping failed lookup for {job}", jobName);
            return hrManagementEmployee;
        }
        
        string jobId = jobEntry.Key;
        jobs.Add(new Models.Job()
        {
            Id = jobEntry.Key,
            JobId = jobEntry.Key,
            Description = jobName,
            Schedule = true,
            Hide = false,
            SkillLevel = 0
        });

        hrManagementEmployee.Jobs = jobs;
        return hrManagementEmployee;
    }
 
    public static Employee StoreEmployeeToEmployee(Models.StoreEmployee storeEmployee)
    {
        var employee = new Employee()
        {
            Id = Convert.ToString(storeEmployee.LocalEmployeeId),
            ClockSeq = storeEmployee.UniquePersonalID,
            FirstName = storeEmployee.FirstName,
            LastName = storeEmployee.LastName,
            StreetAddress = storeEmployee.Address,
            CityAddress = storeEmployee.City,
            State = storeEmployee.StateId,
            Zip = storeEmployee.Zip,
            OfficePhone = storeEmployee.PrimaryPhone,
            Active = storeEmployee.Status == 'A',
            Salaried = storeEmployee.IsManager == 1,
            DeptCode = storeEmployee.UnitId.ToString(), // this is store specific
            DeptName = storeEmployee.HomeUnitName,
            PrimaryWorkLocation = storeEmployee.HomeUnitId.ToString() // this is stable across stores
        };

        FieldService.SetEmailAddress(ref employee, storeEmployee.Email);

        employee.AddAttribute("nickname", storeEmployee.NickName);

        if (DateTime.TryParseExact(storeEmployee.Birthday, "yyyy-MM-ddTHH:mm:ss", CultureInfo.InvariantCulture, DateTimeStyles.None, out DateTime birthday))
        {
            // should we also handle "yyyy-MM-dd'T'HH:mm:ss.fff'Z'" ?
            employee.DateOfBirth = birthday;
        }

        if (DateTime.TryParseExact(storeEmployee.OriginalHireDate, "yyyy-MM-dd'T'HH:mm:ss.fff'Z'",
                             CultureInfo.InvariantCulture,
                             DateTimeStyles.RoundtripKind,
                             out DateTime hireDate))
        {
            employee.HireDate = hireDate;
        }

        if (!string.IsNullOrEmpty(storeEmployee.PrimaryJobId))
        {
            employee.Jobs.Add(new Job() {
                IsPrimary = true,
                Code = storeEmployee.PrimaryJobId,
                Name = storeEmployee.PrimaryJobDescription,
            });
        }

        return employee;
    }

    public static PayMemoTransactionV2 PunchToPayMemoTransaction(PunchPair pp)
    {
        TimeSpan timeSpan = pp.TimeOut - pp.TimeIn;
        var pm = new PayMemoTransactionV2()
        {
            Hours = timeSpan.Hours,
            Comment = pp.Description,
            BusinessDate = pp.TimeIn,
            OriginId = "S"
        
        };

        return pm;   
    }
}

public class ClockTypeConverter : JsonConverter<ClockType>
{
    public override ClockType Read(ref Utf8JsonReader reader, Type typeToConvert, JsonSerializerOptions options)
    {
        string value = reader.GetString();
        return value switch
        {
            "TimeClock" => ClockType.TimeClock,
            "Unpaid Break" => ClockType.UnpaidBreak,
            "Paid Break" => ClockType.PaidBreak,
            _ => throw new JsonException($"Unknown ClockType value: {value}")
        };
    }

    public override void Write(Utf8JsonWriter writer, ClockType value, JsonSerializerOptions options)
    {
        writer.WriteStringValue(value.ToString());
    }
}