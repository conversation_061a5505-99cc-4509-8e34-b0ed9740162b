using System.Text.Json;
using Payroll.Shared;
using Serilog;
using System;
using System.Linq;
using System.Collections.Generic;
using System.Globalization;
using DataCentral.Tool.Models;

namespace DataCentral.Tool
{
    class EmployeeCommand
    {
        public void Active(List<string> args)
        {
            PrintSelected(args, x => x.Active);
        }

        public void Terms(List<string> args)
        {
            PrintSelected(args, x => !x.Active);
        }

        public void List(List<string> args)
        {
            PrintSelected(args, x => true);
        }

        public void PrintSelectedFromLocation(string unitId, Func<Employee, bool> match)
        {
            try
            {
                using (var service = new DataCentralService())
                {
                    var employees = service.GetEmployeesAsync(unitId).Result;
                    
                    // Add header row
                    Console.WriteLine("UnitId\tClockSeq\tFullName\t\t\t\tEmployeeId");
                    
                    foreach (var employee in employees)
                    {
                        if (!match(employee)) continue;
                        var fullName = $"{employee.FirstName } {employee.LastName}".PadRight(30);
                        Console.WriteLine($"{unitId}\t{employee.ClockSeq}\t'{fullName}'\t{employee.Id}");
                    }
                }
            }
            catch (Exception e)
            {
                Log.Logger.Fatal(e.Message);
                Log.Logger.Fatal(e.StackTrace);
            }
        }

        public void PrintSelected(List<string> args, Func<Employee, bool> match)
        {
            // single store option
            if (args != null && args.Count > 0)
            {
                var unitId = args[0];
                PrintSelectedFromLocation(unitId, match);
                return;
            }

            try
            {
                Console.WriteLine("UnitId\tClockSeq\tFirstName\tLastName\tId");
                using (var service = new DataCentralService())
                {
                    var locations = service.GetLocationsAsync().Result;
                    foreach (var location in locations)
                    {
                        PrintSelectedFromLocation(location.Id, match);
                    }
                }
            }
            catch (Exception e)
            {
                Log.Logger.Fatal(e.Message);
                Log.Logger.Fatal(e.StackTrace);
            }
        }

        public void Add(List<string> args)
        {
            if (args == null || args.Count < 6)
            {
                Console.WriteLine("Usage: DataCentral.Tool.exe add employee <restraunt-id> <clock-seq> <first-name> <last-name> <email> <job-name>");
                return;
            }
            Dictionary<string, string> eAtts = new Dictionary<string, string>();
            eAtts.Add("business_title", args[5]);
            
            var employee = new Payroll.Shared.Employee()
            {
                PrimaryWorkLocation = args[0],
                ClockSeq = args[1],
                FirstName = args[2],
                LastName = args[3],
                WorkEmail = args[4],
                Attributes = eAtts
            };
            
            var hrEmployee = Converter.EmployeeToHRManagementEmployee(employee);
            
            try
            {
                using (var service = new DataCentralService())
                {
               
                    var addedEmployee = service.AddEmployee( hrEmployee).Result;
                    if (addedEmployee != null)
                        Console.WriteLine($"Added employee");
                }
            }
            catch (Exception e)
            {
                Log.Logger.Fatal(e.Message);
                Log.Logger.Fatal(e.StackTrace);
            }
        }
        
        public void Status(List<string> args)
        {
            if (args == null || args.Count < 2)
            {
                Console.WriteLine("Sets the employee status. Usage: DataCentral.Tool.exe employee status <store-employee-id> <status>");
                Console.WriteLine("For example, DataCentral.Tool.exe employee status C895A81E-0C20-42C0-B2DC-EAF43F9447BD A ");
                Console.WriteLine("<status> can be one of the following:");
                Console.WriteLine("  A - Active");
                Console.WriteLine("  T - Terminated");
                Console.WriteLine("  L - InActive");

                return;
            }
            var statusMap = Config.StatusMap();

            if (!statusMap.TryGetValue(args[1], out string statusId))
            {
                Log.Logger.Warning(
                    "Failed to find status {status}",
                    args[1]);

                return;
            }
            
            MuEmployeeStatus status = new MuEmployeeStatus()
            {
                LocalEmployeeId = args[0],
                Status = args[1],
                RecordId = statusId,
                ReasonId = statusId,
                BusinessDate = DateTime.Now,
                Comment = "Changed via DataCentral.Tool",
            };

            try
            {
                using (var service = new DataCentralService())
                {
               
                    var statusChanged = service.ChangeEmployeeStatus(args[0], status).Result;
                    if (statusChanged)
                        Console.WriteLine($"Changed Employee Status");
                }
            }
            catch (Exception e)
            {
                Log.Logger.Fatal(e.Message);
                Log.Logger.Fatal(e.StackTrace);
            }
        }

        public void View(List<string> args)
        {
            if (args == null || args.Count < 1)
            {
                Console.WriteLine("Usage: DataCentral.Tool.exe employee view <localId>");
                Console.WriteLine("For example, DataCentral.Tool.exe employee view ");
                return;
            }

            var localId = args[0];

            try
            {
                using (var service = new DataCentralService())
                {
                    var storeEmployee = service.GetStoreEmployeeAsync(localId).Result;
                    Console.WriteLine(JsonSerializer.Serialize(storeEmployee, Json.DefaultSerializerOutputStyle));
                    var employee = Converter.StoreEmployeeToEmployee(storeEmployee);
                    Console.WriteLine(JsonSerializer.Serialize(employee, Json.DefaultSerializerOutputStyle));
                }
            }
            catch (Exception e)
            {
                Log.Logger.Fatal(e.Message);
                Log.Logger.Fatal(e.StackTrace);
            }
        }

        public void Hrview(List<string> args)
        {
            if (args == null || args.Count < 1)
            {
                Console.WriteLine("Usage: DataCentral.Tool.exe employee hrview <store-employee-id>");
                return;
            }

            var upn = args[0];

            try
            {
                using (var service = new DataCentralService())
                {
                    HrEmployee hrEmployee = service.GetHrEmployeeAsync(upn).Result;
                    Console.WriteLine(JsonSerializer.Serialize(hrEmployee, Json.DefaultSerializerOutputStyle));
                    //var employee = Converter.Hr(hrEmployee);
                    //Console.WriteLine(JsonSerializer.Serialize(employee, Json.DefaultSerializerOutputStyle));
                }
            }
            catch (Exception e)
            {
                Log.Logger.Fatal(e.Message);
                Log.Logger.Fatal(e.StackTrace);
            }
        }

        public void Updatejob(List<string> args)
        {
            if (args == null || args.Count < 2)
            {
                Console.WriteLine("Usage: DataCentral.Tool.exe employee updatejob <localId> <jobId>");
                Console.WriteLine("For example, DataCentral.Tool.exe employee updatejob beec803d-85ab-4ae6-bdc3-57c39ae2f222 fe22a07d-d1d1-408a-aad2-d7d2d95ee985");
                return;
            }
            string localEmployeeId = args[0];
            string jobId = args[1];
            
            
            try
            {
                using (var service = new DataCentralService())
                {
                    StoreEmployee storeEmployee = service.GetStoreEmployeeAsync(localEmployeeId).Result;

                    var updated = service.SetEmployeePrimaryJob(storeEmployee, jobId).Result;
                    if (updated)
                        Console.WriteLine($"Updated employee job");
                }
            }
            catch (Exception e)
            {
                Log.Logger.Fatal(e.Message);
                Log.Logger.Fatal(e.StackTrace);
            }
        }
        public void Testpatch(List<string> args)
        {

            try
            {
                using (var service = new DataCentralService())
                {
                    StoreEmployee storeEmployee = service.GetStoreEmployeeAsync("952321bf-c452-461e-8a0d-958de9dfe26a").Result;
                    Console.WriteLine(JsonSerializer.Serialize(storeEmployee, Json.DefaultSerializerOutputStyle));

                    storeEmployee.PrimaryJobId = "87e67a2d-cbb2-421d-974c-f68157808bdc";
                    var updated = service.PatchEmployee(storeEmployee).Result;
                    if (updated)
                        Console.WriteLine($"Updated employee");
                }
            }
            catch (Exception e)
            {
                Log.Logger.Fatal(e.Message);
                Log.Logger.Fatal(e.StackTrace);
            }
        }
    }
}