﻿using Payroll.Shared;
using System;

namespace DataCentral.Tool
{
    public static class FieldService
    {
        public readonly static string FieldSection = "dc_fields";
        public static string EmailFieldName = "work";

        static FieldService()
        {
            Setting.Init();
            EmailFieldName = Setting.Get(FieldSection, "email").ToLower();
        }

        public static string GetEmailAddress(Payroll.Shared.Employee employee)
        {
            switch (EmailFieldName)
            {
                case "personal":
                    return employee.PersonalEmail;
                default:
                    return employee.WorkEmail;
            }
        }

        public static void SetEmailAddress(ref Payroll.Shared.Employee employee, string email)
        {
            switch (EmailFieldName)
            {
                case "personal":
                    employee.PersonalEmail = email;
                    break;
                default:
                    employee.WorkEmail = email;
                    break;
            }
        }
    }
}
