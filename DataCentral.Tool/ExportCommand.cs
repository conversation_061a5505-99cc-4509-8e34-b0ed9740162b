using System;
using System.Collections.Generic;
using System.Linq;
using Payroll.Shared;
using Serilog;
using System.Text.Json; // Ensure this using directive is present
using DataCentral.Tool.Models;

namespace DataCentral.Tool
{
    class ExportCommand : PosExportCommand
    {
        private void PrintTimeCommandInfo()
        {
            Console.WriteLine("Usage: DataCentral.Tool.exe export time <restaurant> <start-day> <end-day>");
            Console.WriteLine("  where: <restaurant> is the restaurant location code like MBMB or 'all' for all locations");
            Console.WriteLine(
                "  where: <start-day> and <end-day> are integers representing how many days prior to today");
            Console.WriteLine(
                "  e.g. 'DataCentral.Tool.exe export time 39 7 1' would export time from 7 days ago till yesterday at the location with ID 39.");
        }

        public void Settings(List<string> args)
        {
            string? atoken = Environment.GetEnvironmentVariable(Config.ENV_KEY_DC_KEY);
            Console.WriteLine($"export {Config.ENV_KEY_DC_KEY}={atoken}");
        }
        
        public int Time(List<string> args)
        {
            if (args == null || args.Count < 3)
            {
                PrintTimeCommandInfo();
                return 1;
            }

            try
            {
                // initialize directories to reduce API calls
                using (var service = new DataCentralService())
                {
                    var locationArg = args[0].Trim();
                    var locations = service.GetLocationsAsync().Result;
                    if (locationArg != "all") locations.RemoveAll(x => x.Id != locationArg);
                    
                    var allPunches = new List<PunchPair>();
                    BuildDateOffsetWindowsFromCommandLine(args[1], args[2], out DateTimeOffset startDate, out DateTimeOffset endDate);

                    foreach (var location in locations)
                    {
                        var punches = service.TimePunchesUsingStartEndDate(location, startDate, endDate).Result;

                        if (args.Count == 4 && !string.IsNullOrEmpty(args[3]))
                        {
                            string targetClockSeq = args[3];
                            punches.RemoveAll(p => p.ClockSeq != targetClockSeq);
                            Log.Logger.Information("Filtered punches for ClockSeq: {clockSeq}", targetClockSeq);
                        }
                        
                        Log.Logger.Debug("Punches returned: {cnt}", punches.Count);
                        allPunches.AddRange(punches);
                    }

                    ConsoleService.PrintFormattedJson(allPunches);
                }
            }
            catch (Exception e)
            {
                Log.Logger.Fatal(e.Message);
                Log.Logger.Fatal(e.StackTrace);
                return 1;
            }

            return 0;
        }
    }
}