using System;
using System.Text.Json.Serialization;

namespace DataCentral.Tool;

public class PayMemo
{
    [JsonPropertyName(name: "MemoID")]
    public string Id { get; set; }

    [JsonPropertyName("Description")]
    [JsonIgnore(Condition = JsonIgnoreCondition.WhenWritingNull)]
    public string Description { get; set; }
}

public class PayMemoTransaction
{
    public PayMemoStoreEmployee StoreEmployee { get; set; }
    public DateTimeOffset BusinessDate { get; set; }
    public PayMemo Memo { get; set; }
    public PayMemoJob Job { get; set; }
    public int Hours { get; set; }
    public decimal Amount { get; set; }
    public string Comment { get; set; }
    public PayMemoOrigin Origin { get; set; }
}
public class PayMemoTransactionV2
{
    [JsonPropertyName("UnitID")]
    public int UnitId { get; set; }

    [JsonPropertyName("StoreEmployeeID")]
    public Guid StoreEmployeeId { get; set; }

    [JsonPropertyName("BusinessDate")]
    public DateTimeOffset BusinessDate { get; set; }

    [JsonPropertyName("MemoID")]
    public Guid? MemoId { get; set; }

    [JsonPropertyName("JobID")]
    public Guid JobId { get; set; }

    [JsonPropertyName("Hours")]
    public decimal Hours { get; set; }

    [JsonPropertyName("Amount")]
    public decimal Amount { get; set; }

    [JsonPropertyName("OriginID")]
    public string OriginId { get; set; }

    [JsonPropertyName("Comment")]
    public string Comment { get; set; }
}
public class PayMemoStoreEmployee
{
    [JsonPropertyName(name: "StoreEmployeeID")]
     public Guid StoreEmployeeId { get; set; }
}

public class PayMemoJob
{
    [JsonPropertyName(name: "ID")]
    public string Id { get; set; }
    
    [JsonIgnore(Condition = JsonIgnoreCondition.WhenWritingNull)]
    public string Description { get; set; }
}

public class PayMemoOrigin
{
    [JsonPropertyName(name: "OriginID")]
    public string OriginId { get; set; } = "S";
}

