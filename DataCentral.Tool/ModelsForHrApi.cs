﻿using System;
using System.Text.Json.Serialization;
using System.Collections.Generic;
namespace DataCentral.Tool.Models;

public class HRManagementEmployee
{
    [JsonPropertyName("StoreEmployeeID")]
    [JsonIgnore(Condition = JsonIgnoreCondition.WhenWritingNull)]
    public string StoreEmployeeId { get; set; }
    [JsonIgnore(Condition = JsonIgnoreCondition.WhenWritingNull)]
    [JsonPropertyName("TypeID")]
    public string TypeId { get; set; }  //Required: "P" person or "R" Responsibility
    [JsonIgnore(Condition = JsonIgnoreCondition.WhenWritingNull)]
    public string FirstName { get; set; } //Required
    [JsonIgnore(Condition = JsonIgnoreCondition.WhenWritingNull)]
    public string LastName { get; set; } //Required
    [JsonIgnore(Condition = JsonIgnoreCondition.WhenWritingNull)]
    public string MiddleName { get; set; }
    [JsonIgnore(Condition = JsonIgnoreCondition.WhenWritingNull)]
    public string NickName { get; set; }
    [JsonIgnore(Condition = JsonIgnoreCondition.WhenWritingNull)]
    public string Address { get; set; }
    [JsonIgnore(Condition = JsonIgnoreCondition.WhenWritingNull)]
    public string City { get; set; }
    [JsonIgnore(Condition = JsonIgnoreCondition.WhenWritingNull)]
    [JsonPropertyName("StateID")]
    public string StateId { get; set; }
    [JsonIgnore(Condition = JsonIgnoreCondition.WhenWritingNull)]
    public string Zip { get; set; }
    [JsonIgnore(Condition = JsonIgnoreCondition.WhenWritingNull)]
    public string Email { get; set; }
    [JsonPropertyName("StoreID")]
    public string StoreId { get; set; } //Required
    [JsonIgnore(Condition = JsonIgnoreCondition.WhenWritingNull)]
    public string UniquePersonalNumber { get; set; } //Required
    [JsonIgnore(Condition = JsonIgnoreCondition.WhenWritingNull)]
    public string HireDate { get; set; } //Required
    public bool Borrowed { get; set; } //Required
    [JsonIgnore(Condition = JsonIgnoreCondition.WhenWritingNull)]
    public List<Job> Jobs { get; set; }
    [JsonIgnore(Condition = JsonIgnoreCondition.WhenWritingNull)]
    public EmployeeStatus Status { get; set; }
    [JsonIgnore(Condition = JsonIgnoreCondition.WhenWritingDefault)]
    public bool IsManager { get; set; }
    [JsonIgnore(Condition = JsonIgnoreCondition.WhenWritingNull)]
    public string PrimaryPhone { get; set; }

    [JsonPropertyName("Birthdate")]
    [JsonInclude]
    public string Birthdate;
    public string PosPayrollID { get; set; }

}

public class HrEmployee
{
    [JsonPropertyName("StoreEmployeeId")]
     public Guid StoreEmployeeId { get; set; }
    public string FirstName { get; set; }
    public string LastName { get; set; }
    public string MaidenName { get; set; }
    public string MiddleName { get; set; }
    public bool Hide { get; set; }
    public string UniquePersonalNumber { get; set; }
    public string Address { get; set; }
    public string Address2 { get; set; }
    public string City { get; set; }
    public string Zip { get; set; }
    [JsonPropertyName("TypeID")]
    public string TypeId { get; set; }
    [JsonPropertyName("ElectronicID")] 
    public string ElectronicId { get; set; }
    [JsonPropertyName("ClockInMethodID")] 
    public string ClockInMethodId { get; set; }
    public bool Schedule { get; set; }
    public bool WebSchedule { get; set; }
    public bool ExemptMinor { get; set; }
    public bool IsManager { get; set; }
    public DateTime OriginalHireDate { get; set; }
    public string NickName { get; set; }
    public DateTime? Birthdate { get; set; }
    public int EmployeeID { get; set; }
    public int TimeClockID { get; set; }
    public string PosPayrollID { get; set; }
    public string Suffix { get; set; }
    public string County { get; set; }
    public string FullPartTime { get; set; }
    public string AnnualSalary { get; set; }
    public string PrimaryPhone { get; set; }
    public string Email { get; set; }
    public bool Borrowed { get; set; }
    public bool ClockExemptSchedule { get; set; }
    public bool ClockExemptAutoBreaks { get; set; }
    public bool ClockExemptPayDeducts { get; set; }
    public string CustomID1 { get; set; }
    public string CustomID2 { get; set; }
    public string CustomID3 { get; set; }
    public string CustomID4 { get; set; }
    public string CustomID5 { get; set; }
    public string CustomID6 { get; set; }
    public string CustomID7 { get; set; }
    public string CustomID8 { get; set; }
    public string CustomID9 { get; set; }
    public string CustomID10 { get; set; }
    public string CustomID11 { get; set; }
    public string CustomID12 { get; set; }
    public string CustomID13 { get; set; }
    public string CustomID14 { get; set; }
    public string CustomID15 { get; set; }
    public string CustomID16 { get; set; }
    public string CustomID17 { get; set; }
    public string CustomID18 { get; set; }
    public string CustomID19 { get; set; }
    public string CustomID20 { get; set; }
    public bool PosLinked { get; set; }
    public bool FiredWithNonEligibleForRehireReason { get; set; }
    public HrStore Store { get; set; }
    public HrStore HomeStore { get; set; }
    public HrEmployeeType EmployeeType { get; set; }
    public HrPayMode PayMode { get; set; }
    public HrStatus Status { get; set; }
    public List<HrStatusChange> StatusChanges { get; set; }
    public HrState State { get; set; }
    public Job PrimaryJob { get; set; }
    public List<HrJobInfo> Jobs { get; set; }
    public List<HrFlag> Flags { get; set; }
    public HrStateW4 StateW4 { get; set; }
    public HrFederalW4 FederalW4 { get; set; }
    //public HrI9 I9 { get; set; }
}

public class HrStore
{
    public int ID { get; set; }
    public string Description { get; set; }
    public string Number { get; set; }
    public string Hidden { get; set; }
}

public class HrEmployeeType
{
    public string ID { get; set; }
    public string Description { get; set; }
}

public class HrPayMode
{
    public string ID { get; set; }
    public string Description { get; set; }
}

public class HrStatus
{
    public string ID { get; set; }
    public string Description { get; set; }
}

public class HrStatusChange
{
    public DateTime EffectiveDate { get; set; }
    public string Comment { get; set; }
    public bool Hide { get; set; }
    public HrStatus Status { get; set; }
    public HrReason Reason { get; set; }
}

public class HrReason
{
    public Guid ID { get; set; }
    public string ExportCode { get; set; }
    public string Description { get; set; }
    public bool EligibleForRehire { get; set; }
    public string Scope { get; set; }
    public bool Hide { get; set; }
}

public class HrState
{
    public Guid ID { get; set; }
    public HrCountry Country { get; set; }
    public string Code { get; set; }
    public bool Hide { get; set; }
    public string Description { get; set; }
}

public class HrCountry
{
    public int ID { get; set; }
    public string Description { get; set; }
    public string Code { get; set; }
    public bool Hide { get; set; }
}

public class HrJob
{
    public Guid ID { get; set; }
    public string Description { get; set; }
    public bool Tippable { get; set; }
    public bool Schedule { get; set; }
    public bool Hide { get; set; }
    public PayMode PayMode { get; set; }
}

public class HrJobInfo
{
    public int SkillLevel { get; set; }
    public decimal CurrentRate { get; set; }
    public Job Job { get; set; }
    public List<HrJobRate> JobRates { get; set; }
    public bool Hide { get; set; }
    public bool SecuredRate { get; set; }
}

public class HrJobRate
{
    public Guid ID { get; set; }
    public bool Hide { get; set; }
    public DateTime EffectiveDate { get; set; }
    public bool Approved { get; set; }
    public string ApprovedBy { get; set; }
    public decimal Rate { get; set; }
    public string Comment { get; set; }
}

public class HrFlag
{
    public HrPayrollFlag PayrollFlag { get; set; }
    public string Value { get; set; }
    public Guid? ValueLookupID { get; set; }
}

public class HrPayrollFlag
{
    public Guid ID { get; set; }
    public string Description { get; set; }
}

public class HrStateW4
{
    public HrFilingStatus FilingStatus { get; set; }
    public int TotalNumberOfAllowances { get; set; }
    public decimal AdditionalAmountWithheld { get; set; }
    public decimal WithholdPercent { get; set; }
    public bool ExemptFromWithholding { get; set; }
}

public class HrFederalW4
{
    public HrFilingStatus FilingStatus { get; set; }
    public decimal AdditionalAmountWithheld { get; set; }
    public bool ExemptFromWithholding { get; set; }
    public bool MultipleJobs { get; set; }
    public decimal DependentsTotal { get; set; }
    public decimal OtherIncome { get; set; }
    public decimal Deductions { get; set; }
}

public class HrFilingStatus
{
    public int ID { get; set; }
    public string Description { get; set; }
}

public class HrI9
{
    public string ResidentStatus { get; set; }
    public string AlienNumber { get; set; }
    public DateTime AuthorizedToWorkUntilDate { get; set; }
    public string ListADocumentTitle { get; set; }
    public string ListADocumentNumber { get; set; }
    public DateTime ListAExpirationDate { get; set; }
    public string ListAIssuingAuthority { get; set; }
    public string ListBDocumentTitle { get; set; }
    public string ListBDocumentNumber { get; set; }
    public DateTime ListBExpirationDate { get; set; }
    public string ListBIssuingAuthority { get; set; }
    public string ListCDocumentTitle { get; set; }
    public string ListCDocumentNumber { get; set; }
    public DateTime ListCExpirationDate { get; set; }
    public string ListCIssuingAuthority { get; set; }
}

