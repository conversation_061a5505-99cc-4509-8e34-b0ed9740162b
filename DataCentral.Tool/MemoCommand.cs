using System.Text.Json;
using Payroll.Shared;
using Serilog;
using System;
using System.Linq;
using System.Collections.Generic;
using System.Globalization;

namespace DataCentral.Tool
{
    class MemoCommand
    {
        public void PrintSelected(List<string> args, Func<Location, bool> match)
        {
            try
            {
                using (var service = new DataCentralService())
                {
                    var locations = service.GetLocationsAsync().Result;
                    Console.WriteLine("Id\tNumber\tName");
                    foreach (var location in locations)
                    {
                        if (!match(location)) continue;
                        Console.WriteLine($"{location.Id}\t{location.Code}\t{location.TimeZone}\t{location.Name}");
                    }
                }
            }
            catch (Exception e)
            {
                Log.Logger.Fatal(e.Message);
                Log.Logger.Fatal(e.StackTrace);
            }
        }

        public void View(List<string> args)
        {
            if (args == null || args.Count < 1)
            {
                Console.WriteLine("Usage: DataCentral.Tool.exe restaurant view <id>");
                Console.WriteLine("For example, DataCentral.Tool.exe restaurant view 12");
                return;
            }

            try
            {
            }
            catch (Exception e)
            {
                Log.Logger.Fatal(e.Message);
                Log.Logger.Fatal(e.StackTrace);
            }
        }

        public void Types(List<string> args)
        {
            try
            {
                using (var service = new DataCentralService())
                {
                    var memos = service.GetPayMemos().Result;
                    foreach (var memo in memos)
                    {
                        Console.WriteLine($"{memo.Id}\t{memo.Description}");
                    }
                }
            }
            catch (Exception e)
            {
                Log.Logger.Fatal(e.Message);
                Log.Logger.Fatal(e.StackTrace);
            }
        }

        public void Transactions(List<string> args)
        {
            try
            {
                using (var service = new DataCentralService())
                {
                    IEnumerable<PayMemoTransaction> transactions = service.GetPayMemoTransactions().Result;
                    foreach (var transaction in transactions)
                    {
                        Console.WriteLine($"{transaction.Memo.Description}\t{transaction.Hours}\t{transaction.Comment}\t{transaction.Job.Description}");
                    }
                }
            }
            catch (Exception e)
            {
                Log.Logger.Fatal(e.Message);
                Log.Logger.Fatal(e.StackTrace);
            }
        }
    }
}