using System;
using System.Text.Json.Serialization;
using System.Collections.Generic;
namespace DataCentral.Tool.Models;


public class JobRateResponse
{
    [JsonPropertyName("RecordID")]
    public Guid RecordId { get; set; }

    [JsonPropertyName("LocalEmployeeID")]
    public Guid LocalEmployeeId { get; set; }

    [Json<PERSON>ropertyName("PayrollJobID")]
    public Guid PayrollJobId { get; set; }

    [JsonPropertyName("Rate")]
    public decimal Rate { get; set; }

    [JsonPropertyName("Approved")]
    public string Approved { get; set; }

    [JsonPropertyName("ApproveUser")]
    public string ApproveUser { get; set; }

    [Json<PERSON>ropertyName("EffectiveDate")]
    public DateTime EffectiveDate { get; set; }

    [JsonPropertyName("EndDate")]
    public DateTime EndDate { get; set; }
}
