using System;
using System.Text.Json.Serialization;
using System.Collections.Generic;
using Payroll.Shared;
using System.Text.Json;
using System.Text.Json.Nodes;
using System.Linq;
using System.Globalization;
using System.Runtime.Serialization;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using System.Dynamic;
using Newtonsoft.Json;
using JsonSerializer = System.Text.Json.JsonSerializer;

namespace DataCentral.Tool.Models;

using System;
using System.Text.Json.Serialization;

public enum EmployeeType
{
    StoreEmployee = 1,
    HRManagementEmployee = 2
}

public enum ClockType
{
    TimeClock = 1,
    [JsonPropertyName("Unpaid Break")]
    UnpaidBreak = 2,
    [JsonPropertyName("Paid Break")]
    PaidBreak = 3,
}

public class StoreReference
{
    [JsonPropertyName("ID")]
    public int Id { get; set; }

    [JsonPropertyName("Description")]
    public string Description { get; set; }

    [JsonPropertyName("Number")]
    public string Number { get; set; }

    [JsonPropertyName("Hidden")]
    public string Hidden { get; set; }
}

public class Store
{
    [JsonPropertyName("SubscriptionGroupDescription")]
    public string SubscriptionGroupDescription { get; set; }

    [JsonPropertyName("PeriodName")]
    public string PeriodName { get; set; }

    [JsonPropertyName("FiscalCalendarDescription")]
    public string FiscalCalendarDescription { get; set; }

    [JsonPropertyName("POSLinkingGroupDescription")]
    public string POSLinkingGroupDescription { get; set; }

    [JsonPropertyName("ForecastFormatDescription")]
    public string ForecastFormatDescription { get; set; }

    [JsonPropertyName("LocalCurrencyDescription")]
    public string LocalCurrencyDescription { get; set; }

    [JsonPropertyName("LocalTax1Description")]
    public string LocalTax1Description { get; set; }

    [JsonPropertyName("LocalTax2Description")]
    public string LocalTax2Description { get; set; }

    [JsonPropertyName("PayrollGroupDescription")]
    public string PayrollGroupDescription { get; set; }

    [JsonPropertyName("PayrollDistrictDescription")]
    public string PayrollDistrictDescription { get; set; }

    [JsonPropertyName("FieldsMappingDescription")]
    public string FieldsMappingDescription { get; set; }

    [JsonPropertyName("TimeZoneDescription")]
    public string TimeZoneDescription { get; set; }

    [JsonPropertyName("CountryDescription")]
    public string CountryDescription { get; set; }

    [JsonPropertyName("AddressStateDescription")]
    public string AddressStateDescription { get; set; }

    [JsonPropertyName("CogsShiftsDescription")]
    public string CogsShiftsDescription { get; set; }

    [JsonPropertyName("ImportCalendarDescription")]
    public string ImportCalendarDescription { get; set; }

    [JsonPropertyName("ID")]
    public int Id { get; set; }

    [JsonPropertyName("Description")]
    public string Description { get; set; }

    [JsonPropertyName("Number")]
    public string Number { get; set; }

    [JsonPropertyName("Hidden")]
    public string Hidden { get; set; }

    [JsonPropertyName("SubscriptionGroupId")]
    public string SubscriptionGroupId { get; set; }

    [JsonPropertyName("PeriodStart")]
    public DateTime PeriodStart { get; set; }

    [JsonPropertyName("PeriodEnd")]
    public DateTime PeriodEnd { get; set; }

    [JsonPropertyName("ExtendDays")]
    public int ExtendDays { get; set; }

    [JsonPropertyName("FiscalCalendarId")]
    public string FiscalCalendarId { get; set; }

    [JsonPropertyName("POSLinkingGroupId")]
    public string POSLinkingGroupId { get; set; }

    [JsonPropertyName("POSUnitId")]
    public string POSUnitId { get; set; }

    [JsonPropertyName("BusinessDayEnd")]
    public string BusinessDayEnd { get; set; }

    [JsonPropertyName("InServiceDate")]
    public DateTime? InServiceDate { get; set; }

    [JsonPropertyName("StoreCloseTime")]
    public string StoreCloseTime { get; set; }

    [JsonPropertyName("SameStoreSalesDate")]
    public DateTime? SameStoreSalesDate { get; set; }

    [JsonPropertyName("ForecastFormatId")]
    public string ForecastFormatId { get; set; }

    [JsonPropertyName("AccountMask")]
    public string AccountMask { get; set; }

    [JsonPropertyName("LocalCurrencyId")]
    public string LocalCurrencyId { get; set; }

    [JsonPropertyName("ImportCalendarId")]
    public string ImportCalendarId { get; set; }

    public string Address1 { get; set; }
    public string City { get; set; }
    public string Zip { get; set; }
}

public class StoreEmployee

{
    [JsonPropertyName("LocalEmployeeID")]
    public string LocalEmployeeId { get; set; }

    [JsonPropertyName("GlobalEmployeeID")]
    public string GlobalEmployeeId { get; set; }

    [JsonPropertyName("UnitID")]
    public int UnitId { get; set; }

    [JsonPropertyName("StoreID")]
    public int StoreId { get; set; }

    [JsonPropertyName("FirstName")]
    [JsonInclude]
    public string FirstName;

    [JsonPropertyName("LastName")]
    [JsonInclude]
    public string LastName;

    [JsonPropertyName("MiddleName")]
    public string MiddleName { get; set; }

    [JsonPropertyName("NickName")]
    [JsonInclude]
    public string NickName;
    public string Suffix { get; set; }
    public char Status { get; set; }
    public char Type { get; set; }

    [JsonPropertyName(name: "Address")]
    [JsonInclude]
    public string Address;
    public string Address2 { get; set; }

    [JsonPropertyName(name: "City")]
    [JsonInclude]
    public string City;

    [JsonPropertyName("StateID")]
    public string StateId { get; set; }

    public string StateDescription { get; set; }

    [JsonInclude]
    public string Zip;
    public string OriginalHireDate { get; set; }
    public string UniquePersonalID { get; set; }

    [JsonPropertyName("EmployeeID")]
    public int? EmployeeId { get; set; }

    [JsonPropertyName("POSPayrollID")]
    public string PosPayrollId { get; set; }

    public string ElectronicID { get; set; }

    [JsonPropertyName("TimeClockID")]
    public int? TimeClockId { get; set; }

    [JsonPropertyName("HomeUnitID")]
    public int HomeUnitId { get; set; }

    public string HomeUnitName { get; set; }

    [JsonPropertyName("PrimaryJobID")]
    [JsonInclude]
    public string PrimaryJobId;

    [JsonPropertyName("PrimaryJobDescription")]
    [JsonInclude]
    public string PrimaryJobDescription;

    [JsonPropertyName("Birthday")]
    [JsonInclude]
    public string Birthday;

    [JsonPropertyName("Email")]
    [JsonInclude]
    public string Email;
    
    // this is necessary because the phone number property is different for GET and POST
    [JsonPropertyName("PhoneNumber")] // Used for POST
    [JsonInclude]
    public string PhoneNumber;

    [JsonPropertyName("PrimaryPhone")] // Used for GET
    [JsonInclude]
    public string PrimaryPhone;

    public decimal? AnnualSalary { get; set; }
    public bool DynamicDailyScheduleCost { get; set; }
    public decimal? DailyScheduleCost { get; set; }
    public bool Display { get; set; }
    public bool IgnoreForPayroll { get; set; }
    public char Mode { get; set; }
    public char Borrowed { get; set; }
    public string FullPartTime { get; set; }
    public bool ClockExemptSchedule { get; set; }
    public bool ClockExemptAutoBreaks { get; set; }
    public bool ClockExemptPayDeducts { get; set; }
    public string Group1 { get; set; }
    public string Group2 { get; set; }
    public string Group3 { get; set; }
    public string Group4 { get; set; }
    public string Group5 { get; set; }
    public string Group6 { get; set; }
    public string Group7 { get; set; }
    public string Group8 { get; set; }
    public string Group9 { get; set; }
    public string Group10 { get; set; }
    public string Group11 { get; set; }
    public string Group12 { get; set; }
    public string Group13 { get; set; }
    public string Group14 { get; set; }
    public string Group15 { get; set; }
    public string Group16 { get; set; }
    public string Group17 { get; set; }
    public string Group18 { get; set; }
    public string Group19 { get; set; }
    public string Group20 { get; set; }
    public char Schedule { get; set; }
    [JsonConverter(typeof(BooleanConverter))]
    public bool ExemptMinor { get; set; }
    public string County { get; set; }
    public string MaidenName { get; set; }
    [JsonPropertyName("ClockInMethod")]
    [JsonConverter(typeof(CharConverter))]
    public char ClockInMethod { get; set; }
    [JsonConverter(typeof(BooleanConverter))]
    public bool Hide { get; set; }

    public int IsManager { get; set; }

    [JsonPropertyName("ApprovedStoreID")]
    public int ApprovedStoreId { get; set; }

    public string ApprovedStoreName { get; set; }

    public DateTime CreateDate { get; set; }

    public string CreateUser { get; set; }

    public bool MultiJobs { get; set; }

    public List<Job> Jobs { get; set; }
}

public class BooleanConverter : JsonConverter<bool>
{
    public override bool Read(ref Utf8JsonReader reader, Type typeToConvert, JsonSerializerOptions options)
    {
        switch (reader.TokenType)
        {
            case JsonTokenType.True:
                return true;
            case JsonTokenType.False:
                return false;
            case JsonTokenType.String:
                string stringValue = reader.GetString();
                if (string.IsNullOrEmpty(stringValue))
                    return false;
                return stringValue.Equals("Y", StringComparison.OrdinalIgnoreCase) || 
                       stringValue.Equals("1", StringComparison.OrdinalIgnoreCase) || 
                       stringValue.Equals("true", StringComparison.OrdinalIgnoreCase);
            case JsonTokenType.Number:
                return reader.GetInt32() != 0;
            default:
                return false;
        }
    }

    public override void Write(Utf8JsonWriter writer, bool value, JsonSerializerOptions options)
    {
        writer.WriteBooleanValue(value);
    }
}

public class CharConverter : JsonConverter<char>
{
    public override char Read(ref Utf8JsonReader reader, Type typeToConvert, JsonSerializerOptions options)
    {
        if (reader.TokenType == JsonTokenType.String)
        {
            string value = reader.GetString();
            if (value != null && value.Length > 0)
            {
                return value[0];
            }
        }
        else if (reader.TokenType == JsonTokenType.Number)
        {
            return (char)reader.GetInt32();
        }
        return '\0';
    }

    public override void Write(Utf8JsonWriter writer, char value, JsonSerializerOptions options)
    {
        writer.WriteNumberValue((int)value);
    }
}

/* DataCentral Report   */
public class TimeCardReport
{
    public List<TimeCardData> Data { get; set; }
}
public class PayMemoReport
{
    public List<PayMemoData> Data { get; set; }
}

public class Data
{
    public string TableName { get; set; }
    public int RowCount { get; set; }
}

public class TimeCardData : Data
{
    public List<TimeCardRow> Rows { get; set; }

}

public class PayMemoData : Data
{
    public List<PayMemoRow> Rows { get; set; }
}

public class TimeCardRow
{
    public int UnitID { get; set; }
    [JsonPropertyName("Unit Number")]
    public string UnitNumber { get; set; }
    [JsonPropertyName("Unit Name")]
    public string UnitName { get; set; }
    [JsonPropertyName("Employee Name")]
    public string EmployeeName { get; set; }
    [JsonPropertyName("Job Name")]
    public string JobName { get; set; }
    [JsonPropertyName("Business Date")]
    public DateTime BusinessDate { get; set; }
    public ClockType Type { get; set; }

    [JsonPropertyName("Clock In DateTime")]
    public DateTimeOffset ClockInDateTime { get; set; }
    [JsonPropertyName("Clock Out DateTime")]
    public DateTimeOffset ClockOutDateTime { get; set; }
    public string LastMUser { get; set; }
    public DateTime LastMDate { get; set; }
    [JsonPropertyName("Job GUID")]
    public Guid JobGUID { get; set; }
    [JsonPropertyName("LocalEmployee GUID")]
    public Guid LocalEmployeeGUID { get; set; }
    public string Modified { get; set; }
    public string IgnoreForPayroll { get; set; }
    public string DCForced { get; set; }
    public string Origin { get; set; }
    [JsonPropertyName("Report Origin")]
    public string ReportOrigin { get; set; }
}

public class PayMemoRow
{
    [JsonPropertyName("UnitNumber")]
    public string UnitNumber { get; set; }

    [JsonPropertyName("UnitName")]
    public string UnitName { get; set; }

    [JsonPropertyName("EmployeeName")]
    public string EmployeeName { get; set; }

    [JsonPropertyName("SSN")]
    public string SSN { get; set; }

    [JsonPropertyName("Earning")]
    public string Earning { get; set; }

    [JsonPropertyName("PayrollJob")]
    public string PayrollJob { get; set; }

    [JsonPropertyName("Origin")]
    public string Origin { get; set; }

    [JsonPropertyName("MemoDate")]
    public DateTime MemoDate { get; set; }

    [JsonPropertyName("LastModified")]
    public DateTime LastModified { get; set; }

    [JsonPropertyName("UserName")]
    public string UserName { get; set; }

    [JsonPropertyName("Amount")]
    public decimal Amount { get; set; }

    [JsonPropertyName("Group2")]
    public string Group2 { get; set; }

    [JsonPropertyName("Group3")]
    public string Group3 { get; set; }

    [JsonPropertyName("Group4")]
    public string Group4 { get; set; }

    [JsonPropertyName("Group5")]
    public string Group5 { get; set; }

    [JsonPropertyName("LocalEmployeeID")]
    public string LocalEmployeeID { get; set; }

    [JsonPropertyName("OriginalAmount")]
    public decimal OriginalAmount { get; set; }

    [JsonPropertyName("Report Origin  ")]
    public string ReportOrigin { get; set; }
}


public class JobReport
{
    public List<Job> Data { get; set; }
}

public class Job
{
    [JsonPropertyName("ID")]
    public string Id { get; set; }

    [JsonPropertyName("JobID")]
    public string JobId { get; set; }

    [JsonPropertyName("Description")]
    public string Description { get; set; }

    [JsonPropertyName("Tippable")]
    public bool Tippable { get; set; }

    [JsonPropertyName("Schedule")]
    public bool Schedule { get; set; }

    [JsonPropertyName("PayMode")]
    public PayMode PayMode { get; set; }

    [JsonPropertyName("Hide")]
    [JsonConverter(typeof(BooleanConverter))]
    public bool Hide { get; set; }

    [JsonPropertyName("TipShareMethod")]
    public string TipShareMethod { get; set; }

    [JsonPropertyName("UseInPool")]
    public bool UseInPool { get; set; }
    public int SkillLevel { get; set; }

}

public class PayMode
{
    [JsonPropertyName("ID")]
    public string Id { get; set; }

    [JsonPropertyName("Description")]
    public string Description { get; set; }
}

public partial class JobInfo
{
    [JsonPropertyName("ID")] public string Id { get; set; }

    [JsonPropertyName("Description")] public string Code { get; set; }
}

public class EmployeeStatus
{
    [JsonPropertyName("StatusID")]
    public string StatusId { get; set; }
    public string StatusReasonID { get; set; }
    public string EffectiveDate { get; set; }
    public string Comment { get; set; }
    [JsonConverter(typeof(BooleanConverter))]
    public bool Hide { get; set; }
}

public class MuEmployeeStatus
{
    [JsonPropertyName("LocalEmployeeID")]
    public string LocalEmployeeId { get; set; }
    [JsonPropertyName("RecordID")]
    public string RecordId { get; set; }
    [JsonPropertyName("ReasonID")]
    public string ReasonId { get; set; }
    public string Status { get; set; }
    public DateTime BusinessDate { get; set; }
    public string Comment { get; set; }
}

public class EmployeeJob
{
    [JsonPropertyName("RecordID")]
    public string RecordId { get; set; }

    [JsonPropertyName("PayrollJobID")]
    public string PayrollJobId { get; set; }

    [JsonPropertyName("SkillLevel")]
    public int SkillLevel { get; set; }

    [JsonPropertyName("Hide")]
    public string Hide { get; set; }
}

public class TimeCardBreak : Break
{
    public string EmployeeId { get; set; }
    public DateTime BusinessDate { get; set; }
}
/*
 * The Following models are used for the AddEmployee Response
 */
public class AddEmployeeResponse
{
    public List<EmployeeData> Data { get; set; }
}

public class EmployeeData
{
    public string StoreEmployeeID { get; set; }
    public string UniquePersonalNumber { get; set; }
    public StoreData Store { get; set; }
    public StoreData HomeStore { get; set; }
    public string FirstName { get; set; }
    public string MiddleName { get; set; }
    public string LastName { get; set; }
    public string NickName { get; set; }
    [JsonConverter(typeof(BooleanConverter))]
    public bool Hide { get; set; }
    public string ElectronicID { get; set; }
    public string EmployeeID { get; set; }
    public int TimeClockID { get; set; }
    public string PosPayrollID { get; set; }
    public string ClockInMethodID { get; set; }
    public bool Schedule { get; set; }
    public bool WebSchedule { get; set; }
    public bool ExemptMinor { get; set; }
    public bool ClockExemptSchedule { get; set; }
    public bool ClockExemptAutoBreaks { get; set; }
    public bool ClockExemptPayDeducts { get; set; }
    public bool Borrowed { get; set; }
    public bool IsManager { get; set; }
    public string Birthdate { get; set; }
    public DateTime OriginalHireDate { get; set; }
    public PayModeData PayMode { get; set; }
    public EmployeeTypeData EmployeeType { get; set; }
    public Status Status { get; set; }
    public List<StatusChange> StatusChanges { get; set; }
    public string Address { get; set; }
    public string Address2 { get; set; }
    public string City { get; set; }
    public string State { get; set; }
    public string PrimaryJob { get; set; }
    public List<JobData> Jobs { get; set; }
    public List<string> Flags { get; set; }
    public string FederalW4 { get; set; }
    public string StateW4 { get; set; }
    public string I9 { get; set; }
    public string CustomID1 { get; set; }
    public string CustomID2 { get; set; }
    public string CustomID3 { get; set; }
    public string CustomID4 { get; set; }
    public string CustomID5 { get; set; }
    public string CustomID6 { get; set; }
    public string CustomID7 { get; set; }
    public string CustomID8 { get; set; }
    public string CustomID9 { get; set; }
    public string CustomID10 { get; set; }
    public string CustomID11 { get; set; }
    public string CustomID12 { get; set; }
    public string CustomID13 { get; set; }
    public string CustomID14 { get; set; }
    public string CustomID15 { get; set; }
    public string CustomID16 { get; set; }
    public string CustomID17 { get; set; }
    public string CustomID18 { get; set; }
    public string CustomID19 { get; set; }
    public string CustomID20 { get; set; }
    public string MaidenName { get; set; }
    public string Suffix { get; set; }
    public string Zip { get; set; }
    public string County { get; set; }
    public string Email { get; set; }
    public string FullPartTime { get; set; }
    public decimal? AnnualSalary { get; set; }
    public string PrimaryPhone { get; set; }
    public bool PosLinked { get; set; }
    public bool FiredWithNonEligibleForRehireReason { get; set; }
}

public class StoreData
{
    public int ID { get; set; }
    public string Description { get; set; }
    public string Number { get; set; }
}

public class PayModeData
{
    public string ID { get; set; }
    public string Description { get; set; }
}

public class EmployeeTypeData
{
    public string ID { get; set; }
    public string Description { get; set; }
}

public class Status
{
    public string ID { get; set; }
    public string Description { get; set; }
}

public class StatusChange
{
    public DateTime EffectiveDate { get; set; }
    public bool Hide { get; set; }
    public string Comment { get; set; }
    public Status Status { get; set; }
    public Reason Reason { get; set; }
}

public class Reason
{
    public string ID { get; set; }
    public string Description { get; set; }
    public bool EligibleForRehire { get; set; }
    public string Scope { get; set; }
    public string ExportCode { get; set; }
}

public class JobData
{
    public int SkillLevel { get; set; }
    public bool Schedule { get; set; }
    [JsonConverter(typeof(BooleanConverter))]
    public bool Hide { get; set; }
    public bool SecuredRate { get; set; }
    public JobDetails Job { get; set; }
    public List<JobRate> JobRates { get; set; }
    public decimal CurrentRate { get; set; }
}

public class JobDetails
{
    public string ID { get; set; }
    public string Description { get; set; }
    public bool Tippable { get; set; }
    public bool Schedule { get; set; }
    public PayMode PayMode { get; set; }
    [JsonConverter(typeof(BooleanConverter))]
    public bool Hide { get; set; }
    public string TipShareMethod { get; set; }
    public bool UseInPool { get; set; }
}

public class JobRate
{
    // Define properties for JobRate if needed
}

public class JobPatch
{
    [JsonPropertyName("RecordID")]
    public string RecordId { get; set; }
    public char Hide { get; set; }
}