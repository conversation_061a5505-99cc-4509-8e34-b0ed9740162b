using Serilog;
using System;
using System.Collections.Generic;
using System.Globalization;
using System.Linq;
using System.Net;
using System.Net.Http;
using System.Text;
using System.Text.Json;
using System.Threading;
using System.Threading.Tasks;
using Payroll.Shared;
using System.Dynamic;
using System.Text.Json.Serialization;
using DataCentral.Tool.Models;
using Newtonsoft.Json;
using JsonSerializer = System.Text.Json.JsonSerializer;
using System.Security.Cryptography.X509Certificates;

namespace DataCentral.Tool
{
    public class DataCentralService : IDisposable
    {
        private uint TotalApiRequestsMade { get; set; }
        private DateTime StartTime { get; set; }
        private LocationService LocationService { get; set; } = new LocationService();
        static DataCentralService()
        {
        }

        public DataCentralService()
        {
            TotalApiRequestsMade = 0;
            StartTime = DateTime.Now;
        }

        public Uri EndpointForPath(string path)
        {
            return new Uri($"{Config.Endpoint}/{path}");
        }

        private void LogApiRequest()
        {
            TotalApiRequestsMade++;
            if (Config.ApiDelayInSeconds > 0) Thread.Sleep(Config.ApiDelayInSeconds * 1000);
        }

        private HttpClient GetAuthenticatedHttpClient()
        {
            var client = new HttpClient();
            client.DefaultRequestHeaders.Add("DCKey", Config.DC_Key);
            Log.Logger.Debug("Api Key: {rid}", Config.DC_Key);
            return client;
        }

        /// Get Methods ///////////////////////////////////////////////////////////////////////////////////////////
        
        public async Task<List<Location>> GetLocationsAsync()
        {
            using (var locationService = new LocationService())
            using (var client = GetAuthenticatedHttpClient())
            {
                var endpoint = EndpointForPath($"v2/stores");
                Log.Logger.Debug("Fetching stores via {url}", endpoint);
                var response = await client.GetStringAsync(endpoint);

                // Create JsonSerializerOptions with WriteIndented set to true for pretty printing
                var options = new JsonSerializerOptions
                {
                    WriteIndented = true
                };

                // Use the options for logging the formatted JSON
                var formattedResponse = JsonSerializer.Serialize(JsonSerializer.Deserialize<object>(response), options);
                LogApiRequest();
                
                var stores = JsonSerializer.Deserialize<List<Models.Store>>(response);
                var locations = new List<Location>();

                foreach (var store in stores)
                {
                    var location = Converter.StoreToLocation(store);
                    var success = await locationService.DecorateLocationWithTimeZoneInfo(Config.POS_LOCATIONS_CACHE_COLLECTION, location);

                    if (!success)
                    {
                        Log.Logger.Warning("Failed to decorate location {id} with timezone info", location.Id);
                    }

                    locations.Add(location);
                }

                return locations;
            }
        }
        
        public async Task<DataCentral.Tool.Models.JobReport> GetJobsAsync()
        {
            using (var client = GetAuthenticatedHttpClient())
            {
                var endpoint = EndpointForPath("HRManagement/Jobs");
                Log.Logger.Debug("Fetching jobs via {url}", endpoint);
                var response = await client.GetStringAsync(endpoint);
                var apiResponse = JsonSerializer.Deserialize<JsonElement>(response);
                //Log.Logger.Debug(JsonSerializer.Serialize(apiResponse, Json.DefaultSerializerOutputStyle));
                LogApiRequest();

                var jobs = JsonSerializer.Deserialize<DataCentral.Tool.Models.JobReport>(response);
                return jobs;
            }
        }

        public async Task<StoreEmployee> GetStoreEmployeeAsync(string employeeLocalId)
        {
            using (var client = GetAuthenticatedHttpClient())
            {
                var endpoint = EndpointForPath($"v2/MUEmployee/storeemployees/{employeeLocalId}");
                Log.Logger.Debug("Fetching store employee via {url}", endpoint);
                var response = await client.GetStringAsync(endpoint);
                LogApiRequest();
                // Log the full response to debug
                Log.Logger.Debug("Full response for store employee:");
                Log.Logger.Debug(response);
            
                var apiResponse = JsonSerializer.Deserialize<JsonElement>(response);
                return JsonSerializer.Deserialize<Models.StoreEmployee>(response);
            }
        }

        public async Task<HrEmployee> GetHrEmployeeAsync(string storeEmployeeId)
        {
            using (var client = GetAuthenticatedHttpClient())
            {
                var endpoint = EndpointForPath($"HRManagement/Employees?StoreEmployeeID={storeEmployeeId}");
                Log.Logger.Debug("Fetching hr employee via {url}", endpoint);
                var response = await client.GetStringAsync(endpoint);
                LogApiRequest();

                var apiResponse = JsonSerializer.Deserialize<JsonElement>(response);
                var dataElement = apiResponse.GetProperty("Data");
                if (dataElement.ValueKind != JsonValueKind.Array || dataElement.GetArrayLength() == 0)
                    return new HrEmployee();

                Log.Logger.Debug(JsonSerializer.Serialize(dataElement[0], Json.DefaultSerializerOutputStyle));
                return JsonSerializer.Deserialize<Models.HrEmployee>(dataElement[0]);
            }
        }
        public async Task<EmployeeJob> GetEmployeeJobAsync(StoreEmployee employee, string payrollJobId)
        {
            using (var client = GetAuthenticatedHttpClient())
            {
                var endpoint = EndpointForPath($"v2/MUEmployee/storeemployees/{employee.GlobalEmployeeId}/jobs");
                Log.Logger.Debug("Fetching employee jobs via {url}", endpoint);
                var response = await client.GetStringAsync(endpoint);
                LogApiRequest();

                var options = new JsonSerializerOptions
                {
                    PropertyNameCaseInsensitive = true
                };

                var jobs = JsonSerializer.Deserialize<List<EmployeeJob>>(response, options);

                var matchingJob = jobs.FirstOrDefault(job => job.PayrollJobId == payrollJobId);

                if (matchingJob != null)
                {
                    Log.Logger.Debug("Found job: {Job}", JsonSerializer.Serialize(matchingJob, Json.DefaultSerializerOutputStyle));
                    return matchingJob;
                }

                Log.Logger.Warning("No job found with PayrollJobID: {PayrollJobID} for employee: {EmployeeID}", payrollJobId, employee.GlobalEmployeeId);
                return null;
            }
        }

        public async Task<List<EmployeeJob>> GetAllEmployeeJobsAsync(StoreEmployee employee)
        {
            
            Log.Logger.Debug("Employee: {employee}", JsonSerializer.Serialize(employee, new JsonSerializerOptions { WriteIndented = true }));
            using (var client = GetAuthenticatedHttpClient())
            {
                var endpoint = EndpointForPath($"v2/MUEmployee/storeemployees/{employee.GlobalEmployeeId}/jobs");
                Log.Logger.Debug("Fetching all jobs for employee {employeeId} via {url}", employee.GlobalEmployeeId, endpoint);
                
                var response = await client.GetStringAsync(endpoint);
                LogApiRequest();

                var options = new JsonSerializerOptions
                {
                    PropertyNameCaseInsensitive = true
                };

                var jobs = JsonSerializer.Deserialize<List<EmployeeJob>>(response, options);

                if (jobs != null && jobs.Any())
                {
                    Log.Logger.Debug("Retrieved {count} jobs for employee {employeeId}", jobs.Count, employee.GlobalEmployeeId);
                    return jobs;
                }
                else
                {
                    Log.Logger.Warning("No jobs found for employee {employeeId}", employee.GlobalEmployeeId);
                    return new List<EmployeeJob>();
                }
            }
        }
        private async Task<List<T>> GetDataCentralEmployeesAsync<T>(string unitId)
        {
            using (var client = GetAuthenticatedHttpClient())
            {
                Uri endpoint;
                string employeeType;

                if (typeof(T) == typeof(Models.HRManagementEmployee))
                {
                    endpoint = EndpointForPath($"HRManagement/Employees?StoreID={unitId}");
                }
                else if (typeof(T) == typeof(Models.StoreEmployee))
                {
                    endpoint = EndpointForPath($"v2/MUEmployee/storeemployees?UnitID={unitId}");
                }
                else
                {
                    throw new ArgumentException($"Unsupported employee type: {typeof(T).Name}");
                }

                Log.Logger.Debug("Fetching {0} employees via {{url}}", typeof(T).Name, endpoint);
                var response = await client.GetStringAsync(endpoint);
                LogApiRequest();

                var apiResponse = JsonSerializer.Deserialize<JsonElement>(response);
                //Log.Logger.Debug(JsonSerializer.Serialize(apiResponse, Json.DefaultSerializerOutputStyle));

                return JsonSerializer.Deserialize<List<T>>(response);
            }
        }

        public async Task<List<Employee>> GetEmployeesAsync(string unitId)
        {
            var storeEmployees = await GetDataCentralEmployeesAsync<StoreEmployee>(unitId);
            var employees = new List<Employee>();

            foreach (var storeEmployee in storeEmployees)
            {
                var employee = Converter.StoreEmployeeToEmployee(storeEmployee);
                employees.Add(employee);
            }

            return employees;
        }

        public async Task<Dictionary<string, Dictionary<string, Models.HRManagementEmployee>>> GetAllHRManagementEmployeesAsync(List<Location> locations)
        {
            var allEmployees = new Dictionary<string, Dictionary<string, Models.HRManagementEmployee>>();
            foreach (var location in locations)
            {
                var employees = await GetDataCentralEmployeesAsync<Models.HRManagementEmployee>(location.Id);
                var employeeMap = new Dictionary<string, Models.HRManagementEmployee>();

                foreach (var employee in employees)
                {
                    if (employee.IsManager)
                    {
                        Log.Logger.Debug("Skipping employee {id}, marked IgnoreForPayroll", employee.StoreEmployeeId);
                        continue;
                    }

                    if (string.IsNullOrEmpty(employee.UniquePersonalNumber))
                    {
                        // seems like most of these are NON-employee records, more like fake ones for online ordering, etc. (store is set to 0 for the fake ones)
                        if (string.IsNullOrEmpty(employee.StoreId))
                            Log.Logger.Information("Skipping employee {id}/{nm} {lm} with a null unique personal id", employee.StoreEmployeeId, employee.FirstName, employee.LastName);
                        continue;
                    }

                    if (employeeMap.ContainsKey(employee.UniquePersonalNumber))
                    {
                        var currEmp = employeeMap[employee.UniquePersonalNumber];
                        if (currEmp.Status.StatusId != "A" || currEmp.UniquePersonalNumber != currEmp.UniquePersonalNumber)
                        {
                            employeeMap[employee.UniquePersonalNumber] = employee;
                            continue;
                        }

                        string js1 = JsonSerializer.Serialize(employeeMap[employee.UniquePersonalNumber], Json.DefaultSerializerOutputStyle);
                        string js2 = JsonSerializer.Serialize(employee, Json.DefaultSerializerOutputStyle);

                        // this is a rubric to resolve this issue, nothing scientific, just choosing whichever record has more data
                        if (js2.Length > js1.Length)
                        {
                            employeeMap[employee.UniquePersonalNumber] = employee;
                        }

                        Log.Logger.Warning("Duplicate POS payroll id for employee {id}, not resolved by status or unique personal id", employee.UniquePersonalNumber);

                        Log.Logger.Warning("  1: " + js1);
                        Log.Logger.Warning("  2: " + js2);

                        continue;
                    }

                    employeeMap.Add(employee.UniquePersonalNumber, employee);
                }

                if (!allEmployees.ContainsKey(location.Id))
                {
                    allEmployees.Add(location.Id, new Dictionary<string, Models.HRManagementEmployee>());
                }

                allEmployees[location.Id] = employeeMap;
            }

            return allEmployees;
        }

        public async Task<Dictionary<string, Dictionary<string, Models.StoreEmployee>>> GetAllStoreEmployees(List<Location> locations)
        {
            var allEmployees = new Dictionary<string, Dictionary<string, Models.StoreEmployee>>();
            foreach (var location in locations)
            {
                var employees = await GetDataCentralEmployeesAsync<Models.StoreEmployee>(location.Id);
                var employeeMap = new Dictionary<string, Models.StoreEmployee>();
                
                foreach (var employee in employees)
                {
                    if (employee.IgnoreForPayroll)
                    {
                        Log.Logger.Debug("Skipping employee {id}, marked IgnoreForPayroll", employee.LocalEmployeeId);
                        continue;
                    }

                    if (string.IsNullOrEmpty(employee.UniquePersonalID))
                    {
                        // seems like most of these are NON-employee records, more like fake ones for online ordering, etc. (store is set to 0 for the fake ones)
                        if (employee.StoreId != 0)
                            Log.Logger.Information("Skipping employee {id}/{nm} {lm} with a null unique personal id", employee.LocalEmployeeId, employee.FirstName, employee.LastName);
                        continue;
                    }
                    
                    if (employeeMap.ContainsKey(employee.UniquePersonalID))
                    {
                        var currEmp = employeeMap[employee.UniquePersonalID];
                        if (currEmp.Status != 'A' || currEmp.UniquePersonalID != currEmp.UniquePersonalID)
                        {
                            employeeMap[employee.UniquePersonalID] = employee;
                            continue;
                        }

                        string js1 = JsonSerializer.Serialize(employeeMap[employee.UniquePersonalID], Json.DefaultSerializerOutputStyle);
                        string js2 = JsonSerializer.Serialize(employee, Json.DefaultSerializerOutputStyle);

                        // this is a rubric to resolve this issue, nothing scientific, just choosing whichever record has more data
                        if (js2.Length > js1.Length)
                        {
                            employeeMap[employee.UniquePersonalID] = employee;
                        }

                        Log.Logger.Warning("Duplicate POS payroll id for employee {id}, not resolved by status or unique personal id", employee.UniquePersonalID);

                        Log.Logger.Warning("  1: " + js1);
                        Log.Logger.Warning("  2: " + js2);

                        continue;
                    }

                    employeeMap.Add(employee.UniquePersonalID, employee);
                }

                if (!allEmployees.ContainsKey(location.Id))
                {
                    allEmployees.Add(location.Id, new Dictionary<string, Models.StoreEmployee>());
                }

                allEmployees[location.Id] = employeeMap;  
            }

            return allEmployees;    
        }

        public async Task<List<JobRateResponse>> GetEmployeeJobRates(Guid localEmployeeId, Guid payrollJobId)
        {
            using (var client = GetAuthenticatedHttpClient())
            {
                var endpoint = EndpointForPath($"v2/MUEmployee/{localEmployeeId}/jobs/{payrollJobId}/rates");
                Log.Logger.Debug("Fetching employee job rates via {url}", endpoint);
                
                var response = await client.GetAsync(endpoint);
                LogApiRequest();

                if (!response.IsSuccessStatusCode)
                {
                    Log.Logger.Error("Failed to fetch employee job rates. Status code: {statusCode}", response.StatusCode);
                    return null;
                }

                var content = await response.Content.ReadAsStringAsync();
                Log.Logger.Debug("API Response: {response}", content);

                var jobRateResponses = JsonSerializer.Deserialize<List<JobRateResponse>>(content);
                
                if (jobRateResponses == null || !jobRateResponses.Any())
                {
                    return new List<JobRateResponse>();
                }

                return jobRateResponses;
            }
        }
        public async Task<bool> PostEmployeeJobRate(Guid localEmployeeId, Guid payrollJobId, Guid recordId, DateTime effectiveDate, decimal rate, decimal currentRate)
        {
            using (var client = GetAuthenticatedHttpClient())
            {
                var endpoint = EndpointForPath($"v2/MUEmployee/{localEmployeeId}/jobs/{payrollJobId}/rates");
                Log.Logger.Debug("Posting employee job rate via {url}", endpoint);

                var jobRatePost = new
                {
                    RecordID = recordId,
                    EffectiveDate = effectiveDate.ToString("yyyy-MM-dd"),
                    Approved = "Y",
                    Rate = rate,
                    CurrentRate = currentRate
                };

                var options = new JsonSerializerOptions(Json.DefaultSerializerOutputStyle)
                {
                    DefaultIgnoreCondition = System.Text.Json.Serialization.JsonIgnoreCondition.Never
                };

                var body = JsonSerializer.Serialize(jobRatePost, options);
                Log.Logger.Debug("Request body for job rate post: {body}", body);
                var content = new StringContent(body, Encoding.UTF8, "application/json");

                var response = await client.PostAsync(endpoint, content);
                LogApiRequest();

                if (!response.IsSuccessStatusCode)
                {
                    var responseContent = await response.Content.ReadAsStringAsync();
                    Log.Logger.Error("Failed to post employee job rate. Status code: {StatusCode}. Response: {Response}", response.StatusCode, responseContent);
                    return false;
                }

                Log.Logger.Information("Employee job rate posted successfully. Status code: {StatusCode}", response.StatusCode);
                return true;
            }
        }
        public async Task<bool> PatchEmployeeJobRate(Guid recordId, Guid localEmployeeId, Guid payrollJobId, decimal rate, DateTime effectiveDate)
        {
            using (var client = GetAuthenticatedHttpClient())
            {
                var endpoint = EndpointForPath("v2/MUEmployee/jobs/rates");
                Log.Logger.Debug("Patching employee job rate via {url}", endpoint);

                var jobRatePatch = new[]
                {
                    new
                    {
                        RecordID = recordId,
                        EffectiveDate = effectiveDate.ToString("yyyy-MM-dd"),
                        Approved = "Y",
                        Rate = rate,
                        LocalEmployeeID = localEmployeeId,
                        PayrollJobID = payrollJobId
                    }
                };

                var body = JsonSerializer.Serialize(jobRatePatch, Json.DefaultSerializerOutputStyle);
                Log.Logger.Debug("Request body for job rate patch: {body}", body);
                var content = new StringContent(body, Encoding.UTF8, "application/json");

                var response = await client.PatchAsync(endpoint, content);
                LogApiRequest();

                if (!response.IsSuccessStatusCode)
                {
                    var responseContent = await response.Content.ReadAsStringAsync();
                    Log.Logger.Error("Failed to patch employee job rate. Status code: {StatusCode}. Response: {Response}", response.StatusCode, responseContent);
                    return false;
                }

                Log.Logger.Information("Employee job rate patched successfully. Status code: {StatusCode}", response.StatusCode);
                return true;
            }
        }

        public async Task<IEnumerable<PayMemo>> GetPayMemos()
        {
            using (var client = GetAuthenticatedHttpClient())
            {
                var endpoint = EndpointForPath("Payroll/PayMemos");
                Log.Logger.Debug("Fetching current pay period pay memo types via {url}", endpoint);
                var response = await client.GetStringAsync(endpoint);
                var apiResponse = JsonSerializer.Deserialize<JsonElement>(response);
                Log.Logger.Debug(JsonSerializer.Serialize(apiResponse, Json.DefaultSerializerOutputStyle));
                LogApiRequest();

                var memos = JsonSerializer.Deserialize<List<PayMemo>>(apiResponse.GetProperty("Data"));
                return memos;
            }
        }

        public async Task<IEnumerable<PayMemoTransaction>> GetPayMemoTransactions()
        {
            using (var client = GetAuthenticatedHttpClient())
            {
                var endpoint = EndpointForPath("Payroll/PayMemoTransactions?StartDate=2024-07-15&ThruDate=2024-07-29");
                Log.Logger.Debug("Fetching current pay period pay memos transactions via {url}", endpoint);
                var response = await client.GetStringAsync(endpoint);
                var apiResponse = JsonSerializer.Deserialize<JsonElement>(response);
                Log.Logger.Debug(JsonSerializer.Serialize(apiResponse, Json.DefaultSerializerOutputStyle));
                LogApiRequest();

                List<PayMemoTransaction> transactions = JsonSerializer.Deserialize<List<PayMemoTransaction>>(apiResponse.GetProperty("Data"));
                return transactions;
            }
        }

        public async Task<bool> AddPayMemoTransaction(PayMemoTransactionV2 payMemo)
        {
            using (var client = GetAuthenticatedHttpClient())
            {
                var endpoint = EndpointForPath("v2/PayMemoTransaction/Transactions");
                Log.Logger.Debug("Posting new pay memo transaction via {url}", endpoint);

                
                var memoArray = new[] { payMemo };

                var body = JsonSerializer.Serialize(memoArray, Json.DefaultSerializerOutputStyle);
                Log.Logger.Debug("Request body for pay memo transaction: {body}", body);
                var content = new StringContent(body, Encoding.UTF8, "application/json");

                var response = await client.PutAsync(endpoint, content);
                LogApiRequest();

                string responseBody = await response.Content.ReadAsStringAsync();
                Log.Logger.Debug(responseBody);

                if (!response.IsSuccessStatusCode)
                {
                    Log.Logger.Error("Failed to add pay memo transaction: {msg}", response.ReasonPhrase);
                    Log.Logger.Debug("Status Code: {StatusCode}", response.StatusCode);
                    Log.Logger.Debug("Content: {Content}", responseBody);
                    return false;
                }

                return true;
            }
        }

        public async Task<EmployeeData> AddEmployee(HRManagementEmployee employee)
        {
            using (var client = GetAuthenticatedHttpClient())
            {
                var endpoint = EndpointForPath("HRManagement/Employees");
                Log.Logger.Debug("Posting new employee via {url}", endpoint);
                Log.Logger.Debug("employee: {employee}", JsonSerializer.Serialize(employee, new JsonSerializerOptions { WriteIndented = true  }));
                var body = JsonSerializer.Serialize(employee, new JsonSerializerOptions{   
                        DefaultIgnoreCondition = JsonIgnoreCondition.Never});
                body = "[" + body + "]"; //todo - clean this up; it's a hack to make the API happy
                
                Log.Logger.Debug("body: {body}", body);

                var content = new StringContent(body, Encoding.UTF8, "application/json");

                var response = await client.PostAsync(endpoint, content);
                LogApiRequest();
                string responseBody = await response.Content.ReadAsStringAsync();
                Log.Logger.Debug("response: {response}", JsonSerializer.Serialize(response, new JsonSerializerOptions { WriteIndented = true }));

                if (!response.IsSuccessStatusCode)
                {
                    Log.Logger.Error("Failed to add employee: {msg}", response.ReasonPhrase);
                    Log.Logger.Error(responseBody);
                    return null;
                }

                Log.Logger.Debug(responseBody);
                var addedEmployee = JsonSerializer.Deserialize<AddEmployeeResponse>(responseBody);

                //debug log the addedEmployee
                Log.Logger.Debug("addedEmployee: {addedEmployee}", JsonSerializer.Serialize(addedEmployee, new JsonSerializerOptions { WriteIndented = true }));
                
                return addedEmployee.Data[0];

            }
        }
        
        //HRManagement endpoint should not be used to Patch.  ONLY POST (for now)
        private async Task<bool> PatchEmployee(HRManagementEmployee employee)
        {
            using (var client = GetAuthenticatedHttpClient())
            {
                Employee addedEmployee = null;
                var endpoint = EndpointForPath("HRManagement/Employees");
                Log.Logger.Debug("Patching employee via {url}", endpoint);
                Log.Logger.Debug("employee: {employee}", JsonSerializer.Serialize(employee, new JsonSerializerOptions { WriteIndented = true }));

                var employeeArray = new[] { employee };
                var body = JsonSerializer.Serialize(employeeArray, new JsonSerializerOptions { WriteIndented = true });
                
                Log.Logger.Debug("body: {body}", body);

                var content = new StringContent(body, Encoding.UTF8, "application/json");
                
                var msg = new HttpRequestMessage()
                {
                    Content = content,
                    Method = HttpMethod.Patch,
                    RequestUri = endpoint
                };

                var response = await client.SendAsync(msg);
                LogApiRequest();
                string responseBody = await response.Content.ReadAsStringAsync();

                if (!response.IsSuccessStatusCode)
                {
                    Log.Logger.Error("Failed to update employee: {msg}", response.ReasonPhrase);
                    Log.Logger.Error(responseBody);
                    return false;
                }

                Log.Logger.Debug(responseBody);

                return true;
            }
        }

        public async Task<bool> PatchEmployee(StoreEmployee employee)
        {
            var defaultSerializerOptions = new JsonSerializerOptions 
            { 
                WriteIndented = true, 
                DefaultIgnoreCondition = JsonIgnoreCondition.WhenWritingDefault 
            };

            using (var client = GetAuthenticatedHttpClient())
            {
                var endpoint = EndpointForPath("v2/MUEmployee/storeemployee");
                Log.Logger.Debug("Updating employee via {url}", endpoint);
                
                // Log the employee object being sent
                var body = JsonSerializer.Serialize(employee, defaultSerializerOptions);
                Log.Logger.Debug("Request payload: {body}", body);

                var content = new StringContent(body, Encoding.UTF8, "application/json");
                var msg = new HttpRequestMessage()
                {
                    Content = content,
                    Method = HttpMethod.Patch,
                    RequestUri = endpoint
                };

                var response = await client.SendAsync(msg);
                LogApiRequest();
                string responseBody = await response.Content.ReadAsStringAsync();

                if (!response.IsSuccessStatusCode)
                {
                    Log.Logger.Error("Failed to update employee: Status Code {statusCode}", response.StatusCode);
                    Log.Logger.Error("Response reason: {reason}", response.ReasonPhrase);
                    Log.Logger.Error("Response body: {responseBody}", responseBody);
                    return false;
                }

                Log.Logger.Debug("Response: {responseBody}", responseBody);
                return true;
            }
        }
        
        public async Task<bool> PatchJobAsync(JobPatch jobPatch)
        {
            using (var client = GetAuthenticatedHttpClient())
            {
                var endpoint = EndpointForPath("v2/MUEmployee/jobs");
                Log.Logger.Debug("Updating job via {url}", endpoint);

                var content = new StringContent("[" + JsonSerializer.Serialize(jobPatch) + "]", Encoding.UTF8, "application/json");
                var response = await client.PatchAsync(endpoint, content);
                LogApiRequest();

                if (response.IsSuccessStatusCode)
                {
                    Log.Logger.Information("Job updated successfully. Status code: {StatusCode}", response.StatusCode);
                    return true;
                }
                else
                {
                    var responseContent = await response.Content.ReadAsStringAsync();
                    Log.Logger.Error("Failed to update job. Status code: {StatusCode}. Response: {Response}", response.StatusCode, responseContent);
                    return false;
                }
            }
        }


        public async Task<bool> ChangeEmployeeStatus(string storeEmployeeID, MuEmployeeStatus status)
        {
            using (var client = GetAuthenticatedHttpClient())
            {
                var endpoint = EndpointForPath($"v2/MUEmployee/status");
                Log.Logger.Debug("change employees status via {url}", endpoint);

                
                Log.Logger.Debug("status: {status}", JsonSerializer.Serialize(status, new JsonSerializerOptions { WriteIndented = true }));

                var body = JsonSerializer.Serialize(status);
                var content = new StringContent(body, Encoding.UTF8, "application/json");

                var response = await client.PostAsync(endpoint, content);
                
                LogApiRequest();
                if (!response.IsSuccessStatusCode)
                {
                    Log.Logger.Error("Failed to change employee status: {msg}", response.ReasonPhrase);
                    Log.Logger.Error(await response.Content.ReadAsStringAsync());
                    return false;
                }

                return true;
            }
        }

        private bool LoggedDateParsing(string label, string date, out DateTime outDate)
        {
            if (string.IsNullOrEmpty(date))
            {
                Log.Logger.Debug("{label} - null or empty date", label);
                outDate = DateTime.MinValue;
                return false;
            }

            if (!DateTime.TryParseExact(date, "yyyy-MM-ddTHH:mm:ss.FFF+0000",
                CultureInfo.CurrentCulture, DateTimeStyles.AssumeLocal, out outDate))
            {
                Log.Logger.Warning("{label} - failed to parse date {date}", label, date);
                return false;
            }

            return true;
        }

        public async Task<List<PunchPair>> TimePunchesUsingStartEndDate(Location location, DateTimeOffset startDate, DateTimeOffset endDate)
        {
            var punches = new List<PunchPair>();
            var breaksByEmployeeGuid = new Dictionary<string, List<TimeCardBreak>>();
            var tipsByEmployeeGuid = new Dictionary<string, List<PayMemoRow>>();

            try
            {
                // load our employee map and job map
                //var employeeMap = await EmployeesForRestaurant(restaurantId);
                var employeeMap = await EmployeesForRestaurant(location.Id);
                //Log.Logger.Debug("employeeMap: {employeeMap}", JsonSerializer.Serialize(employeeMap, new JsonSerializerOptions { WriteIndented = true }));
                var jobMap = Config.JobNameMap();
                string jobMapJson = JsonSerializer.Serialize(jobMap); // Corrected line
                //Log.Logger.Debug(jobMapJson);

                var jobs = Config.JobsById();
                var timeCardReport = Config.TimecardReport;
                var payMemoReport = Config.PaymemoReport;

                Dictionary<DateTime, TimeSpan> timeZoneByDay = null;
                if (Config.AdjustForTimeZone)
                {
                    var dateOnlyTimeZones = LocationService.GetTimeZoneOffsets(location, startDate, endDate);
                    timeZoneByDay = dateOnlyTimeZones.ToDictionary(
                        kvp => kvp.Key.ToDateTime(TimeOnly.MinValue),
                        kvp => kvp.Value
                    );
                }

                using (var client = GetAuthenticatedHttpClient())
                {
                    var startDateStr = startDate.ToString("yyyy-MM-dd");
                    var endDateStr = endDate.ToString("yyyy-MM-dd");

                    var endpoint =
                        EndpointForPath(
                            $"Reports/{timeCardReport}/Result?@UnitID={location.Id}");
                    
                    var tipEndpoint = EndpointForPath($"Reports/{payMemoReport}/Result?@UnitID={location.Id}");

                    if (startDateStr != null && endDateStr != null)
                    {
                        endpoint =
                        EndpointForPath(
                            $"Reports/{timeCardReport}/Result?@UnitID={location.Id}&@StartDate={startDateStr}&@EndDate={endDateStr}");
                        //DC reports use different parameter names for date ranges
                        tipEndpoint = EndpointForPath(
                            $"Reports/{payMemoReport}/Result?@UnitID={location.Id}&@FromDate={startDateStr}&@ThruDate={endDateStr}");
                    }
                  
                    Log.Logger.Debug("Fetching punches via {url}", endpoint);

                    var options = new JsonSerializerOptions
                    {
                        WriteIndented = true,
                        Converters = { new ClockTypeConverter() }

                    };
                    var response = await client.GetStringAsync(endpoint);
                    
                    var tipResponse = await client.GetStringAsync(tipEndpoint);

                    // Use the options for logging the formatted JSON
                    //var formattedResponse = JsonSerializer.Serialize(JsonSerializer.Deserialize<object>(response), options);
                    //Log.Logger.Debug("Response: {formattedResponse}", formattedResponse);

                    LogApiRequest();

                    //var dcReport = JsonConvert.DeserializeObject<Report>(response);
                    var dcReport = JsonSerializer.Deserialize<TimeCardReport>(response, options);
                    var tipReport = JsonSerializer.Deserialize<PayMemoReport>(tipResponse, options);

                    if (dcReport.Data[0].Rows.Count == 0)
                    {
                        Log.Logger.Information("No punches found for {date} to {edate} @ location {loc}.", startDateStr, endDateStr, location.Id);
                    }
                    
                    if (tipReport.Data[0].Rows.Count == 0)
                    {
                        Log.Logger.Information("No tips found for {date} to {edate} @ location {loc}.", startDateStr, endDateStr, location.Id);
                    }

                    if (Config.AdjustForTimeZone)
                    {
                        foreach (TimeCardRow row in dcReport.Data[0].Rows)
                        {
                            row.ClockInDateTime = new DateTimeOffset(row.ClockInDateTime.DateTime, 
                                timeZoneByDay[row.BusinessDate]);
                            row.ClockOutDateTime = new DateTimeOffset(row.ClockOutDateTime.DateTime, 
                                timeZoneByDay[row.BusinessDate]);
                        }
                    }
                    
                    //foreach tipReport.Data[0].Rows add the tips to the tipsByEmployeeGuid dictionary
                    foreach (var entry in tipReport.Data[0].Rows)
                    {
                        if (tipsByEmployeeGuid.ContainsKey(entry.LocalEmployeeID))
                        {
                            tipsByEmployeeGuid[entry.LocalEmployeeID].Add(entry);
                        }
                        else
                        {
                            tipsByEmployeeGuid.Add(entry.LocalEmployeeID, new List<PayMemoRow> { entry });
                        }              
                    }
                    
                    foreach (var entry in dcReport.Data[0].Rows)
                    {
                        if (!employeeMap.TryGetValue(entry.LocalEmployeeGUID.ToString(), out Models.StoreEmployee storeEmployee))
                        {
                            Log.Logger.Warning("Failed to find employee {eid} at restaurant {rid}, skipping punch",
                                entry.LocalEmployeeGUID, location.Id);
                            continue;
                        }

                        if (storeEmployee.Mode == 'S')
                        {
                            Log.Logger.Warning("Skipping punch for employee {empNumber} because it is a salaried employee", entry.LocalEmployeeGUID);
                            continue;
                        }

                        if (TryTimeEntryToBreak(entry, out var pb))
                        {
                            if (breaksByEmployeeGuid.ContainsKey(pb.EmployeeId))
                            {
                                breaksByEmployeeGuid[pb.EmployeeId].Add(pb);
                            }
                            else
                            {
                                breaksByEmployeeGuid.Add(pb.EmployeeId, new List<TimeCardBreak> { pb });
                            }
                        }
                    }

                    //needs to be separate loop to gather all breaks first
                    foreach (var entry in dcReport.Data[0].Rows)
                    {
                        if (!employeeMap.TryGetValue(entry.LocalEmployeeGUID.ToString(), out Models.StoreEmployee storeEmployee))
                        {
                            Log.Logger.Warning("Failed to find employee {eid} at restaurant {rid}, skipping punch",
                                entry.LocalEmployeeGUID, location.Id);
                            continue;
                        }

                        if (storeEmployee.Mode == 'S')
                        {
                            Log.Logger.Warning("Skipping punch for employee {empNumber} because it is a salaried employee", entry.LocalEmployeeGUID);
                            continue;
                        }
                        
         
                        if (TryTimeEntryToPunchPair(entry, employeeMap, jobMap, jobs, breaksByEmployeeGuid, tipsByEmployeeGuid, out var pp))
                        {
                            pp.TimeZone = location.TimeZone;                            
                            punches.Add(pp);
                        }
                    }

                    //add any tips that are not associated with a time entry, this should catch all salary employee tips
                    foreach (var employee in tipsByEmployeeGuid)
                    {
                        if (!employeeMap.TryGetValue(employee.Key, out Models.StoreEmployee storeEmployee))
                            {
                                Log.Logger.Warning("Failed to find employee {eid} at restaurant {rid}, skipping punch",
                                    employee.Key, location.Id);
                            }
                        Log.Logger.Debug("Employee {employee} has {tips} tips remaining", employee.Key, employee.Value.Count);
                        foreach (var tip in employee.Value)
                        {
                            Guid ppId = Guid.NewGuid();
                            PunchPair tipPunch = new PunchPair()
                            {
                                Id = ppId.ToString(), 
                                ClockSeq = storeEmployee.UniquePersonalID,
                                Date = tip.MemoDate,
                                // Overtime = 0,
                                Description = $"Tip on non-working day for {storeEmployee.FirstName} {storeEmployee.LastName}",
                                // JobCode = jobDescription,
                                // CashTip =  0,
                                NonCashTip = tip.Amount,
                                Location = tip.UnitNumber,  //this is the location number, not the name or id!
                                // Sales = entry.NonCashSales + entry.CashSales,
                                FirstName = storeEmployee.FirstName,
                                LastName = storeEmployee.LastName,
                                TimeIn = DateTime.MinValue,
                                TimeZone = location.TimeZone
                            };
                            
                            punches.Add(tipPunch);
                            Log.Logger.Information("Created tip punch for employee {employee} with amount {amount} on {date}", 
                                employee.Key, tip.Amount, tip.MemoDate);
                        }
                    }

                }
            }
            catch (Exception e)
            {
                Log.Logger.Error(e.Message);
                Log.Logger.Error(e.GetType().Name);
                Log.Logger.Error(e.StackTrace);
            }

            Log.Logger.Debug("Punches: {punches}", JsonSerializer.Serialize(punches, new JsonSerializerOptions { WriteIndented = true }));

            return punches;
        }


        /*
        * Sets the primary job for an employee. If the employee already has the job, checks if the job is hidden, and unhides it if it is.
        */
        public async Task<bool> SetEmployeePrimaryJob(StoreEmployee employee, string jobId)
        {
            /*
             *
              {
                 "RecordID": "789ec0ce-0534-4be0-a31f-8af6c60b1ed1",
                 "PayrollJobID": "789ec0ce-0534-4be0-a31f-8af6c60b1ed1",
                 "SkillLevel": 0,
                 "Hide": "N"
               }
             */
            
            //first add the job to the employee
            using (var client = GetAuthenticatedHttpClient())
            {
                var endpoint = EndpointForPath($"v2/MUEmployee/{employee.LocalEmployeeId}/jobs");
                Log.Logger.Debug("Posting employee job via {url}", endpoint);
                
                EmployeeJob employeeJob = new EmployeeJob()
                {
                    RecordId = jobId, //this might not actually be needed
                    PayrollJobId = jobId,
                    SkillLevel = 1,
                    Hide = "N"
                };
                
                var body = JsonSerializer.Serialize(employeeJob, Json.DefaultSerializerOutputStyle);
                
                Log.Logger.Debug("body: {body}", body);

                var content = new StringContent(body, Encoding.UTF8, "application/json");

                var response = await client.PostAsync(endpoint, content);
                LogApiRequest();
                string responseBody = await response.Content.ReadAsStringAsync();
                Log.Logger.Debug("response: {response}", JsonSerializer.Serialize(response, new JsonSerializerOptions { WriteIndented = true }));
                Log.Logger.Debug("Response Body: {responseBody}", responseBody);
                // Get all employee jobs
                var allJobs = await GetAllEmployeeJobsAsync(employee);
                
                // Hide all jobs except for the one with jobId
                foreach (var job in allJobs)
                {
                    if (job.PayrollJobId != jobId)
                    {
                        var jobPatch = new JobPatch
                        {
                            RecordId = job.RecordId,
                            Hide = 'Y'
                        };

                        var patchResult = await PatchJobAsync(jobPatch);
                        
                        if (!patchResult)
                        {
                            Log.Logger.Warning("Failed to hide job {JobId} for employee {EmployeeId}", job.PayrollJobId, employee.GlobalEmployeeId);
                        }
                        else
                        {
                            Log.Logger.Information("Successfully hid job {JobId} for employee {EmployeeId}", job.PayrollJobId, employee.GlobalEmployeeId);
                        }
                    }
                }

                // Ensure the specified job is not hidden
                var targetJob = allJobs.FirstOrDefault(j => j.PayrollJobId == jobId);
                if (targetJob != null && targetJob.Hide == "Y")
                {
                    var jobPatch = new JobPatch
                    {
                        RecordId = targetJob.RecordId,
                        Hide = 'N'
                    };

                    var patchResult = await PatchJobAsync(jobPatch);
                    
                    if (!patchResult)
                    {
                        Log.Logger.Error("Failed to unhide job {JobId} for employee {EmployeeId}", jobId, employee.GlobalEmployeeId);
                    }
                    else
                    {
                        Log.Logger.Information("Successfully unhid job {JobId} for employee {EmployeeId}", jobId, employee.GlobalEmployeeId);
                    }
                }

                if (!response.IsSuccessStatusCode)
                {

                    if (!responseBody.Contains("The job already exists"))
                    {
                        Log.Logger.Error("Failed to add employee's job: {msg}", response.ReasonPhrase);
                        Log.Logger.Error(responseBody);

                        return false;
                    }
                    else
                    {
                        Log.Logger.Warning("Employee already has job {jobId}, checking if the job is hidden", jobId);
                        // Get the EmployeeJob using the employee's GlobalEmployeeId and JobId
                        var existingEmployeeJob = await GetEmployeeJobAsync(employee, jobId);
                        
                        if (existingEmployeeJob != null)
                        {
                            if (existingEmployeeJob.Hide == "Y")
                            {
                                Log.Logger.Information("Job {jobId} is hidden for employee {employeeId}. Unhiding it.", jobId, employee.GlobalEmployeeId);
                                
                                // Create a JobPatch object to unhide the job
                                var jobPatch = new JobPatch
                                {
                                    RecordId = existingEmployeeJob.RecordId,
                                    Hide = 'N'
                                };

                                // Patch the job to unhide it
                                var patchResult = await PatchJobAsync(jobPatch);
                                
                                if (!patchResult)
                                {
                                    Log.Logger.Error("Failed to unhide job {jobId} for employee {employeeId}", jobId, employee.GlobalEmployeeId);
                                    return false;
                                }
                                
                                Log.Logger.Information("Successfully unhid job {jobId} for employee {employeeId}", jobId, employee.GlobalEmployeeId);
                            }
                            else
                            {
                                Log.Logger.Information("Job {jobId} is already visible for employee {employeeId}", jobId, employee.GlobalEmployeeId);
                            }
                        }
                        else
                        {
                            Log.Logger.Warning("Could not find job {jobId} for employee {employeeId}", jobId, employee.GlobalEmployeeId);
                        }
                    }
                }
                

                //then update the employee's primary job
                endpoint = EndpointForPath("v2/MUEmployee/storeemployee");
                employee.PrimaryJobId = jobId;
                Log.Logger.Debug("Updating employee primary job via {url}", endpoint);

                body = JsonSerializer.Serialize(employee, Json.DefaultSerializerOutputStyle);
                content = new StringContent(body, Encoding.UTF8, "application/json");
                
                response = await client.PatchAsync(endpoint, content);

                LogApiRequest();
                if(response.IsSuccessStatusCode)
                {
                    return true;
                }
                else
                {
                    Log.Logger.Error("Failed to update employee's primary job: {ReasonPhrase}", response.ReasonPhrase);
                    Log.Logger.Debug("Request content: {content}", content.ReadAsStringAsync().Result);
                    Log.Logger.Debug("Response: {response}", JsonSerializer.Serialize(response, new JsonSerializerOptions { WriteIndented = true }));
                    responseBody = await response.Content.ReadAsStringAsync();
                    Log.Logger.Error("Response body: {responseBody}", responseBody);
                    return false;
                }
            }
        }
        
        private bool TryTimeEntryToPunchPair(
            TimeCardRow entry,
            Dictionary<string, Models.StoreEmployee> employeeMap,
            Dictionary<string, string> jobMap,
            Dictionary<string, string> jobs,
            Dictionary<string, List<TimeCardBreak>> breaks,
            Dictionary<string, List<PayMemoRow>> tips,
            out PunchPair pp)
        {
            //Log.Logger.Debug("Entry: {entry}", JsonSerializer.Serialize(entry, new JsonSerializerOptions { WriteIndented = true }));
            pp = null;

            if (entry.IgnoreForPayroll == "Y")
            {
                 return false;
            }
            // if (entry.Deleted)
            // {
            //     return false;
            // }
            
            if(entry.Type != ClockType.TimeClock)
            {
                Log.Logger.Debug("Skipping {breakType} for {fname} {lname} on {date}",
                    entry.Type, entry.EmployeeName, entry.LocalEmployeeGUID, entry.ClockInDateTime);
                return false;
            }

            if (!employeeMap.TryGetValue(entry.LocalEmployeeGUID.ToString(), out Models.StoreEmployee employee))
            {
                Log.Logger.Warning("Failed to find employee {eid} at restaurant {rid}, skipping punch",
                    entry.LocalEmployeeGUID, entry.UnitID);
                return false;
            }

            if (employee.PrimaryJobDescription == null)
            {
                Log.Logger.Debug(
                    "Time entry missing job description at restaurant {rid}, skipping punch for {fname} {lname} on {date}",
                     entry.UnitID, employee.FirstName, employee.LastName, entry.ClockInDateTime);
                return false;
            }

            if (!jobs.TryGetValue(entry.JobGUID.ToString(), out string job))
            {
                Log.Logger.Warning(
                    "Failed to find job {guid}, skipping punch for {fname} {lname} on {date}",
                    entry.JobGUID.ToString(), entry.UnitID, employee.FirstName, employee.LastName,
                    entry.ClockInDateTime);
                return false;
            }

            if (!jobMap.TryGetValue(job, out string jobDescription))
            {
                Log.Logger.Warning(
                    "Failed to job mapping for {guid}, skipping punch for {fname} {lname} @ {loc} on {date}",
                    job, employee.FirstName, employee.LastName, entry.UnitID, entry.ClockInDateTime);
                return false;
            }

            // if (string.IsNullOrEmpty(employee.ExternalEmployeeId))
            // {
            //     Log.Logger.Debug(
            //         "Null ExternalEmployeeId, skipping punch for {fname} {lname} ({email}) on {date}",
            //         employee.FirstName, employee.LastName, employee.Email, entry.InDate);
            //     return false;
            // }
            
            TimeSpan totalHours = entry.ClockOutDateTime - entry.ClockInDateTime;
            Guid ppId = Guid.NewGuid();

            var employeeBreaks = new List<TimeCardBreak>();
            decimal totalTips = 0;
            //check if there are tips for this employee
            if (!tips.ContainsKey(entry.LocalEmployeeGUID.ToString()))
            {
                Log.Logger.Debug("No tips found for {fname} {lname} on {date}", employee.FirstName, employee.LastName, entry.BusinessDate);
            }
            else
            {
                //find the total tip ammount for this business date
                totalTips = tips[entry.LocalEmployeeGUID.ToString()].Where(x => x.MemoDate == entry.BusinessDate).Sum(x => x.Amount);
                //log tips[entry.LocalEmployeeGUID.ToString()]
                // Log.Logger.Debug("Tips found {tips} for {fname} {lname} on {date}", JsonSerializer.Serialize(tips[entry.LocalEmployeeGUID.ToString()], new JsonSerializerOptions { WriteIndented = true }), employee.FirstName, employee.LastName, entry.BusinessDate); 
                
                //remove that tip so it's not counted again if there are two shifts in the same day
                tips[entry.LocalEmployeeGUID.ToString()].RemoveAll(x => x.MemoDate == entry.BusinessDate);
                //include the tip total in the debug statement
                
                Log.Logger.Debug("Tips found {totalTips} for {fname} {lname} on {date}", totalTips, employee.FirstName, employee.LastName, entry.BusinessDate);

            }
           
            pp = new PunchPair()
            {
                Id = ppId.ToString(), 
                ClockSeq = employee.UniquePersonalID,
                Date = entry.BusinessDate,
                // Overtime = 0,
                Description = $"{employee.FirstName} {employee.LastName}",
                // JobCode = jobDescription,
                // CashTip =  0,
                NonCashTip = totalTips,
                Location = entry.UnitNumber,  //this is the location number, not the name or id!
                // Sales = entry.NonCashSales + entry.CashSales,
                FirstName = employee.FirstName,
                LastName = employee.LastName,
                //TimeIn = entry.BusinessDate
            };
            
            //check for breaks, if found, add them to the punch pair break
            if (breaks.ContainsKey(entry.LocalEmployeeGUID.ToString()))
            {
                //find the breaks for this BusinessDate
                employeeBreaks = breaks[entry.LocalEmployeeGUID.ToString()].Where(x => x.BusinessDate == entry.BusinessDate).ToList();
                //remove the break if it doesn't fall within the punch pair timespan, in case of multiple shifts in the same day
                employeeBreaks.RemoveAll(x => x.TimeIn < entry.ClockInDateTime || x.TimeOut > entry.ClockOutDateTime);
                
                //Debug log the employeebreaks
                Log.Logger.Debug("EmployeeBreaks: {employeeBreaks}", JsonSerializer.Serialize(employeeBreaks, new JsonSerializerOptions { WriteIndented = true }));
            }
            // foreach employee break, add a break to the punch pair
            foreach (var breakEntry in employeeBreaks)
            {
                pp.Breaks.Add(new Payroll.Shared.Break()
                {
                    TimeIn = breakEntry.TimeIn,
                    TimeOut = breakEntry.TimeOut,
                    Paid = breakEntry.Paid
                });
                
                //if it Paid == false calculate the total hours worked
                if (!breakEntry.Paid)
                {
                    totalHours -= breakEntry.TimeOut - breakEntry.TimeIn;
                }
            }
            
            pp.Hours = (decimal)totalHours.TotalHours;

            pp.TimeIn = entry.ClockInDateTime;
            pp.TimeOut = entry.ClockOutDateTime;
            pp.Created = entry.BusinessDate; //todo - this probably isn't right. maybe use ClockInDateTime?
            pp.Modified = entry.LastMDate;

            return true;
        }
        
          private bool TryTimeEntryToBreak(TimeCardRow entry, out TimeCardBreak pb)
        {
            pb = null;
            
            if(entry.Type == ClockType.TimeClock)
            {
                return false;
            }

            if(entry.IgnoreForPayroll == "Y")
            {
                return false;
            }

            pb = new TimeCardBreak()
            {
                BusinessDate = entry.BusinessDate,
                EmployeeId = entry.LocalEmployeeGUID.ToString(),
                TimeIn = entry.ClockInDateTime,
                TimeOut = entry.ClockOutDateTime,
                Paid = entry.Type == ClockType.PaidBreak,
            };
            return true;
        }

        public async Task<Dictionary<string, Models.StoreEmployee>> EmployeesForRestaurant(string restaurantId)
        {
            using (var client = GetAuthenticatedHttpClient())
            {
                var endpoint = EndpointForPath($"v2/MUEmployee/storeemployees?UnitID={restaurantId}");
                Log.Logger.Debug("Fetching employees via {url}", endpoint);
                var response = await client.GetStringAsync(endpoint);
                var apiResponse = JsonSerializer.Deserialize<JsonElement>(response);
                //Log.Logger.Debug(JsonSerializer.Serialize(apiResponse, Json.DefaultSerializerOutputStyle));
                LogApiRequest();

                 var storeEmployees = JsonSerializer.Deserialize<List<Models.StoreEmployee>>(response);
                return storeEmployees.ToDictionary(x => x.LocalEmployeeId);
            }
           
        }
        /*
        * Fetches Jobs from Datacentral and returns them as a dictionary of JobId -> Job
        */
        public async Task<Dictionary<string, Models.Job>> JobsById()
        {
            using (var client = GetAuthenticatedHttpClient())
            {
                var endpoint = EndpointForPath("HRManagement/Jobs");
                Log.Logger.Debug("Fetching jobs via {url}", endpoint);
                var response = await client.GetStringAsync(endpoint);
                var apiResponse = JsonSerializer.Deserialize<JsonElement>(response);
                //Log.Logger.Debug(JsonSerializer.Serialize(apiResponse, Json.DefaultSerializerOutputStyle));
                LogApiRequest();

                var jobs = JsonSerializer.Deserialize<DataCentral.Tool.Models.JobReport>(response);


                return jobs.Data.ToDictionary(x => x.Id.ToString());
            }
        }
        void IDisposable.Dispose()
        {
            var endTime = DateTime.Now;
            var totalTime = endTime - StartTime;
            var requestsPerSec = TotalApiRequestsMade / totalTime.TotalSeconds;

            Log.Logger.Debug("Total Api Requests Made = {total}", TotalApiRequestsMade);
            Log.Logger.Debug("Elapsed Time in Seconds = {time}", totalTime.TotalSeconds);

            if (requestsPerSec > 15)
                Log.Logger.Warning("Requests Per Second     = {stat}", requestsPerSec);
            else
                Log.Logger.Debug("Requests Per Second     = {stat}", requestsPerSec);
        }
    }
}