using System.Text.Json;
using Payroll.Shared;
using Serilog;
using System;
using System.Linq;
using System.Collections.Generic;
using System.Globalization;

namespace DataCentral.Tool
{
    class StoreCommand
    {
        public void Active(List<string> args)
        {
            PrintSelected(args, x => x.Active);
        }

        public void Hidden(List<string> args)
        {
            PrintSelected(args, x => !x.Active);
        }

        public void List(List<string> args)
        {
            PrintSelected(args, x => true);
        }

        public void PrintSelected(List<string> args, Func<Location, bool> match)
        {
            try
            {
                using (var service = new DataCentralService())
                {
                    var locations = service.GetLocationsAsync().Result;
                    Console.WriteLine("Id  Code TimeZone       \tName                     \tAddress");
                    foreach (var location in locations)
                    {
                        if (!match(location)) continue;
                        Console.WriteLine($"{location.Id.PadLeft(3)} {location.Code.PadLeft(4)} {location.TimeZone?.PadRight(15)}\t{location.Name?.PadRight(25)}\t{location.StreetAddress}, {location.CityAddress}, {location.Zip}\t{location.Latitude}, {location.Longitude}");
                    }
                }
            }
            catch (Exception e)
            {
                Log.Logger.Fatal(e.Message);
                Log.Logger.Fatal(e.StackTrace);
            }
        }

        public void View(List<string> args)
        {
            if (args == null || args.Count < 1)
            {
                Console.WriteLine("Usage: DataCentral.Tool.exe restaurant view <id>");
                Console.WriteLine("For example, DataCentral.Tool.exe restaurant view 12");
                return;
            }

            try
            {
            }
            catch (Exception e)
            {
                Log.Logger.Fatal(e.Message);
                Log.Logger.Fatal(e.StackTrace);
            }
        }

        public void Jobs(List<string> args)
        {
            try
            {
                using (var service = new DataCentralService())
                {
                    var jobs = service.GetJobsAsync().Result;
                    foreach (DataCentral.Tool.Models.Job job in jobs.Data)
                    {
                        Console.WriteLine($"{job.Id}\t{job.Description}");
                    }
                }
            }
            catch (Exception e)
            {
                Log.Logger.Fatal(e.Message);
                Log.Logger.Fatal(e.StackTrace);
            }
        }

        private void PrintTimeEntries(List<PunchPair> timeEntries)
        {
            foreach (var entry in timeEntries)
            {
                Console.WriteLine($"{entry.Id}");
            }
        }
    }
}