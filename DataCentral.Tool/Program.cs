using System;
using System.Collections.Generic;
using System.Globalization;
using System.IO;
using System.Reflection;
using Serilog;
using CommandLine;
using Payroll.Shared;
using LiteDB;

namespace DataCentral.Tool
{
    [AttributeUsage(AttributeTargets.Assembly)]
    internal class BuildDateAttribute : Attribute
    {
        public BuildDateAttribute(string value)
        {
            DateTime = DateTime.ParseExact(value, "yyyyMMddHHmmss", CultureInfo.CurrentCulture, DateTimeStyles.None);
        }

        public DateTime DateTime { get; }
    }

    class Program : ProgramBase<SettingCommand>
    {
        private const string AppVersion = "0.2";

        private static DateTime GetBuildDate()
        {
            var assembly = Assembly.GetExecutingAssembly();
            var attribute = assembly.GetCustomAttribute<BuildDateAttribute>();
            return attribute?.DateTime ?? default(DateTime);
        }

        static void DoShowUsage()
        {
            Console.WriteLine("Usage: DataCentral.Tool.exe <command> <command-args>");
            Console.WriteLine("  where <command> is one of...");
            Console.WriteLine("   - employee [active|terms|list] <unit-id|all>");
            Console.WriteLine("   - export [all-time|time]");
            Console.WriteLine("   - import [hires|terms|sync] <unit-id|all>");
            Console.WriteLine("   - info - show version and tool information");
            Console.WriteLine("   - memo [list]");
            Console.WriteLine("   - punch [update|delete]");
            Console.WriteLine("   - setting = settings management");
            Console.WriteLine("   - store [active|hidden|list]");
            Console.WriteLine("   - sync [hires|terms|employees] <unit-id|all>");
        }

        public override int ShowUsage()
        {
            DoShowUsage();
            return 0;
        }

        public void Help(List<string> args)
        {
            ShowUsage();
        }

        public void Info(List<string> args)
        {
            try
            {
                Console.WriteLine($"DataCentral.Tool.exe - Version: {AppVersion}, Built: {GetBuildDate()}");
                Console.WriteLine($"  Branch: {BuildInfo.Branch}, Built: {BuildInfo.Built}, Host: {BuildInfo.Host}, Hash: {BuildInfo.Commit}");
                Console.WriteLine($"  DC Key: {Config.DC_Key}");
                Console.WriteLine($"  Adjust for Time Zone: {Config.AdjustForTimeZone}");
            }
            catch (Exception e)
            {
                Console.WriteLine(e.Message);
            }
        }

        private int ExecCommand<T>(List<string> args) where T : new()
        {
            var command = new CommandArguments(args);

            return Command<T>.Invoke(command);
        }

        public void Cache(List<string> args)
        {
            ExecCommand<CacheCommand>(args);
        }

        public void Employee(List<string> args)
        {
            ExecCommand<EmployeeCommand>(args);
        }

        public void Export(List<string> args)
        {
            ExecCommand<ExportCommand>(args);
        }

        public void Import(List<string> args)
        {
            ExecCommand<ImportCommand>(args);
        }

        public void Memo(List<string> args)
        {
            ExecCommand<MemoCommand>(args);
        }

        public void Store(List<string> args)
        {
            ExecCommand<StoreCommand>(args);
        }

        static int Main(string[] args)
        {
            if (args.Length == 0)
            {
                DoShowUsage();
                return -1;
            }

            // setup logging services...
            string command = String.Join(" ", args);
            Payroll.Shared.Logger.Setup($"DataCentral.Tool.Exe - Command: '{command}', Version: {AppVersion}");

            // Log version information
            Log.Logger.Information($"DataCentral.Tool.exe - Version: {AppVersion}, Built: {GetBuildDate()}");
            Log.Logger.Information($"  Branch: {BuildInfo.Branch}, Built: {BuildInfo.Built}, Host: {BuildInfo.Host}, Hash: {BuildInfo.Commit}");

            // setup database services...
            var mapper = BsonMapper.Global;
            mapper.Entity<Location>().Id(x => x.Id);

            using (var cache = CacheService.OpenCache(SystemConfig.CacheName()))
            {
                var locationCollection = cache.GetCollection<Location>(Config.POS_LOCATIONS_CACHE_COLLECTION);
                locationCollection.EnsureIndex(x => x.Id);
            }

            try
            {
                Parser.Default.ParseArguments<ProgramArguments>(args)
                    .MapResult((ProgramArguments opts) => ProgramDriver<Program>.Run(opts), errs => 1);
            }
            catch (Exception e)
            {
                Console.WriteLine(e.Message);
                Console.WriteLine(e.StackTrace);
                return -1;
            }

            return 0;
        }
    }
}
