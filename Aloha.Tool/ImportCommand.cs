using System.Globalization;
using CsvHelper;
using CsvHelper.Configuration;
using Payroll.Shared;
using Serilog;
using System.Text.RegularExpressions;
using System.Text.Json;

namespace Aloha.Tool
{
    public class ImportCommand
    {
        private class PunchRow
        {
            public string StoreNumber { get; set; }
            public string EmployeeNumber { get; set; }
            public string PayrollId { get; set; }
            public string JobCode { get; set; }
            public string EmployeeName { get; set; }
            public string WorkDate { get; set; }
            public string ClockIn { get; set; }
            public string ClockOut { get; set; }
            public decimal RegWorked { get; set; }
            public decimal OTWorked { get; set; }
        }

        private class PaycomRecordMap : ClassMap<PunchRow>
        {
            public PaycomRecordMap()
            {
                Map(m => m.StoreNumber).Name("Store #");
                Map(m => m.EmployeeNumber).Name("Emp. #");
                Map(m => m.PayrollId).Name("Payroll ID");
                Map(m => m.JobCode).Name("Job Code");
                Map(m => m.EmployeeName).Name("Emp. Name");
                Map(m => m.WorkDate).Name("Work Date");
                Map(m => m.ClockIn).Name("Clock In");
                Map(m => m.ClockOut).Name("Clock Out");
                Map(m => m.RegWorked).Name("Reg. Worked");
                Map(m => m.OTWorked).Name("OT. Worked");
            }
        }

        public static DateTimeOffset? ParseDateTime(DateOnly date, string timeString, TimeSpan tzOffset)
        {
            var numbers = Regex.Matches(timeString, @"\d+")
                               .Cast<Match>()
                               .Select(m => int.Parse(m.Value))
                               .ToList();

            if (numbers.Count() != 2)
            {
                Log.Error("Unknown time string: {timeString}", timeString);
                return null;
            }

            int hour = numbers[0];
            int minute = numbers[1];

            try
            {
                var dateTime = new DateTime(date.Year, date.Month, date.Day, hour, minute, 0);
                DateTimeOffset dateTimeOffset = new DateTimeOffset(dateTime, tzOffset);
                return dateTimeOffset;
            }
            catch (Exception ex)
            {
                Log.Error(ex, "Error parsing date time string: {timeString}", timeString);
                Log.Error("Year: {year}, Month: {month}, Day: {day}, Hour: {hour}, Minute: {minute}", date.Year, date.Month, date.Day, hour, minute);
                return null;
            }
        }

        public void Locations(List<string> args)
        {
            try
            {
                // Read JSON from standard input
                string jsonInput = Console.In.ReadToEnd();

                // Deserialize the JSON into a list of locations
                var locations = JsonSerializer.Deserialize<List<Payroll.Shared.Location>>(jsonInput);

                if (locations == null || locations.Count() == 0)
                {
                    Log.Logger.Error("No locations found in input JSON");
                    return;
                }

                // get cached location data if available
                using var locationService = new LocationService();
                foreach (var location in locations)
                {
                    Log.Logger.Information("Decorating location {id} with timezone info...", location.Id);
                    var success = locationService.DecorateLocationWithTimeZoneInfo(AppConfig.POS_LOCATIONS_CACHE_COLLECTION, location).Result;
                    if (!success)
                    {
                        Log.Logger.Warning("Failed to decorate location {id} with timezone info", location.Id);
                    }
                }

                // Store the locations in the cache
                CacheService.CacheRecords<Payroll.Shared.Location>(AppConfig.POS_LOCATIONS_CACHE_COLLECTION, locations);
                Log.Logger.Information("Successfully imported {count} locations to cache", locations.Count());
            }
            catch (JsonException e)
            {
                Log.Logger.Error(e, "Error parsing location JSON input");
            }
            catch (Exception e)
            {
                Log.Logger.Error(e, "Error importing locations");
            }
        }

        public void Punches(List<string> args)
        {
            try
            {
                var config = new AppConfig();
                var locations = CacheService.FetchRecords<Payroll.Shared.Location>(AppConfig.POS_LOCATIONS_CACHE_COLLECTION);
                if (locations == null || locations.Count() == 0)
                {
                    Log.Logger.Error(messageTemplate: "No locations have been loaded into the cache!");
                    return;
                }

                var punches = new List<PunchPair>();
                using (var reader = new StreamReader(Console.OpenStandardInput()))
                using (var csv = new CsvReader(reader, new CsvConfiguration(CultureInfo.InvariantCulture)))
                {
                    csv.Context.RegisterClassMap<PaycomRecordMap>();
                    var records = csv.GetRecords<PunchRow>().ToList();

                    // first pass to find the min and max dates
                    var minDate = DateOnly.MaxValue;
                    var maxDate = DateOnly.MinValue;
                    foreach (var record in records)
                    {
                        var date = DateOnly.ParseExact(record.WorkDate, "MM-dd-yyyy", CultureInfo.InvariantCulture);
                        if (date < minDate) minDate = date;
                        if (date > maxDate) maxDate = date;
                    }

                    // now get the timezone offsets for each day
                    using var locationService = new LocationService();
                    var timeZoneOffsetsByLocation = new Dictionary<string, Dictionary<DateOnly, TimeSpan>>();
                    foreach (var location in locations)
                    {
                        var timeZoneOffsetByDay = locationService.GetTimeZoneOffsets(location, minDate, maxDate);
                        timeZoneOffsetsByLocation[location.Code] = timeZoneOffsetByDay;
                    }

                    // now process each record
                    foreach (var record in records)
                    {
                        // Parse employee name into first/last
                        var nameParts = record.EmployeeName.Split(',');
                        string lastName = nameParts[0].Trim();
                        string firstName = nameParts.Length > 1 ? nameParts[1].Trim() : "";

                        var location = CacheService.FetchRecord<Payroll.Shared.Location>(AppConfig.POS_LOCATIONS_CACHE_COLLECTION, record.StoreNumber);
                        if (location == null)
                        {
                            Log.Warning("Skipping record for employee {employee} due to unknown location {location}", record.EmployeeName, record.StoreNumber);
                            continue;
                        }

                        // Parse date and times
                        var date = DateOnly.ParseExact(record.WorkDate, "MM-dd-yyyy", CultureInfo.InvariantCulture);
                        if (!timeZoneOffsetsByLocation.ContainsKey(location.Code) || !timeZoneOffsetsByLocation[location.Code].ContainsKey(date))
                        {
                            Log.Warning("Skipping record for employee {employee} due to unknown timezone offset for {location} on {date}", record.EmployeeName, location.Code, record.WorkDate);
                            continue;
                        }

                        var tzOffset = timeZoneOffsetsByLocation[location.Code][date];

                        // Parse clock in/out times and combine with date
                        var timeIn = ParseDateTime(date, record.ClockIn, tzOffset);
                        var timeOut = ParseDateTime(date, record.ClockOut, tzOffset);

                        if (timeIn == null || timeOut == null)
                        {
                            Log.Warning("Skipping record for employee {employee} due to invalid time format", record.EmployeeName);
                            continue;
                        }

                        // If clock out is before clock in, add a day to clock out
                        if (timeOut < timeIn)
                        {
                            timeOut = timeOut.Value.AddDays(1);
                        }

                        var punch = new PunchPair
                        {
                            EECode = record.EmployeeNumber,
                            ClockSeq = record.EmployeeNumber,
                            FirstName = firstName,
                            LastName = lastName,
                            Date = timeIn.Value.Date,
                            TimeIn = timeIn.Value,
                            TimeOut = timeOut.Value,
                            Location = record.StoreNumber,
                            JobCode = record.JobCode,
                            Hours = record.RegWorked,
                            Overtime = record.OTWorked
                        };

                        punches.Add(punch);
                    }
                }

                ConsoleService.PrintFormattedJson(punches);
            }
            catch (Exception ex)
            {
                Log.Error(ex, "Error processing punches data");
                throw;
            }
        }

        public void Employees(List<string> args)
        {
            try
            {
                var employees = new List<Employee>();

                using (var reader = new StreamReader(Console.OpenStandardInput()))
                {
                    reader.ReadLine();
                    using (var csv = new CsvReader(reader, new CsvConfiguration(CultureInfo.InvariantCulture)))
                    {
                        csv.Read();
                        csv.ReadHeader();

                        while (csv.Read())
                        {
                            try
                            {

                                using (var cache = CacheService.OpenCache(SystemConfig.CacheName()))
                                {

                                    var col = cache.GetCollection<Payroll.Shared.Location>(AppConfig.POS_LOCATIONS_CACHE_COLLECTION);
                                    var locations = col.Query().OrderBy(x => x.Code).ToList();

                                    var location = locations.FirstOrDefault(x => x.Code == csv.GetField("Store Number"));
                                    if (location == null)
                                    {
                                        Log.Logger.Warning("Location not found for employee {EmployeeNumber} at code {LocationCode}", csv.GetField("Employee Number"), csv.GetField("Store Number"));
                                        continue;
                                    }
                                }

                                var clockSeq = csv.GetField("Employee Number");
                                if (string.IsNullOrEmpty(clockSeq) || !int.TryParse(clockSeq, out _))
                                {
                                    Log.Logger.Warning("Invalid or missing clock sequence number for employee");
                                    continue;
                                }



                                var employee = new Employee
                                {
                                    Id = csv.GetField("Employee Number"),
                                    ClockSeq = csv.GetField("Employee Number"),
                                    FirstName = csv.GetField("First Name"),
                                    LastName = csv.GetField("Last Name"),
                                    StreetAddress = csv.GetField("Address 1"),
                                    CityAddress = csv.GetField("City"),
                                    State = csv.GetField("State Abbreviation"),
                                    Zip = csv.GetField("Zip Code"),
                                    DateOfBirth = DateTime.Parse(csv.GetField("Birthday")),
                                    HireDate = DateTime.Parse(csv.GetField("Hire Date")),
                                    PersonalEmail = csv.GetField("Email"),
                                    CellPhone = csv.GetField("Home Phone"),
                                    Active = true,
                                    PrimaryWorkLocation = csv.GetField("Store Number"),
                                    Salaried = csv.GetField("FT/PT Status").Equals("FT", StringComparison.OrdinalIgnoreCase)
                                };

                                // Handle optional middle initial
                                var middleInitial = csv.GetField("Middle Initial (Optional)");
                                if (!string.IsNullOrEmpty(middleInitial))
                                {
                                    employee.AddAttribute("middle_initial", middleInitial);
                                }

                                // Handle optional address 2
                                var address2 = csv.GetField("Address 2 (Optional)");
                                if (!string.IsNullOrEmpty(address2))
                                {
                                    employee.AddAttribute("address2", address2);
                                }

                                // Add additional attributes
                                employee.AddAttribute("social_security_number", csv.GetField("Social Number"));
                                employee.AddAttribute("state_marital_status", csv.GetField("State Marital Status"));
                                employee.AddAttribute("federal_marital_status", csv.GetField("Federal Marital Status"));
                                employee.AddAttribute("race_code", csv.GetField("Race Code"));
                                employee.AddAttribute("gender", csv.GetField("Gender"));
                                employee.AddAttribute("citizen", csv.GetField("Citizen"));

                                // Add job information
                                var job = new Job
                                {
                                    Code = csv.GetField("Job Code"),
                                    Rate = decimal.Parse(csv.GetField("Pay Rate")),
                                    IsPrimary = true,
                                    Location = csv.GetField("Store Number")
                                };

                                employee.Jobs.Add(job);

                                // Handle termination if present
                                var termCode = csv.GetField("Term Code");
                                var termDate = csv.GetField("Term Date");
                                if (!string.IsNullOrEmpty(termCode) && !string.IsNullOrEmpty(termDate))
                                {
                                    employee.Active = false;
                                    employee.TermDate = DateTime.Parse(termDate);
                                    employee.AddAttribute("term_code", termCode);
                                }

                                employees.Add(employee);
                            }
                            catch (Exception ex)
                            {
                                Log.Error(ex, "Error processing employee record");
                            }
                        }
                    }
                }

                ConsoleService.PrintFormattedJson(employees);
            }
            catch (Exception ex)
            {
                Log.Error(ex, "Error processing employees data");
                throw;
            }
        }

    }
}