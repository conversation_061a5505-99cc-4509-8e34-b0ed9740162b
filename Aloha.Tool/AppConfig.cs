﻿using Payroll.Shared;
using System;
using System.Collections.Generic;

namespace Aloha.Tool
{
    public class AppConfig
    {
        public static readonly string APP_SECTION = "aloha";

        public static readonly string POS_EMPLOYEES_CACHE_COLLECTION = "aloha_employees";
        public static readonly string POS_LOCATIONS_CACHE_COLLECTION = "aloha_locations";

        public static readonly string ENV_KEY_FTP_HOST = "ALOHA_FTP_HOST";
        public static readonly string ENV_KEY_FTP_USER = "ALOHA_FTP_USERNAME";
        public static readonly string ENV_KEY_FTP_PASSWORD = "ALOHA_FTP_PASSWORD";
        public static readonly string ENV_KEY_FTP_PATH = "ALOHA_FTP_PATH";

        public string? FTPHost { get; private set; }
        public string? FTPUser { get; private set; }
        public string? FTPPassword { get; private set; }
        public string? FTPPath { get; private set; }
        public string TimeZone { get; private set; }

        public AppConfig()
        {
            FTPHost = Environment.GetEnvironmentVariable(ENV_KEY_FTP_HOST);
            FTPUser = Environment.GetEnvironmentVariable(ENV_KEY_FTP_USER);
            FTPPassword = Environment.GetEnvironmentVariable(ENV_KEY_FTP_PASSWORD);
            FTPPath = Environment.GetEnvironmentVariable(ENV_KEY_FTP_PATH);

            {
                var setting = Setting.Get(APP_SECTION, "timezone");
                if (!string.IsNullOrEmpty(setting)) TimeZone = setting;
                else TimeZone = Environment.GetEnvironmentVariable("ALOHA_TIMEZONE") ?? "UTC";
            }
        }
    }
}
