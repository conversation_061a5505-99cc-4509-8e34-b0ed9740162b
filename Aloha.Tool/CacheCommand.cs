﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text.Json;
using Payroll.Shared;
using Serilog;
using LiteDB;

namespace Aloha.Tool
{
    public class CacheCommand
    {
        public void Clear(List<string> args)
        {
            if (args == null || args.Count < 1)
            {
                Console.WriteLine("Usage: Aloha.Tool.exe cache clear [locations]");
                return;
            }

            var scmd = args[0];
            switch (scmd)
            {
                // dont let users just pass any random collection name
                case "locations":
                    CacheService.RemoveRecords<Location>(AppConfig.POS_LOCATIONS_CACHE_COLLECTION);
                    break;
                default:
                    Console.WriteLine("Error: unrecognized cache dump object: {0}", scmd);
                    break;
            }
        }

        public void Dump(List<string> args)
        {
            string entity = "employees";
            if (args != null && args.Count > 0)
            {
                entity = args[0];
            }

            try
            {
                using (var cache = CacheService.OpenCache(SystemConfig.CacheName()))
                {
                    if (entity == "locations")
                    {
                        var col = cache.GetCollection<Payroll.Shared.Location>(AppConfig.POS_LOCATIONS_CACHE_COLLECTION);
                        var locations = col.Query().OrderBy(x => x.Code).ToList();
                        ConsoleService.PrintFormattedJson(locations);
                    }
                }
            }
            catch (Exception e)
            {
                Log.Logger.Fatal(e.Message);
                Log.Logger.Fatal(e.StackTrace);
            }
        }
    }
}
