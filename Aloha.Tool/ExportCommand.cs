﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text.RegularExpressions;
using Payroll.Shared;
using Serilog;

namespace Aloha.Tool
{
    public class ExportCommand
    {
        private static readonly string CSV_HEADER = "Store Number,Employee Number,Birthday,Hire Date,Last Name,First Name,Middle Initial,Address 1,Address 2,City,State Abbreviation,Zip Code,Social Number,Email,Home Phone,FT/PT Status,State Marital Status,Federal Marital Status,Race Code,Gender,Citizen,Job Code,Pay Rate,Term Code,Term Date,Payroll ID,Home Store Override";

        public static string FormatSocialSecurityNumber(string digits)
        {
            // Remove any non-digit characters from the input
            string cleanedDigits = new string(digits.Where(char.IsDigit).ToArray());

            // Check if we have exactly 9 digits
            if (cleanedDigits.Length != 9)
            {
                throw new ArgumentException("SSN input must contain exactly 9 digits.");
            }

            // Format the digits as SSN
            return $"{cleanedDigits.Substring(0, 3)}-{cleanedDigits.Substring(3, 2)}-{cleanedDigits.Substring(5, 4)}";
        }

        private static string FormatPhoneNumber(string phone)
        {
            if (string.IsNullOrWhiteSpace(phone))
                return "************";

            // Remove any non-digit characters
            var digits = new string(phone.Where(char.IsDigit).ToArray());

            // If we don't have exactly 10 digits, return default
            if (digits.Length != 10)
                return "************";

            // Format as XXX-XXX-XXXX
            return $"{digits.Substring(0, 3)}-{digits.Substring(3, 3)}-{digits.Substring(6, 4)}";
        }

        public void Settings(List<string> args)
        {
            {
                var atoken = Environment.GetEnvironmentVariable(AppConfig.ENV_KEY_FTP_PASSWORD);
                Console.WriteLine($"export {AppConfig.ENV_KEY_FTP_PASSWORD}='{atoken}'");
            }
        }

        public void Employees(List<string> args)
        {
            if (!ConsoleService.TryGetEmployeesFromInput(out var employeesToImport))
            {
                Log.Logger.Error("Failed to parse employee list");
                return;
            }

            if (employeesToImport == null)
            {
                Log.Logger.Error("Failed to load employee list");
                return;
            }

            using (var service = new AlohaService())
            {
                foreach (var employee in employeesToImport)
                {
                    try
                    {
                        // Skip locations with non-digits - they will break aloha
                        if (!Regex.IsMatch(employee.PrimaryWorkLocation ?? "", @"^\d+$"))
                        {
                            continue;
                        }

                        var primaryJob = employee.Jobs.FirstOrDefault(x => x.IsPrimary);
                        var jobRate = primaryJob?.Rate;
                        var jobCode = primaryJob?.Code;

                        string hourlyStatus = "";
                        string dolStatus = employee.Attributes.TryGetValue("dol_status", out var dolValue) ? dolValue : "";
                        string ssn = employee.Attributes.TryGetValue("ssn", out var ssnValue) ? ssnValue : "";
                        string ssnFormatted = FormatSocialSecurityNumber(ssn);

                        if (dolStatus == "Full Time")
                        {
                            hourlyStatus = "FT";
                        }
                        else
                        {
                            hourlyStatus = "PT";
                        }

                        var primaryPhone = FormatPhoneNumber(employee.OfficePhone);
                        if (string.IsNullOrEmpty(primaryPhone))
                        {
                            primaryPhone = "************";
                        }

                        var termDate = employee.TermDate?.ToString("MM/dd/yyyy") ?? "";
                        var termCode = string.IsNullOrEmpty(termDate) ? "" : "94";
                        var zipCode = string.IsNullOrEmpty(employee.Zip) ? "" : employee.Zip.Substring(0, 5);

                        // Format and output CSV row
                        var csvRow = string.Join(",",
                            employee.PrimaryWorkLocation,              // Store Id
                            employee.ClockSeq,                         // Employee Number
                            employee.DateOfBirth?.ToString("MM/dd/yyyy") ?? "", // Birthday
                            employee.HireDate?.ToString("MM/dd/yyyy") ?? "",  // Hire Date
                            EscapeCsvField(employee.LastName),       // Last Name
                            EscapeCsvField(employee.FirstName),      // First Name
                            "",                                            // Middle Initial
                            EscapeCsvField(employee.StreetAddress),       // Address 1
                            "",       // Address 2
                            EscapeCsvField(employee.CityAddress),           // City
                            employee.State,                          // State Abbreviation
                            zipCode,                        // Zip Code
                            ssnFormatted,           // Social Number
                            EscapeCsvField(employee.PersonalEmail),          // Email
                            primaryPhone,      // Home Phone
                            hourlyStatus,                                      // FT/PT Status
                            "0",                                      // State Marital Status
                            "0",                                      // Federal Marital Status
                            "98",                                      // Race Code
                            "M",                                      // Gender
                            "1",                                      // Citizen
                            jobCode,                                      // Job Code
                            jobRate,         // Pay Rate
                            termCode,                // Term Code
                            termDate, // Term Date
                            employee.ClockSeq,                      // Payroll ID
                            "1"              // Home Store Override - hardcoded to 1 for now
                        );

                        Console.WriteLine(csvRow);
                    }
                    catch (Exception e)
                    {
                        Log.Logger.Error("Error processing employee {employee} at location {location}...", employee.ClockSeq, employee.PrimaryWorkLocation);
                        Log.Logger.Fatal(e.Message);
                        if (!string.IsNullOrEmpty(e.StackTrace)) Log.Logger.Fatal(e.StackTrace);
                    }
                }
            }
        }

        private static string EscapeCsvField(string field)
        {
            if (string.IsNullOrEmpty(field)) return "";
            if (field.Contains(",") || field.Contains("\"") || field.Contains("\n"))
            {
                return $"\"{field.Replace("\"", "\"\"")}\"";
            }
            return field;
        }
    }
}