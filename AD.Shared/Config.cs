using Payroll.Shared;

namespace AD.Shared
{
    public static class Config
    {
        public static readonly string SettingSection = "ad";
        public static readonly string SkipGroupSection = "ad_skip_groups";
        public static readonly string SensitivePropertiesSection = "ad_sensitive_properties";
        public static readonly string NotifyOnChangeSection = "ad_notify_on_change";

        private static readonly string endpointUrl;
        public static readonly int HireLimit;
        public static readonly int SyncLimit;
        public static readonly int TermLimit;

        public static readonly HashSet<string> PropsToNotify = new HashSet<string>();
        public static readonly HashSet<string> SensitiveProps = new HashSet<string>();
        public static readonly HashSet<string> GroupsToSkip = new HashSet<string>();

        static Config()
        {
            // canonicalize endpoint url format
            endpointUrl = Setting.Get(SettingSection, "endpoint").Trim();

            {
                var list = Setting.ListSection(SkipGroupSection);
                foreach (var val in list.Values)
                {
                    GroupsToSkip.Add(val.Trim().ToLower());
                }
            }

            {
                var list = Setting.ListSection(NotifyOnChangeSection);
                foreach (var key in list.Keys)
                {
                    PropsToNotify.Add(key.Trim().ToLower());
                }
            }

            {
                var list = Setting.ListSection(SensitivePropertiesSection);
                foreach (var key in list.Keys)
                {
                    // these are case sensitive properties that should not be lowered
                    SensitiveProps.Add(key.Trim());
                }
            }

            {
                string limit = Setting.Get(SettingSection, "sync_limit");
                SyncLimit = string.IsNullOrEmpty(limit) ? 0 : int.Parse(limit);
            }

            {
                string limit = Setting.Get(SettingSection, "hire_limit");
                HireLimit = string.IsNullOrEmpty(limit) ? 0 : int.Parse(limit);
            }

            {
                string limit = Setting.Get(SettingSection, "term_limit");
                TermLimit = string.IsNullOrEmpty(limit) ? 0 : int.Parse(limit);
            }
        }

        public static bool NotifyOnChange(string propName)
        {
            if (string.IsNullOrEmpty(propName)) return false;
            var lower = propName.ToLower();

            return PropsToNotify.Contains(lower);
        }

        public static bool SkipGroup(string groupName)
        {
            if (string.IsNullOrEmpty(groupName)) return false;
            var lower = groupName.ToLower().Trim();

            return GroupsToSkip.Contains(lower);
        }

        public static string Endpoint()
        {
            return endpointUrl;
        }

        public static string Password()
        {
            return Setting.Get(SettingSection, "password");
        }

        public static string Username()
        {
            return Setting.Get(SettingSection, "username");
        }

        public static string PrimaryKey()
        {
            return Setting.Get(SettingSection, "pkey");
        }

        private static ExecutionMode ExecutionMode(string modeName)
        {
            return Setting.ExecutionMode(SettingSection, modeName);
        }

        public static ExecutionMode HireMode()
        {
            return Setting.ExecutionMode(SettingSection, "hire_mode");
        }

        public static ExecutionMode SyncMode()
        {
            return Setting.ExecutionMode(SettingSection, "sync_mode");
        }

        public static ExecutionMode TermMode()
        {
            return Setting.ExecutionMode(SettingSection, "term_mode");
        }
    }
}