﻿using Payroll.Shared;
using System.Linq;
using System.Collections.Generic;
using Newtonsoft.Json;
using Serilog;
using System;

namespace Square.Tool
{
    public static class Config
    {
        public readonly static string SettingSection = "square";
        public readonly static string LocationSection = "square_restaurants";
        public readonly static string ManagerSection = "square_manager_jobs";
        public readonly static string SquareJobMappingSection = "square_job_mapping";
        public readonly static string SquareLocationDesignation = "square_location_designation";

        static Config()
        {
            Setting.Init();
        }

        public static string AccessToken()
        {
            var token = Setting.Get(SettingSection, "token");
            if (!string.IsNullOrEmpty(token)) return token;
            return System.Environment.GetEnvironmentVariable("SQUARE_ACCESS_TOKEN") ?? string.Empty;
        }

        public static int ApiDelayInMilliSeconds()
        {
            var delay = Setting.Get(SettingSection, "delay");
            if (string.IsNullOrEmpty(delay)) return 250;
            return Convert.ToInt32(delay);
        }

        public static Square.Environment Environment()
        {
            var environment = Setting.Get(SettingSection, "environment");
            return environment == "sandbox" ? Square.Environment.Sandbox : Square.Environment.Production;
        }

        public static List<string> ManagerJobsById()
        {
            var managerJobs = new List<string>();
            var section = Setting.ListSection(ManagerSection);
            if (section == null) return managerJobs;
            return section.Keys.ToList();
        }

        public static Dictionary<string, RestaurantInfo> RestaurantsByCode()
        {
            return ConfigService.RestaurantsByCode(LocationSection);
        }

        public static Dictionary<string, RestaurantInfo> RestaurantsById()
        {
            return ConfigService.RestaurantsById(LocationSection);
        }

        public static Dictionary<string, string> SquareJobMappings()
        {
            var map = new Dictionary<string, string>();
            var section = Setting.ListSection(SquareJobMappingSection);

            foreach (var item in section)
            {
                map.Add(item.Key, item.Value);
            }

            return map;
        }

        public static string GetSquareLocationDesignation()
        {
            var section = Setting.ListSection(SquareLocationDesignation);
            Console.WriteLine("Job designation: " + section.Values.FirstOrDefault().ToString());
            return section.Values.FirstOrDefault().ToString();
        }
    }
}