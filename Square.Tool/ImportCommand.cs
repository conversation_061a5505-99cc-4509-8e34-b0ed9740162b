﻿using System;
using System.Collections.Generic;
using System.Linq;
using Payroll.Shared;
using Serilog;
using Square.Exceptions;
using Square.Models;
using PayrollSharedEmployee = Payroll.Shared.Employee;

namespace Square.Tool
{
    class ImportCommand
    {
        Dictionary<string, RestaurantInfo>? RestaurantDirectory { get; set; }
        IEnumerable<TeamMember>? ExistingEmployees { get; set; }                      
        string? MissingJobDesignation { get; set; }

        #region Exposed Methods

        public void Hires(List<string> args)
        {            
            Process();
        }        

        public void Changes(List<string> args)
        {
            Process();
        }
                
        #endregion

        #region Private Methods

        void Process()
        {
            if (!ConsoleService.TryGetEmployeesFromInput(out List<PayrollSharedEmployee> hires))
            {
                Log.Logger.Error("Failed to parse Square employee list");
                return;
            }
            
            LoadDirectories();
            ProcessEmployees(hires);
        }

        void LoadDirectories()
        {
            MissingJobDesignation = Config.GetSquareLocationDesignation();
            RestaurantDirectory = Config.RestaurantsByCode();

            var locationIds = new List<string>(RestaurantDirectory.Keys);
            ExistingEmployees = EmployeeCommand.GetSquareEmployees(locationIds);            
        }

        void ProcessEmployees(IEnumerable<PayrollSharedEmployee> employees)
        {
            var newTeamMembers = new Dictionary<string, CreateTeamMemberRequest>();
            var existingTeamMembers = new Dictionary<string, UpdateTeamMemberRequest>();

            foreach (var employee in employees)
            {
                if (!ValidateEmployee(employee))
                {
                    Log.Logger.Information("Square Employee not valid, skipping {fname} {lname}", employee.FirstName, employee.LastName);                    
                    continue;
                }
                
                var existingEmployee = ExistingEmployees.Where(x => x.ReferenceId == employee.ClockSeq).FirstOrDefault();                

                //  New Employee (TeamMember)
                if (existingEmployee == null)
                {                    
                    if (!ValidateNewEmployee(employee))
                    {
                        Log.Logger.Information("Skipping creation of an inactive Square employee {cseq}", employee.ClockSeq);
                        continue;
                    }

                    Log.Logger.Information("Creating new Square employee with external id {cseq}",employee.ClockSeq);

                    var teamMember = BuildTeamMember(employee);

                    if (teamMember != null)
                    {
                        var teamMemberRequest = new CreateTeamMemberRequest.Builder()
                        .TeamMember(teamMember)
                        .Build();

                        newTeamMembers.Add(employee.ClockSeq, teamMemberRequest);
                    }
                    
                }
                //  Existing Employee (TeamMember)
                else
                {
                    var needToUpdate = DoesEmployeeNeedUpdated(existingEmployee, employee);

                    if (needToUpdate)
                    {
                        var locationList = GetLocationList(employee);
                        var assignedLocations = GetTeamMemberAssignedLocations(locationList, employee);

                        var existing = new TeamMember.Builder()
                            .Id(existingEmployee.Id)
                            .ReferenceId(employee.ClockSeq)
                            .IsOwner(false)
                            .Status(employee.Active ? "ACTIVE" : "INACTIVE")
                            .EmailAddress(employee.WorkEmail)
                            .FamilyName(employee.LastName)
                            .PhoneNumber(employee.CellPhone)
                            .GivenName(employee.FirstName)
                            .AssignedLocations(assignedLocations)
                            .Build();

                        var body = new UpdateTeamMemberRequest.Builder()
                            .TeamMember(existing)                            
                            .Build();

                        existingTeamMembers.Add(existingEmployee.Id, body);
                    }
                }                    
            }

            BulkCreateEmployees(newTeamMembers);
            BulkEditEmployees(existingTeamMembers);
            UpdateWageSettings(employees);
        }

        bool DoesEmployeeNeedUpdated(TeamMember teamMember, PayrollSharedEmployee employee)
        {
            if (teamMember.EmailAddress != employee.WorkEmail)
            {
                return true;
            }

            if (teamMember.Status != (employee.Active ? "ACTIVE" : "INACTIVE"))
            {
                return true;
            }

            if (teamMember.FamilyName != employee.LastName)
            {
                return true;
            }

            if (teamMember.PhoneNumber != employee.CellPhone)
            {
                return true;
            }

            if (teamMember.GivenName != employee.FirstName)
            {
                return true;
            }

            return false;
        }

        void BulkCreateEmployees(Dictionary<string, CreateTeamMemberRequest> newTeamMembers)
        {
            if (newTeamMembers.Count == 0)
            {
                return;
            }

            var body = new BulkCreateTeamMembersRequest.Builder(teamMembers: newTeamMembers).Build();
            var client = SquareService.BuildClient();

            try
            {
                var result = client.TeamApi.BulkCreateTeamMembers(body: body);

                if (result != null && result.Errors != null)
                {
                    CheckAPIErrors(result.Errors);
                }
                else
                {
                    Log.Logger.Information("SQUARE Employee Bulk Insert successful");
                }
            }
            catch (ApiException e)
            {
                WriteOutAPIExceptions("SQUARE API Error****  Bulk create Square employees", e);
            }
        }
        
        void BulkEditEmployees(Dictionary<string, UpdateTeamMemberRequest> editTeamMembers)
        {
            if (editTeamMembers.Count == 0)
            {
                return;
            }

            var body = new BulkUpdateTeamMembersRequest.Builder(teamMembers: editTeamMembers).Build();
            var client = SquareService.BuildClient();

            try
            {
                var result = client.TeamApi.BulkUpdateTeamMembers(body: body);

                if (result != null && result.Errors != null)
                {
                    CheckAPIErrors(result.Errors);
                }
                else
                {
                    Log.Logger.Information("SQUARE Employee Bulk Edit successful");
                }
            }
            catch (ApiException e)
            {
                WriteOutAPIExceptions("API Error****  Bulk Edit Square Employees", e);
            }
        }

        void UpdateWageSettings(IEnumerable<PayrollSharedEmployee> employees)
        {
            //  reload employees as we may have created new records.
            //  when updating, SQUARE needs the TeamMemberID which is a unique
            //  ID in their system.
            LoadDirectories();

            var client = SquareService.BuildClient();

            foreach (var employee in employees)
            {
                var existingEmployee = ExistingEmployees.Where(x => x.ReferenceId == employee.ClockSeq).FirstOrDefault();

                foreach (var job in employee.Jobs)
                {
                    if (string.IsNullOrEmpty(job.Name))
                    {
                        Log.Logger.Information("Missing job name for Square employee {fname} {lname}", employee.FirstName, employee.LastName);
                        continue;
                    }

                    var hourlyRate = new Money.Builder()
                      .Amount(Convert.ToInt64(job.Rate * 100))
                      .Currency("USD")
                      .Build();

                    var jobAssignment = new JobAssignment.Builder(jobTitle: job.Name, payType: "HOURLY")
                      .HourlyRate(hourlyRate)
                      .Build();

                    var jobAssignments = new List<JobAssignment>();
                    jobAssignments.Add(jobAssignment);

                    var wageSetting = new WageSetting.Builder()
                      .TeamMemberId(existingEmployee.Id)
                      .JobAssignments(jobAssignments)
                      .Build();

                    var body = new UpdateWageSettingRequest.Builder(wageSetting: wageSetting).Build();
                    
                    try
                    {
                        var result = client.TeamApi.UpdateWageSetting(teamMemberId: existingEmployee.Id, body: body);
                    }
                    catch (ApiException e)
                    {
                        WriteOutAPIExceptions("SQUARE API Error****  Update Square Employee Wages", e);
                    }
                }
            }            
        }

        bool ValidateNewEmployee(PayrollSharedEmployee employee)
        {
            var valid = true;

            if (!employee.Active)
            {
                Log.Logger.Information("Skipping creation of an inactive Square employee {cseq}", employee.ClockSeq);
                valid = false;
            }

            var duplicateEmail = ExistingEmployees.Where(x => x.EmailAddress == employee.WorkEmail).FirstOrDefault();

            if (duplicateEmail != null)
            {
                Log.Logger.Warning(" Skipping creation of Square employee {cseq} with duplicate email address {email}",
                    employee.ClockSeq, employee.WorkEmail);
                valid = false;
            }

            return valid;
        }

        bool ValidateEmployee(PayrollSharedEmployee employee)
        {
            Log.Logger.Information("Validating Square employee {fname} {lname} at location {loc}",
                            employee.FirstName, employee.LastName, employee.PrimaryWorkLocation);

            var valid = true;

            try
            {
                if (string.IsNullOrEmpty(employee.ClockSeq))
                {
                    Log.Logger.Information("Null clockseq, skipping {name}", employee.LastName);
                    valid = false;
                }

                if (!RestaurantDirectory.TryGetValue(employee.PrimaryWorkLocation, out _))
                {
                    Log.Logger.Error("Failed to locate location {rid}", employee.PrimaryWorkLocation);
                    valid = false;
                }                
                
                return valid;
            }
            catch (Exception ex)
            {
                Log.Logger.Information($"Exception validating Square Employee {employee.Id} - {employee.FirstName} {employee.LastName}");
                return false;
            }            
        }

        List<string> GetLocationList(PayrollSharedEmployee employee)
        {
            var locationList = new List<string>();

            foreach (var job in employee.Jobs)
            {
                if (!string.IsNullOrEmpty(job.Location))
                {
                    if (!RestaurantDirectory.TryGetValue(job.Location, out RestaurantInfo? restaurant))
                    {
                        Log.Logger.Fatal("Failed to find location {loc}.", job.Location);
                        continue;
                    }

                    locationList.Add(restaurant.Code);
                }
                else
                {
                    Log.Logger.Information($"Square Employee {employee.Id} - {employee.FirstName} {employee.LastName} job {job.Name} -  has an empty location");
                }
            }

            return locationList;
        }

        TeamMemberAssignedLocations GetTeamMemberAssignedLocations(List<string> locationList, PayrollSharedEmployee employee)
        {
            var locationAssignmentType = "EXPLICIT_LOCATIONS";

            //  if we did not find any locations, check config file to see
            //  if customer wants to us to associate new employees to All
            //  locations in this scenario
            if (locationList.Count == 0)
            {
                if (MissingJobDesignation.ToUpper() == "All".ToUpper())
                {
                    locationAssignmentType = "ALL_CURRENT_AND_FUTURE_LOCATIONS";
                }
                else
                {
                    Log.Logger.Fatal("Failed to find any locations for Square employee {loc}.", employee.ClockSeq);
                    return null;
                }
            }

            return new TeamMemberAssignedLocations.Builder()
                .AssignmentType(locationAssignmentType)
                .LocationIds(locationList)
                .Build();
        }

        TeamMember? BuildTeamMember(PayrollSharedEmployee employee)
        {
            var locationList = GetLocationList(employee);                                  

            var assignedLocations = GetTeamMemberAssignedLocations(locationList, employee);

            return new TeamMember.Builder()
                .ReferenceId(employee.ClockSeq)
                .Status("ACTIVE")                
                .GivenName(employee.FirstName)
                .FamilyName(employee.LastName)
                .EmailAddress(employee.WorkEmail)
                .PhoneNumber(employee.CellPhone)
                .AssignedLocations(assignedLocations)
                .Build();                        
        }       

        void CheckAPIErrors(IList<Error> errors)
        {
            foreach (var error in errors)
            {
                Log.Logger.Error("Error code: " + error.Code);
                Log.Logger.Error("Error detail: " + error.Detail);
            }
        }

        void WriteOutAPIExceptions(string message, ApiException e)
        {
            Log.Logger.Error(message);
            Log.Logger.Error($"Response Code: {e.ResponseCode}");            
            Log.Logger.Error($"Exception: {e.Message}");
            Log.Logger.Error($"Stack Trace: {e.StackTrace}");
        }        

        #endregion
    }
}