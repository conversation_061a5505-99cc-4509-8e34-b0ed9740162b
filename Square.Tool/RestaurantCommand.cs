﻿using System;
using System.Collections.Generic;
using System.Linq;
using Payroll.Shared;
using Serilog;
using Square.Exceptions;

namespace Square.Tool
{
    class RestaurantCommand
    {
        // NOTE: I like the brink setup format of "MBBR = <weird-token-here>" more than how toast is
        // setup. so this code is using RestaurantsByCode instead of how Toast works. Fix toast soon.
        // also a "Location" in Square world is a "Restaurant" in Toast world and in PayrollTools
        public void List(List<string> args)
        {
            // brief listing or full
            bool brief = false;
            if (args != null && args.Count > 0) brief = true;

            try
            {
                var enabledRestaurants = Config.RestaurantsByCode();

                var service = new SquareService();
                var locations = service.GetLocations();
                
                if (!brief)
                {
                    Console.WriteLine("Enabled\tCode\tName\tId");
                }

                foreach (var location in locations)
                {
                    var enabled = enabledRestaurants.ContainsKey(location.Id);
                    var code = enabled ? enabledRestaurants[location.Id].Id : "    ";

                    if (brief)
                    {
                        // we only write out enabled entries in brief mode
                        if (enabled) Console.Write(location.Id + " ");
                    }
                    else
                    {
                        Console.WriteLine($"{enabled}  \t{code}\t{location.Name}\t{location.Id}");
                    }
                }

                if (brief)
                    Console.Write("\n");
            }
            catch (Exception e)
            {
                Log.Logger.Fatal($"Exception: {e.Message}");
                Log.Logger.Fatal(e.StackTrace);
            }
        }

        public void Employees(List<string> args)
        {
            if (args == null || args.Count < 1)
            {
                Console.WriteLine("Usage:   Square.Tool.exe restaurant employees <code>");
                Console.WriteLine("Example: Square.Tool.exe restaurant employees mm22");
                return;
            }

            try
            {
                var codeMap = Config.RestaurantsById();
                if (!codeMap.TryGetValue(args.First(), out RestaurantInfo? rInfo))
                {
                    Console.WriteLine($"Invalid restaurant code: {args.First()}");
                    return;
                }

                var locations = new List<string> { rInfo.Code };
                var employees = EmployeeCommand.GetSquareEmployees(locations);

                foreach (var employee in employees)
                {
                    if (!string.IsNullOrEmpty(employee.ReferenceId))
                        Console.WriteLine($"{employee.Id}\t{employee.EmailAddress}\t{employee.FamilyName}, {employee.GivenName}\t{employee.Status}");
                }
            }
            catch (Exception e)
            {
                Log.Logger.Fatal($"Exception: {e.Message}");
                Log.Logger.Fatal(e.StackTrace);
            }
        }
    }
}