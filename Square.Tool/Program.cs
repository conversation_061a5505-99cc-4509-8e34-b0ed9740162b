using System;
using System.Collections.Generic;
using Payroll.Shared;
using Serilog;
using CommandLine;
using Microsoft.VisualBasic;

namespace Square.Tool
{
    public class Program : ProgramBase<SettingCommand>
    {
        public static readonly string AppVersion = "1.1.0";

        public override int ShowUsage()
        {
            Console.WriteLine("Usage: Square.Tool.exe <command> <command-args>");
            Console.WriteLine("  where <command> is one of...");
            Console.WriteLine("   - cash [list] = cash management");
            Console.WriteLine("   - employee [list, exceptions, connections, set] = employee management");
            Console.WriteLine("   - info - show version and tool information");
            Console.WriteLine("   - job [list] = job management");
            Console.WriteLine("   - export [alltime, time] = export utilities");
            Console.WriteLine("   - import [changes, employees] = import utilities");
            Console.WriteLine("   - restaurant [list, view, employees] = restaurant management");
            Console.WriteLine("   - setting = settings management");
            return 0;
        }

        public void Help(List<string> args)
        {
            ShowUsage();
        }

        public void Info(List<string> args)
        {
            Payroll.Shared.Setting.Init();
            Console.WriteLine($"Toast.Tool.exe version {AppVersion} ({Payroll.Shared.Setting.IniFilePath})");
            Console.WriteLine();

            var list = Payroll.Shared.Setting.ListSection("toast");
            foreach (var e in list)
            {
                Console.WriteLine($"{e.Key}={e.Value}");
            }

            Console.WriteLine();
            Console.WriteLine("To set an api call delay of N seconds, use...");
            Console.WriteLine("  Toast.Tool.exe setting delay <N>");
        }

        private int ExecCommand<T>(List<string> args) where T : new()
        {
            var command = new CommandArguments(args);

            return Command<T>.Invoke(command);
        }

        public void Employee(List<string> args)
        {
            if (ExecCommand<EmployeeCommand>(args) != 0)
                ShowUsage();
        }

        public void Job(List<string> args)
        {
            if (ExecCommand<JobCommand>(args) != 0)
                ShowUsage();
        }

        public void Import(List<string> args)
        {
            if (ExecCommand<ImportCommand>(args) != 0)
                ShowUsage();
        }

        public void Export(List<string> args)
        {
            if (ExecCommand<ExportCommand>(args) != 0)
                ShowUsage();
        }

        public void Restaurant(List<string> args)
        {
            if (ExecCommand<RestaurantCommand>(args) != 0)
                ShowUsage();
        }

        static int Main(string[] args)
        {
            // setup logging services...
            string command = String.Join(" ", args);
            Logger.Setup($"Square.Tool.Exe - Command: '{command}', Version: {AppVersion}");

            // Log version information  
            Log.Logger.Information($"Square.Tool.exe version {AppVersion} ({Payroll.Shared.Setting.IniFilePath})");

            try
            {
                Parser.Default.ParseArguments<ProgramArguments>(args)
                    .MapResult((ProgramArguments opts) => ProgramDriver<Program>.Run(opts), errs => 1);
            }
            catch (Exception e)
            {
                Log.Logger.Error(e.Message);
                Log.Logger.Error(e.StackTrace);
                return -1;
            }

            return 0;
        }
    }
}