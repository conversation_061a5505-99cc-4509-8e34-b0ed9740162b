<Project Sdk="Microsoft.NET.Sdk">

    <PropertyGroup>
        <OutputType>Exe</OutputType>
        <TargetFrameworks>net8.0</TargetFrameworks>
        <Nullable>enable</Nullable>
    </PropertyGroup>

    <PropertyGroup Condition=" '$(Configuration)' == 'Release' ">
      <DebugType>none</DebugType>
    </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="CommandLineParser" Version="2.9.1" />
    <PackageReference Include="Newtonsoft.Json" Version="13.0.3" />
    <PackageReference Include="Serilog" Version="4.2.0" />
    <PackageReference Include="Serilog.Sinks.Console" Version="6.0.0" />
    <PackageReference Include="Serilog.Sinks.File" Version="7.0.0" />
    <PackageReference Include="Square" Version="41.1.0" />
    <PackageReference Include="AutoMapper" Version="14.0.0" />
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\Payroll.Shared\Payroll.Shared.csproj" />
  </ItemGroup>

</Project>
