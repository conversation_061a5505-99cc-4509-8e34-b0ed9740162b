using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading;
using Payroll.Shared;
using Serilog;
using Square.Exceptions;
using Square.Models;

namespace Square.Tool
{
    public class SquareService
    {
        private static readonly int ApiDelayInMilliSeconds;
        private uint TotalApiRequestsMade { get; set; }
        private DateTime StartTime { get; set; }

        static SquareService()
        {
            ApiDelayInMilliSeconds = Config.ApiDelayInMilliSeconds();
        }

        public SquareService()
        {
            TotalApiRequestsMade = 0;
            StartTime = DateTime.Now;
        }

        public static SquareClient BuildClient()
        {
            var accessToken = Config.AccessToken();
            var environment = Config.Environment();
            
            return new SquareClient.Builder()
                .Environment(environment)
                .AccessToken(accessToken)
                .Build();
        }

        public static IEnumerable<Square.Models.Location> ActiveLocations()
        {
            SquareClient client = BuildClient();
            var api = client.LocationsApi;
            return api.ListLocations().Locations.Where(x => x.Status == "ACTIVE");
        }

        private R InvokeWithDelay<R>(Func<R> func)
        {
            Log.Logger.Debug("InvokeWithDelay: Waiting {0} milliseconds", ApiDelayInMilliSeconds);
            Thread.Sleep(ApiDelayInMilliSeconds);
            TotalApiRequestsMade++;
            return func();
        }

        public IEnumerable<TeamMember> GetTeamMembers(SearchTeamMembersFilter filter)
        {
            var teamMembers = new List<TeamMember>();
            SquareClient client = BuildClient();

            var query = new SearchTeamMembersQuery.Builder()
                .Filter(filter)
                .Build();

            string? cursor = null;
            do
            {
                var body = new SearchTeamMembersRequest.Builder()
                    .Query(query);
                if (cursor != null) body.Cursor(cursor);

                var result = InvokeWithDelay(() => client.TeamApi.SearchTeamMembers(body.Build()));
                if (result.TeamMembers == null || result.TeamMembers.Count == 0) return teamMembers;

                teamMembers.AddRange(result.TeamMembers);
                cursor = result.Cursor;
            } while (!string.IsNullOrEmpty(cursor));

            return teamMembers;
        }

        public IEnumerable<TeamMember> GetActiveEmployees()
        {
            var filter = new SearchTeamMembersFilter.Builder()
                .Status("ACTIVE");
            return GetTeamMembers(filter.Build());
        }

        public IEnumerable<Shift> GetShifts(ShiftFilter filter)
        {
            var shifts = new List<Shift>();
            SquareClient client = BuildClient();
            string? cursor = null;

            var bodyQuery = new ShiftQuery.Builder()
                .Filter(filter)
                .Build();
            var body = new SearchShiftsRequest.Builder()
                .Query(bodyQuery).Limit(100)
                .Build();

            do
            {
                var page = InvokeWithDelay(() => client.LaborApi.SearchShifts(body));
                if (page.Shifts == null || page.Shifts.Count == 0) break; 
                
                shifts.AddRange(page.Shifts);
                cursor = page.Cursor;
            } while (!string.IsNullOrEmpty(cursor));

            return shifts;
        }

        private IEnumerable<EmployeeWage> GetEmployeeWages()
        {
            var employeeWages = new List<EmployeeWage>();
            SquareClient client = BuildClient();
            string? cursor = null;

            do
            {
                var page = InvokeWithDelay(() => client.LaborApi.ListEmployeeWages(null, null, cursor));
                if (page.EmployeeWages == null || page.EmployeeWages.Count == 0) break; 

                employeeWages.AddRange(page.EmployeeWages);
                cursor = page.Cursor;
            } while (!string.IsNullOrEmpty(cursor));

            return employeeWages;
        }

        public IEnumerable<Square.Models.Location> GetLocations()
        {
            var locations = new List<Square.Models.Location>();
            SquareClient client = BuildClient();

            var page = InvokeWithDelay(() => client.LocationsApi.ListLocations());
            locations.AddRange(page.Locations);

            return locations;
        }

        public Dictionary<string, Job> GetJobs()
        {
            var employeeWages = GetEmployeeWages();
            var jobMap = new Dictionary<string, Job>();

            foreach (var wage in employeeWages)
            {
                if (jobMap.ContainsKey(wage.Title)) continue;

                var job = new Job()
                {
                    Name = wage.Title,
                    Rate = wage.HourlyRate == null ? 0.00m : Convert.ToDecimal(wage.HourlyRate.Amount / 100)
                };

                jobMap.Add(wage.Title, job);
            }

            return jobMap;
        }
    }
}