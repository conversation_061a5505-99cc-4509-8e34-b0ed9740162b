﻿using System;
using System.Collections.Generic;
using System.Net;
using Payroll.Shared;
using Serilog;
using Square.Exceptions;

namespace Square.Tool
{
    class JobCommand
    {
        public void List(List<string> args)
        {
            try
            {
                var squareJobMapping = Config.SquareJobMappings();

                if (squareJobMapping.Count == 0)
                {
                    Log.Logger.Warning("Did not find any job mappings between Square and UtilPro");                    

                }                

                var service = new SquareService();
                var jobs = service.GetJobs();

                foreach (var job in jobs.Values)
                {
                    Console.WriteLine($"{job.Name}\t${job.Rate}");

                    if (squareJobMapping.ContainsKey(job.Name))
                    {
                        Console.WriteLine("Found Job Mapping");
                    }
                }
            }
            catch (Exception e)
            {
                Log.Logger.Fatal($"Exception: {e.Message}");
                Log.Logger.Fatal(e.StackTrace);
            }
        }
    }
}