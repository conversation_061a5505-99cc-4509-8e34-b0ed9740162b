﻿using System;
using System.Collections.Generic;
using System.Linq;
using AutoMapper;
using Newtonsoft.Json;
using Payroll.Shared;
using Serilog;
using Square.Models;

namespace Square.Tool
{
    class ExportCommand : PosExportCommand
    {
        public void Time(List<string> args)
        {
            if (args == null || args.Count < 3)
            {
                Console.WriteLine("Usage: Square.Tool.exe export time <restaurant> <start-day> <end-day>");
                Console.WriteLine("  where: <restaurant> is the restaurant location code like MBMB");
                Console.WriteLine(
                    "  where: <start-day> and <end-day> are integers representing how many days prior to today");
                Console.WriteLine(
                    "  e.g. 'Square.Tool.exe export time MBBR 7 1' would export time from 7 days ago till yesterday at the MBBR location.");
                return;
            }

            try
            {
                var config = new MapperConfiguration(cfg => {
                    cfg.CreateMap<Models.Break, Payroll.Shared.Break>();
                });

                IMapper iMapper = config.CreateMapper();

                // initialize directories to reduce API calls
                var location = args[0].Trim();
                BuildDateWindowsFromCommandLine(args[1], args[2], out DateTime startDate, out DateTime endDate);
                var restaurants = Config.RestaurantsById();

                if (!restaurants.TryGetValue(location, out RestaurantInfo? restaurant))
                {
                    Log.Logger.Fatal("Failed to find location {loc}.", location);
                    return;
                }

                Log.Logger.Information($"Fetching punches for location {restaurant.Id}");

                var service = new SquareService();

                var bodyQueryFilterWorkdayDateRange = new DateRange.Builder()
                    .StartDate(startDate.ToString("yyyy-MM-dd"))
                    .EndDate(endDate.ToString("yyyy-MM-dd"))
                    .Build();
                var bodyQueryFilterWorkday = new ShiftWorkday.Builder()
                    .DateRange(bodyQueryFilterWorkdayDateRange)
                    .MatchShiftsBy("START_AT")
                    .DefaultTimezone("America/New_York")
                    .Build();
                var bodyQueryFilter = new ShiftFilter.Builder()
                    .Workday(bodyQueryFilterWorkday)
                    .Build();

                var shifts = service.GetShifts(bodyQueryFilter);
                Log.Logger.Debug("Punches returned: {cnt}", shifts.Count());
                var shiftJson = JsonConvert.SerializeObject(shifts, Formatting.Indented);
                Console.WriteLine(shiftJson);

                var employees = service.GetActiveEmployees().ToDictionary(x => x.Id);
                var locations = Config.RestaurantsByCode();
                var punches = new List<PunchPair>();

                foreach (var shift in shifts)
                {
                    var startAt = Convert.ToDateTime(shift.StartAt);
                    var endAt = Convert.ToDateTime(shift.EndAt);

                    if (shift.Wage?.HourlyRate?.Amount == null)
                    {
                        Log.Logger.Error($"Skipping punch with empty wage for employee id {shift.EmployeeId}");
                        continue;
                    }

                    var wage = shift.Wage.HourlyRate.Amount / 100;
                    var jobCode = shift.Wage.Title;

                    if (!employees.ContainsKey(shift.EmployeeId))
                    {
                        Log.Logger.Error($"Skipping punch for unknown employee {shift.EmployeeId}");
                        continue;
                    }

                    if (!locations.ContainsKey(shift.LocationId))
                    {
                        Log.Logger.Error($"Skipping punch with unknown location {shift.LocationId}");
                        continue;
                    }

                    var punch = new PunchPair()
                    {
                        Date = startAt,
                        TimeIn = startAt,
                        TimeOut = endAt,
                        Location = locations[shift.LocationId].Id,
                        ClockSeq = employees[shift.EmployeeId].ReferenceId,
                        Rate = Convert.ToDecimal(wage),
                        JobCode = jobCode,
                        Description = "SQUARE"                        
                    };
                    
                    var breaks = iMapper.Map<List<Models.Break>, List<Payroll.Shared.Break>>(shift.Breaks.ToList());

                    punch.Breaks.AddRange(breaks);

                    punches.Add(punch);
                }

                Console.WriteLine();
                var json = JsonConvert.SerializeObject(punches, Formatting.Indented);
                Console.WriteLine(json);
            }
            catch (Exception e)
            {
                Log.Logger.Fatal(e.Message);
                Log.Logger.Fatal(e.StackTrace);
            }
        }
    }
}