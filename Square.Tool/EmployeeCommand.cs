﻿using System;
using System.Collections.Generic;
using System.Linq;
using Newtonsoft.Json;
using Payroll.Shared;
using Serilog;
using Square.Exceptions;
using Square.Models;
using Employee = Square.Models.Employee;

namespace Square.Tool
{
    class EmployeeCommand
    {
        public static IEnumerable<Square.Models.TeamMember> GetSquareEmployees(IList<string>? locations = null)
        {
            if (locations != null && locations.Count > 0)
                Log.Logger.Debug("Searching for employees at location {lid}", locations.First());

            try
            {
                var filter = new SearchTeamMembersFilter.Builder()
                    .Status("ACTIVE");
                if (locations != null)
                    filter.LocationIds(locations);

                var service = new SquareService();
                return service.GetTeamMembers(filter.Build());
            }
            catch (Exception e)
            {
                Log.Logger.Fatal($"Exception: {e.Message}");
                Log.Logger.Fatal(e.StackTrace);
            }

            return new List<TeamMember>();
        }

        public void Connections(List<string> args)
        {
            try
            {
                var employees = GetSquareEmployees();
                foreach (var employee in employees)
                {
                    if (!string.IsNullOrEmpty(employee.ReferenceId))
                        Console.WriteLine($"{employee.Id}\t{employee.EmailAddress}\t{employee.Status}");
                }
            }
            catch (Exception e)
            {
                Log.Logger.Fatal($"Exception: {e.Message}");
                Log.Logger.Fatal(e.StackTrace);
            }
        }

        public void Exceptions(List<string> args)
        {
            try
            {
                var employees = GetSquareEmployees();
                foreach (var employee in employees)
                {
                    if (string.IsNullOrEmpty(employee.ReferenceId))
                        Console.WriteLine($"{employee.Id}\t{employee.EmailAddress}\t{employee.Status}");
                }
            }
            catch (Exception e)
            {
                Log.Logger.Fatal($"Exception: {e.Message}");
                Log.Logger.Fatal(e.StackTrace);
            }
        }

        public void List(List<string> args)
        {
            try
            {
                var employees = GetSquareEmployees();
                foreach (var employee in employees)
                {
                    Console.WriteLine($"{employee.Id}\t{employee.EmailAddress}\t{employee.Status}");
                }
            }
            catch (Exception e)
            {
                Log.Logger.Fatal($"Exception: {e.Message}");
                Log.Logger.Fatal(e.StackTrace);
            }
        }
    }
}