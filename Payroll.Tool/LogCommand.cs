﻿using LiteDB;
using Payroll.Shared;
using System;
using System.Collections.Generic;
using System.IO;
using Serilog;

namespace Payroll.Tool
{
    public class LogCommand
    {
        public void Info(List<string> args)
        {
            if (args.Count == 0)
            {
                Console.WriteLine("Usage: Payroll.Tool log info <msg>");
            }

            Log.Logger.Information(args[0]);
        }
    }
}