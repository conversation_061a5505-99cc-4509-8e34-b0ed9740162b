﻿using System;
using System.Collections.Generic;
using Serilog;
using Payroll.Shared;
using System.Linq;
using LiteDB;

namespace Payroll.Tool
{
    class ResultsCommand
    {
        public static readonly string RESULTS_CACHE = "results";

        public void Clear(List<string> args)
        {
            try
            {
                using (var cache = CacheService.OpenCache(SystemConfig.CacheName()))
                {
                    var col = cache.GetCollection(RESULTS_CACHE);
                    col.DeleteAll();
                    Console.WriteLine("Cleared '{0}' cache", RESULTS_CACHE);
                }
            }
            catch (Exception e)
            {
                Log.Logger.Fatal(e.Message);
                Log.Logger.Fatal(e.StackTrace);
            }
        }

        public void List(List<string> args)
        {
            try
            {
                using (var cache = CacheService.OpenCache(SystemConfig.CacheName()))
                {
                    var col = cache.GetCollection<Result>(RESULTS_CACHE);
                    var results = col.Query().OrderBy(x => x.Id).ToList();

                    foreach (var result in results)
                    {
                        Console.WriteLine($"{result.Id}, {result.Timestamp}, {result.Success}, {result.ResultType}");
                    }
                }
            }
            catch (Exception e)
            {
                Log.Logger.Fatal(e.Message);
                Log.Logger.Fatal(e.StackTrace);
            }
        }
    }
}
