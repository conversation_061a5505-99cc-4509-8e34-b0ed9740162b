<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <OutputType>Exe</OutputType>
    <TargetFrameworks>net8.0;net9.0</TargetFrameworks>
    <Configurations>Debug;Release;CoreOnly</Configurations>
    <Platforms>AnyCPU</Platforms>
  </PropertyGroup>

  <PropertyGroup Condition=" '$(Configuration)' == 'Release' ">
    <DebugType>none</DebugType>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="CommandLineParser" Version="2.9.1" />
    <PackageReference Include="CsvHelper" Version="33.0.1" />
    <PackageReference Include="FluentEmail.Smtp" Version="3.0.2" />
    <PackageReference Include="MongoDB.Driver" Version="3.4.0" />
    <PackageReference Include="Newtonsoft.Json" Version="13.0.3" />
    <PackageReference Include="Serilog" Version="4.2.0" />
    <PackageReference Include="Serilog.Sinks.Console" Version="6.0.0" />
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\Payroll.Shared\Payroll.Shared.csproj" />
  </ItemGroup>

</Project>
