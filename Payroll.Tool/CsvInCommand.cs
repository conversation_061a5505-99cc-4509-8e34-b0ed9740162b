﻿using Payroll.Shared;
using Serilog;
using System;
using System.Collections.Generic;
using System.Linq;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;

namespace Payroll.Tool
{
    public class CsvInCommand
    {
        public void Employees(List<string> args)
        {
            try
            {
                var input = Console.In.ReadToEnd();
                var empList = JsonConvert.DeserializeObject<List<Employee>>(input);
                var employees = empList.ToDictionary(x => x.Id);

                var records = JArray.Parse(input);
                foreach (var record in records)
                {
                    var id = record["Id"].ToString();
                    for (int i = 1; i < 11; i++)
                    {
                        var jobName = record[$"JobName{i}"];
                        var payRate = record[$"Rate{i}"];
 
                        if (jobName.Type == JTokenType.String && !string.IsNullOrEmpty(payRate.ToString()))
                        {
                            var job = new Job()
                            {
                                Name = jobName.ToString(),
                                Rate = Convert.ToDecimal(payRate.ToString()),
                                Code = record[$"JobNumber{i}"].ToString()
                            };
                            employees[id].Jobs.Add(job);
                        }
                    }
                }

                Console.WriteLine(JsonConvert.SerializeObject(employees.Values, Formatting.Indented));
            }
            catch (Exception e)
            {
                Log.Logger.Error(e.Message);
                Log.Logger.Error(e.StackTrace);
            }
        }
    }
}