﻿using System;
using System.Collections.Generic;
using System.Diagnostics.Contracts;
using FluentEmail.Core;
using FluentEmail.Smtp;
using Serilog;
using System.Net.Mail;
using Payroll.Shared;
using System.Text;
using System.IO;

namespace Payroll.Tool
{
    public class MailCommand
    {
        private string SubjectForResult(Result result)
        {
            var name = result.Args?.GetValueOrDefault("name");
            var pkey = result.Args?.GetValueOrDefault("pkey");
            var date = result.Args?.GetValueOrDefault("date");
            var label = $"{name} - {pkey} - {date}";

            switch (result.ResultType)
            {
                case ResultType.NewHire:
                    return $"[OA Alert] Hired: {label}";
                case ResultType.ReHire:
                    return $"[OA Alert] Rehire: {label}";
                case ResultType.Change:
                    return $"[OA Alert] Change: {label}";
                case ResultType.Update:
                    return $"[OA Alert] Update: {label}";
                case ResultType.Termination:
                    return $"[OA Alert] Terminated: {label}";
                case ResultType.Error:
                    return SystemConfig.EmailSubjectForErrors;
                default:
                    return $"Unknown {label}";
            }
        }

        public void Results(List<string> args)
        {
            if (!ConsoleService.TryGetResultsFromInput(out var results))
            {
                return;
            }

            var notifyAddr = SystemConfig.NotifyAddress;
            var errorsNotifyAddr = SystemConfig.ErrorsNotifyAddress;

            if (args.Count > 0)
            {
                notifyAddr = args[0];
                errorsNotifyAddr = args[0];
            }

            try
            {
                using (var smtpClient = new SmtpClient(SystemConfig.SmtpHost))
                {
                    smtpClient.Credentials = new System.Net.NetworkCredential(SystemConfig.SmtpUsername, SystemConfig.SmtpPassword);

                    // Using Smtp Sender package (or set using AddSmtpSender in services)
                    Email.DefaultSender = new SmtpSender(smtpClient);

                    foreach (var result in results)
                    {
                        if (result.ResultType == ResultType.Error)
                        {
                            ProcessEmailError(errorsNotifyAddr,result);
                            continue;
                        }

                        var body = new StringBuilder();
                        foreach (var arg in result.Args)
                        {
                            body.AppendLine($"{arg.Key} = {arg.Value}");
                        }

                        Email
                        .From(SystemConfig.FromAddress)
                        .To(notifyAddr)
                        .Subject(SubjectForResult(result))
                        .Body(body.ToString())
                        .Send();
                    }
                }
            }
            catch (Exception e)
            {
                Log.Logger.Fatal(e.Message);
                Log.Logger.Fatal(e.StackTrace);
            }
        }

        private void ProcessEmailError(string errorsNotifyAddr, Result result)
        {
            // Read the email body template from the file specified in SystemConfig
            string bodyTemplate;
            try
            {
                bodyTemplate = File.ReadAllText(SystemConfig.EmailBodyForErrors);
            }
            catch (Exception e)
            {
                Log.Logger.Error($"Failed to read email template: {e.Message}");
                return;
            }

            // Replace $glcode$ with the value from result.Args
            var ext3 = result.Args.GetValueOrDefault("ext3", "-missing-");
            var fullname = result.Args.GetValueOrDefault("name", "-missing-");
            var id = result.Args.GetValueOrDefault("pkey", "-missing-");
            var title = result.Args.GetValueOrDefault("title", "-missing-");

            var body = bodyTemplate.Replace("$ext3$", ext3);
            body = body.Replace("$fullname$", fullname);
            body = body.Replace("$id$", id);
            body = body.Replace("$title$", title);

            // Append additional information
            var additionalInfo = new StringBuilder();
            foreach (var arg in result.Args)
            {
                additionalInfo.AppendLine($"{arg.Key} = {arg.Value}");
            }
            body += "\n\nAdditional Information:\n" + additionalInfo.ToString();

            Email
                .From(SystemConfig.FromAddress)
                .To(errorsNotifyAddr)
                .Subject(SubjectForResult(result))
                .Body(body)
                .Send();
        }

        public void Send(List<string> args)
        {
            if (args.Count == 0)
            {
                Console.WriteLine("Usage: Payroll.Tool mail send <subject> [<to-addr>]");
                return;
            }

            var body = Console.In.ReadToEnd();
            var subject = args[0];

            var notifyAddr = SystemConfig.NotifyAddress;
            if (args.Count > 1)
            {
                notifyAddr = args[1];
            }

            try
            {
                using (var smtpClient = new SmtpClient(SystemConfig.SmtpHost))
                {
                    smtpClient.Credentials = new System.Net.NetworkCredential(SystemConfig.SmtpUsername, SystemConfig.SmtpPassword);

                    // Using Smtp Sender package (or set using AddSmtpSender in services)
                    Email.DefaultSender = new SmtpSender(smtpClient);

                    Email
                    .From(SystemConfig.FromAddress)
                    .To(notifyAddr)
                    .Subject(subject)
                    .Body(body)
                    .Send();
                }
            }
            catch (Exception e)
            {
                Log.Logger.Fatal(e.Message);
                Log.Logger.Fatal(e.StackTrace);
            }
        }

        public void Attachment(List<string> args)
        {
            if (args.Count < 2)
            {
                Console.WriteLine("Usage: Payroll.Tool mail attachment <subject> <filename> [<to-addr>]");
                return;
            }

            var body = Console.In.ReadToEnd();
            var subject = args[0];
            var filename = args[1];

            var notifyAddr = SystemConfig.NotifyAddress;
            if (args.Count > 2)
            {
                notifyAddr = args[2];
            }

            try
            {
                using (var smtpClient = new SmtpClient(SystemConfig.SmtpHost))
                {
                    smtpClient.Credentials = new System.Net.NetworkCredential(SystemConfig.SmtpUsername, SystemConfig.SmtpPassword);

                    // Using Smtp Sender package (or set using AddSmtpSender in services)
                    Email.DefaultSender = new SmtpSender(smtpClient);

                    Email.From(SystemConfig.FromAddress).To(notifyAddr).Subject(subject).Body(body).AttachFromFilename(filename).Send();
                }
            }
            catch (Exception e)
            {
                Log.Logger.Fatal(e.Message);
                Log.Logger.Fatal(e.StackTrace);
            }
        }

        public void Test(List<string> args)
        {
            if (args.Count == 0)
            {
                Console.WriteLine("Usage: Payroll.Tool mail test <to-addr>");
            }

            try
            {
                using (var smtpClient = new SmtpClient(SystemConfig.SmtpHost))
                {
                    smtpClient.Credentials = new System.Net.NetworkCredential(SystemConfig.SmtpUsername, SystemConfig.SmtpPassword);

                    // Using Smtp Sender package (or set using AddSmtpSender in services)
                    Email.DefaultSender = new SmtpSender(smtpClient);

                    Email
                    .From(SystemConfig.FromAddress)
                    .To(args[0])
                    .Subject("Test Email from OA Service")
                    .Body($"Test email created at {DateTime.Now}")
                    .Send();

                }
            }
            catch (Exception e)
            {
                Log.Logger.Fatal(e.Message);
                Log.Logger.Fatal(e.StackTrace);
            }
        }
    }
}