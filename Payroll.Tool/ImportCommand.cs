﻿using System;
using System.IO;
using System.Collections.Generic;
using Newtonsoft.Json;
using Payroll.Shared;
using System.Text;
using Serilog;
using MongoDB.Bson;
using MongoDB.Driver;
using MongoDB.Bson.Serialization;

namespace Payroll.Tool
{
    public class ImportCommand
    {
        public void Employees(List<string> args)
        {
            try
            {
                var json = Console.In.ReadToEnd();
                var employees = JsonConvert.DeserializeObject<List<Payroll.Shared.Employee>>(json);

                if (employees == null)
                {
                    Console.WriteLine("--ERROR: failed to parse employee json");
                    Log.Logger.Error("Failed to parse employee json");
                    return;
                }

                ImportEmployees(employees);
            }
            catch (Exception e)
            {
                Log.Logger.Error("Failed to import employee json");
                Log.Logger.Error(e.Message);
            }
        }

        public void Punches(List<string> args)
        {
            try
            {
                var json = Console.In.ReadToEnd();
                var punches = JsonConvert.DeserializeObject<List<Payroll.Shared.PunchPair>>(json);

                if (punches == null)
                {
                    Console.WriteLine("--ERROR: failed to parse punch json");
                    Log.Logger.Error("Failed to parse punch json");
                    return;
                }

                ImportPunches(punches);
            }
            catch (Exception e)
            {
                Log.Logger.Error("Failed to import punch json");
                Log.Logger.Error(e.Message);
            }
        }

        private void ImportEmployees(IEnumerable<Payroll.Shared.Employee> employees)
        {
            try
            {
                var database = Db.Connect();
                var collection = database.GetCollection<Employee>("employees");
                BsonSerializer.UseZeroIdChecker = true; // used for value types

                var options = new ReplaceOptions { IsUpsert = true };
                foreach (var employee in employees)
                {
                    Log.Logger.Information("adding record for {id}", employee.Id);
                    var filter = Builders<Employee>.Filter.Eq(x=>x.Id, employee.Id);
                    collection.ReplaceOne(filter, employee, options);
                }
            }
            catch (Exception e)
            {
                Log.Logger.Error("Failed to import employee change json");
                Log.Logger.Error(e.Message);
                Log.Logger.Error(e.StackTrace);
            }
        }

        private void ImportPunches(IEnumerable<Payroll.Shared.PunchPair> punches)
        {
            try
            {
                var database = Db.Connect();
                IMongoCollection<PunchPair> collection = database.GetCollection<PunchPair>("punches");
                BsonSerializer.UseZeroIdChecker = true; // used for value types

                var options = new ReplaceOptions { IsUpsert = true };
                foreach (PunchPair punch in punches)
                {
                    Log.Logger.Information("adding punch record for {fname}{lname}",
                        punch.FirstName, punch.LastName);
                    FilterDefinition<PunchPair> filter = Builders<PunchPair>.Filter.Eq(x => x.Id, punch.Id);
                    collection.ReplaceOne(filter, punch, options);
                }
            }
            catch (Exception e)
            {
                Log.Logger.Error("Failed to import employee change json");
                Log.Logger.Error(e.Message);
                Log.Logger.Error(e.StackTrace);
            }
        }
    }
}