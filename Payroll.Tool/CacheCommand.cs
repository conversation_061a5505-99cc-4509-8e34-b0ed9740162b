﻿using System;
using System.Collections.Generic;
using Serilog;
using System.Text.Json;
using Payroll.Shared;
using System.Linq;
using LiteDB;

namespace Payroll.Tool
{
    class CacheCommand
    {
        public static readonly string EMPLOYEE_CACHE = "employees";

        public void Dump(List<string> args)
        {
            var filter = "all";
            if (args != null && args.Count > 0)
            {
                filter = args[0];
            }

            try
            {
                using (var cache = CacheService.OpenCache(SystemConfig.CacheName()))
                {
                    var col = cache.GetCollection<Employee>(EMPLOYEE_CACHE);
                    var employees = new List<Employee>();

                    if (filter == "term")
                    {
                        employees = col.Find(x => x.ClockSeq != null && x.Active == false).OrderBy(x => x.ClockSeq).ToList();

                        var maxDaysOld = -1;
                        if (args.Count > 1)
                        {
                            maxDaysOld = Convert.ToInt32(args[1]);
                        }

                        if (maxDaysOld != -1)
                        {
                            var now = DateTime.Now;
                            var recentTerms = employees.FindAll(x =>
                            {
                                var age = now - x.TermDate;
                                Log.Logger.Debug("Emp: {0}, Now: {1}, TermDate: {2}, Age: {3}, Max: {4}",
                                    x.Id, now.ToString("MM/dd/yyyy hh:mm:ss tt zzz"), x.TermDate?.ToString("MM/dd/yyyy hh:mm:ss tt zzz"), age?.TotalDays, maxDaysOld);
                                return age?.TotalDays < maxDaysOld;
                            });

                            employees = recentTerms;
                        }
                    }
                    else
                    {
                        IEnumerable<Employee> results = null;
                        DateTime now = DateTime.Now;

                        if (filter == "all") results = col.Query().ToList();
                        else if (filter == "new") // Rockhill specific, need to move elsewhere
                            results = col.Find(x => x.Active == true && (x.OrgCode == "new" || x.RehireDate > now.Date));
                        else if (filter == "cseq")
                            results = col.Find(x => x.ClockSeq != null && x.Active == true);
                        else if (filter == "active")
                            results = col.Find(x => x.Active == true);
                        else if (filter == "term")
                            results = col.Find(x => x.Active == false);

                        employees = results.OrderBy(x => x.ClockSeq).ToList();
                    }

                    ConsoleService.PrintFormattedJson(employees);
                }
            }
            catch (Exception e)
            {
                Log.Logger.Fatal(e.Message);
                Log.Logger.Fatal(e.StackTrace);
            }
        }

        public void Clear(List<string> args)
        {
            if (args == null || args.Count < 1)
            {
                Console.WriteLine("Usage: Payroll.Tool.exe cache clear [employees|stats]");
                return;
            }

            var scmd = args[0];
            switch (scmd)
            {
                // dont let users just pass any random collection name
                case "employees":
                    ClearCache("employees");
                    break;
                case "stats":
                    ClearCache("stats");
                    break;
                default:
                    Console.WriteLine(format: "Error: unrecognized cache dump object: {0}", scmd);
                    break;
            }
        }

        public void ClearCache(string name)
        {
            try
            {
                using (var cache = CacheService.OpenCache(SystemConfig.CacheName()))
                {
                    var col = cache.GetCollection(name);
                    col.DeleteAll();
                    Console.WriteLine("Cleared '{0}' cache", name);
                }
            }
            catch (Exception e)
            {
                Log.Logger.Fatal(e.Message);
                Log.Logger.Fatal(e.StackTrace);
            }
        }

        public void Import(List<string> args)
        {
            bool debugMode = false;
            if (args != null && args.Count > 0)
            {
                if (args[0] == "debug")
                {
                    debugMode = true;
                }
            }
            
            try
            {
                if (!ConsoleService.TryGetEmployeesFromInput(out List<Employee> employees))
                {
                    Log.Logger.Error("Failed to parse employee list");
                    return;
                }

                if (employees == null)
                {
                    Log.Logger.Error("Skipping null employees list");
                    return;
                }

                if (debugMode)
                {
                    foreach (var employee in employees)
                    {
                        Log.Logger.Information("Importing employee {cseq}/{fn} {ln} @ {loc}", employee.ClockSeq, employee.FirstName, employee.LastName, employee.PrimaryWorkLocation);
                        CacheService.CacheRecord<Employee>(EMPLOYEE_CACHE, employee);
                    }
                }
                else
                {
                    CacheService.CacheRecords<Employee>(EMPLOYEE_CACHE, employees);
                }
            }
            catch (Exception e)
            {
                Log.Logger.Fatal(e.Message);
                Log.Logger.Fatal(e.StackTrace);
            }
        }

        public void Info(List<string> args)
        {
            try
            {
                var time = CacheService.GetLastUpdatedTime();
                Console.WriteLine($"Last Updated: {time.ToString()}");
            }
            catch (Exception e)
            {
                Log.Logger.Fatal(e.Message);
                Log.Logger.Fatal(e.StackTrace);
            }
        }

        public void Remove(List<string> args)
        {
            if (args == null || args.Count < 2)
            {
                Console.WriteLine("Usage: Payroll.Tool.exe cache remove [employee] <id>");
                return;
            }

            var scmd = args[0];
            switch (scmd)
            {
                // dont let users just pass any random collection name
                case "employee":
                    CacheService.RemoveRecord<Employee>(EMPLOYEE_CACHE, args[1]);
                    break;
                default:
                    Console.WriteLine("Error: unrecognized cache remove cmd: {0}", scmd);
                    break;
            }
        }

        public void Time(List<string> args)
        {
            if (args != null && args.Count > 1)
            {
                if (args[0] == "run")
                {
                    CacheService.CacheLastRunTime(args[1], DateTime.Now);
                    return;
                }
            }
            else
            {
                CacheService.CacheLastUpdatedTime(DateTime.Now);
            }
        }

        public void View(List<string> args)
        {
            if (args == null || args.Count < 1)
            {
                Console.WriteLine("Usage: Payroll.Tool.exe cache view <emp-id>");
                return;
            }

            var idString = args[0];

            try
            {
                try
                {
                    var findEmp = CacheService.FetchRecord<Employee>(EMPLOYEE_CACHE, idString);
                    if (findEmp == null)
                    {
                        Console.WriteLine("Failed to find a cached employee with id: {0}", idString);
                        return;
                    }

                    var employees = new List<Employee>();
                    employees.Add(findEmp);
                    ConsoleService.PrintFormattedJson(employees);
                }
                catch (Exception e)
                {
                    Log.Logger.Error(e.Message);
                    Log.Logger.Error(e.StackTrace);
                }
            }
            catch (Exception e)
            {
                Log.Logger.Error(e.Message);
                Log.Logger.Error(e.StackTrace);
            }
        }
    }
}
