﻿using Payroll.Shared;
using Serilog;
using System;
using System.Collections.Generic;
using System.IO;
using System.Text.Json;

namespace Payroll.Tool
{
    public class CsvOutCommand
    {
        public void Punches(List<string> args)
        {
            if (!ConsoleService.TryGetPunchesFromInput(out List<PunchPair> punches))
                return;

            if (punches.Count == 0) return;

            // we check the first 10 punches to see if names are present in the data
            // if they are we include them in the output
            var namesAreIncluded = false;
            for (var i = 0; i < punches.Count; i++)
            {
                if (!string.IsNullOrEmpty(punches[i].FirstName) || !string.IsNullOrEmpty(punches[i].LastName))
                    namesAreIncluded = true;

                if (namesAreIncluded || i == 10) break;
            }

            Console.Write($"Location\tPayrollId\tTimeIn\tTimeOut\tJobCode\tRegHours\tRate\tSales\tTips\tOvtHours");
            
            if (namesAreIncluded)
                Console.Write("\tFirstName\tLastName");
            Console.WriteLine();

            foreach (var punch in punches)
            {
                var tipTotal = punch.CashTip + punch.NonCashTip;
                Console.Write($"{punch.Location}\t{punch.ClockSeq}\t{punch.TimeIn}\t{punch.TimeOut}\t{punch.JobCode}\t{punch.Hours}\t{punch.Rate}\t{punch.Sales}\t{tipTotal}\t{punch.Overtime}");
                if (namesAreIncluded)
                    Console.Write($"\t{punch.FirstName}\t{punch.LastName}");
                Console.WriteLine();
            }
        }
    }
}