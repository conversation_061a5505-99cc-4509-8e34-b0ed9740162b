﻿using Payroll.Shared;
using Serilog;
using System;
using System.Collections.Generic;
using System.Net.Http;
using System.Text.Json;

namespace Payroll.Tool
{
    public class TimeCommand
    {
        private readonly HttpClient _httpClient;

        public TimeCommand()
        {
            _httpClient = new HttpClient();
        }

        public int When(List<string> args)
        {
            if (args.Count != 3)
            {
                Console.WriteLine("Usage: time when <when - in 24 hour format, eg. 13 or 13:30> <latitude> <longitude>");
                return 1;
            }

            if (!double.TryParse(args[1], out double latitude) || !double.TryParse(args[2], out double longitude))
            {
                Console.WriteLine("Invalid latitude or longitude. Please provide valid numbers.");
                return 1;
            }

            string when = args[0];
            TimeSpan parsedWhen;

            if (!TryParseTimeInput(when, out parsedWhen))
            {
                Console.WriteLine("Invalid time format. Please use HH or HH:mm format.");
                return 1;
            }

            try
            {
                using (var service = new TimeService())
                {
                    var currentTime = service.GetCurrentTimeForLocation(latitude, longitude).Result;
                    var currentTimeSpan = currentTime.TimeOfDay;

                    // Compare only hours if minutes weren't specified
                    if (when.Length <= 2)
                    {
                        return currentTimeSpan.Hours == parsedWhen.Hours ? 0 : 1;
                    }

                    // Compare both hours and minutes
                    return currentTimeSpan.Hours == parsedWhen.Hours && currentTimeSpan.Minutes == parsedWhen.Minutes ? 0 : 1;
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error: {ex.Message}");
                return 1;
            }
        }

        private bool TryParseTimeInput(string input, out TimeSpan result)
        {
            result = TimeSpan.Zero;

            if (input.Length == 1 || input.Length == 2)
            {
                // Hour only format
                if (int.TryParse(input, out int hour) && hour >= 0 && hour <= 23)
                {
                    result = new TimeSpan(hour, 0, 0);
                    return true;
                }
            }
            else if (input.Length == 4 || input.Length == 5)
            {
                // HH:mm format
                return TimeSpan.TryParse(input, out result);
            }

            return false;
        }

        public int Now(List<string> args)
        {
            if (args.Count != 2)
            {
                Console.WriteLine("Usage: time now <latitude> <longitude>");
                return 1;
            }

            if (!double.TryParse(args[0], out double latitude) || !double.TryParse(args[1], out double longitude))
            {
                Console.WriteLine("Invalid latitude or longitude. Please provide valid numbers.");
                return 1;
            }

            try
            {
                using (var service = new TimeService())
                {
                    var currentTime = service.GetCurrentTimeForLocation(latitude, longitude).Result;
                    Console.WriteLine($"{currentTime:HH:mm}");
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error: {ex.Message}");
            }

            return 0;
        }

        public int Ago(List<string> args)
        {
            if (args.Count < 3)
            {
                Console.WriteLine("Usage: time ago <latitude> <longitude> <days>");
                return 1;
            }

            if (!double.TryParse(args[0], out double latitude) || !double.TryParse(args[1], out double longitude))
            {
                Console.WriteLine("Invalid latitude or longitude. Please provide valid numbers.");
                return 1;
            }

            try
            {
                var days = int.Parse(args[2]);
                using (var service = new TimeService())
                {
                    var currentTime = service.GetTimeForLocation(latitude, longitude, DateTimeOffset.UtcNow.AddDays(-days)).Result;
                    Console.WriteLine($"{currentTime:HH:mm}");
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error: {ex.Message}");
            }

            return 0;
        }

        public static bool IsDSTObserved(string timeZoneId, DateTime date)
        {
            try
            {
                TimeZoneInfo timeZone = TimeZoneInfo.FindSystemTimeZoneById(timeZoneId);
                return timeZone.IsDaylightSavingTime(date);
            }
            catch (TimeZoneNotFoundException)
            {
                Console.WriteLine("The specified time zone was not found.");
                return false;
            }
            catch (InvalidTimeZoneException)
            {
                Console.WriteLine("The specified time zone is invalid.");
                return false;
            }
        }

        public int Offset(List<string> args)
        {
            if (args.Count < 2)
            {
                Console.WriteLine("Usage: time offset <timezone> <days-ago>");
                return 1;
            }

            try
            {
                var timezone = args[0];
                var daysAgo = int.Parse(args[1]);

                var onDay = DateTime.UtcNow.AddDays(-daysAgo);
                var timeZone = TimeZoneInfo.FindSystemTimeZoneById(timezone);
                var offset = timeZone.GetUtcOffset(onDay);

                Console.WriteLine($"On {onDay:yyyy-MM-dd} the offset is {offset.TotalHours} hours. Daylight Saving: {timeZone.IsDaylightSavingTime(onDay)}");

                string timeZoneId = "America/New_York";
                bool isDST = IsDSTObserved(timeZoneId, onDay);
                Console.WriteLine($"Is DST observed in {timeZoneId} on {onDay}: {isDST}");

                return 0;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error: {ex.Message}");
                return 1;
            }
        }

        public int Punches(List<string> args)
        {
            if (args.Count < 1)
            {
                Console.WriteLine("Usage: time punches <days>");
                return 1;
            }

            var daysAgo = int.Parse(args[0]);
            var onDay = DateTime.Now.AddDays(-daysAgo).AddMinutes(5);

            // this is a hack, just for testing purposes...
            CacheService.CacheLastRunTime("PaycomPunches", onDay);
            return 0;
        }

        public int Zone(List<string> args)
        {
            if (args.Count < 2)
            {
                Console.WriteLine("Usage: time zone <latitude> <longitude>");
                return 1;
            }

            if (!double.TryParse(args[0], out double latitude) || !double.TryParse(args[1], out double longitude))
            {
                Console.WriteLine("Invalid latitude or longitude. Please provide valid numbers.");
                return 1;
            }

            try
            {
                using (var service = new TimeService())
                {
                    var info = service.GetTimeInfoForLocation(latitude, longitude, DateTimeOffset.UtcNow).Result;
                    Console.WriteLine(JsonSerializer.Serialize(info));
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error: {ex.Message}");
            }

            return 0;
        }
    }
}