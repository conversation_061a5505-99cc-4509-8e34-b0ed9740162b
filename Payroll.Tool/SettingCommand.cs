﻿using System;
using System.Collections.Generic;
using Payroll.Shared;
using Serilog;
using static System.Environment;

namespace Payroll.Tool
{
    public class SettingCommand
    {
        public void Init(List<string> args)
        {
            Setting.Init();
        }

        public void Set(List<string> args)
        {
            if (args == null || args.Count < 3)
            {
                Console.WriteLine("Usage: Payroll.Tool.exe setting set <section> <name> <value>");
                return;
            }

            var section = args[0];
            var key = args[1];
            var val = args[2];

            Log.Logger.Information("Storing setting {section}-{key}={val}", section, key, val);
            Setting.Set(section, key, val);
        }

        public void Get(List<string> args)
        {
            if (args == null || args.Count < 2)
            {
                Console.WriteLine("Usage: Payroll.Tool.exe setting get <section> <name>");
                return;
            }

            var section = args[0];
            var key = args[1];
            var val = Setting.Get(section, key);
            Console.WriteLine(val);
        }

        public void List(List<string> args)
        {
            Dictionary<string, string> settings;
            if (args != null && args.Count > 0)
            {
                settings = Setting.ListSection(args[0]);
            }
            else settings = Setting.List();

            foreach (var entry in settings)
            {
                Console.WriteLine($"{entry.Key}\t{entry.Value}");
            }
        }
    }
}