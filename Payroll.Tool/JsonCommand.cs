﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Text.Json;
using Payroll.Shared;
using Serilog;

namespace Payroll.Tool
{
    public class JsonCommand
    {
        public int Count(List<string> args)
        {
            try
            {
                var jsonInput = Console.In.ReadToEnd();
                var json = JsonSerializer.Deserialize<JsonElement>(jsonInput);
                if (json.ValueKind == JsonValueKind.Array)
                {
                    Console.WriteLine(json.GetArrayLength());
                    return ExitCode.Success;
                }
            }
            catch (Exception)
            {
                // just eat it
            }

            Console.WriteLine("0");
            return ExitCode.Failure;
        }

        public int Grep(List<string> args)
        {
            if (args.Count == 0)
            {
                Console.WriteLine("Usage: Payroll.Tool json grep <property>");
                return ExitCode.Failure;
            }

            var propName = args[0];

            try
            {
                string input = Console.In.ReadToEnd();
                string[] lines = input.Split(new[] { Environment.NewLine }, StringSplitOptions.RemoveEmptyEntries);

                foreach (string line in lines)
                {
                    if (string.IsNullOrWhiteSpace(line))
                        continue;

                    try
                    {
                        var json = JsonSerializer.Deserialize<JsonElement>(line);
                        var mtValue = json.GetProperty(propName);
                        Console.WriteLine(mtValue.GetString());
                    }
                    catch (JsonException)
                    {
                        // just silently eat these json parsing errors
                    }
                }
            }
            catch (Exception e)
            {
                Log.Logger.Error($"Error: {e.Message}");
            }

            return ExitCode.Failure;
        }
    }
}