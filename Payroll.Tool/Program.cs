using System;
using System.Collections.Generic;
using System.Reflection;
using CommandLine;
using Payroll.Shared;
using Serilog;
using static System.Environment;

namespace Payroll.Tool
{
    class Program : ProgramBase<SettingCommand>
    {
        public static readonly string AppVersion = "1.2.6";

        static void DoShowUsage()
        {
            Payroll.Shared.Setting.Init();
            Console.WriteLine("Usage: Payroll.Tool.exe <commmand> <command-args>");
            Console.WriteLine($" -- version {AppVersion} ({Payroll.Shared.Setting.IniFilePath})");
            Console.WriteLine($" -- {OSVersion.Platform} {OSVersion.VersionString}");
            Console.WriteLine();
            Console.WriteLine("  where <command> is one of...");
            Console.WriteLine("   - cache [clear|dump<active|all|new|cseq|terms>|import <debug>|info|remove|view|stats|time] = cache management");
            Console.WriteLine("   - config [list] = config management");
            Console.WriteLine("   - csvin [employees] = convert CSV input to JSON");
            Console.WriteLine("   - csvout [punches] = convert JSON input to CSV");
            Console.WriteLine("   - convert [dentries] = convert to mirror types");
            Console.WriteLine("   - log [info] <msg> = send <msg> to log");
            Console.WriteLine("   - mail [results|send|attachment|test]");
            Console.WriteLine("   - results [clear|list] = results management");
            Console.WriteLine("   - stats [clear|list] = stats management");
            Console.WriteLine("   - setting [list, set, get, init] = settings management");
            Console.WriteLine();
            Console.WriteLine("   - import [changes, employees] = import change records to db");
            Console.WriteLine("   - db [test] = test db connection");
            Console.WriteLine("   - employee [list, view (id|active|terminated)] = list employees in db");

            Console.WriteLine();
            Console.WriteLine("Notes");
            Console.WriteLine("- The 'cache dump' command is used to export records from the cache.");
            Console.WriteLine("- The 'cache view' command is used to show one employee's information.");
            Console.WriteLine("- The 'cache stats' command shows API usage stats.");
            Console.WriteLine("- The 'cache import debug' command imports employees one by one");
            Console.WriteLine("- All other commands are used for debugging purposes");
        }

        public override int ShowUsage()
        {
            DoShowUsage();
            return 0;
        }

        public int Help(List<string> args)
        {
            return ShowUsage();
        }

        private int ExecCommand<T>(List<string> args) where T : new()
        {
            var command = new CommandArguments(args);
            return Command<T>.Invoke(command);
        }

        public int Cache(List<string> args)
        {
            return ExecCommand<CacheCommand>(args);
        }

        public int Config(List<string> args)
        {
            return ExecCommand<ConfigCommand>(args);
        }

        public int Clear(List<string> args)
        {
            return ExecCommand<ClearCommand>(args);
        }

        public int Convert(List<string> args)
        {
            return ExecCommand<ConvertCommand>(args);
        }

        public int Csvin(List<string> args)
        {
            return ExecCommand<CsvInCommand>(args);
        }

        public int Csvout(List<string> args)
        {
            return ExecCommand<CsvOutCommand>(args);
        }

        public int Db(List<string> args)
        {
            return ExecCommand<DbCommand>(args);
        }

        public int Date(List<string> args)
        {
            return ExecCommand<DateCommand>(args);
        }

        public int Diff(List<string> args)
        {
            return ExecCommand<DiffCommand>(args);
        }

        public int Employee(List<string> args)
        {
            return ExecCommand<EmployeeCommand>(args);
        }

        public int Export(List<string> args)
        {
            return ExecCommand<ExportCommand>(args);
        }

        public int Import(List<string> args)
        {
            return ExecCommand<ImportCommand>(args);
        }

        public int Mail(List<string> args)
        {
            return ExecCommand<MailCommand>(args);
        }

        public int Results(List<string> args)
        {
            return ExecCommand<ResultsCommand>(args);
        }

        public int Stats(List<string> args)
        {
            return ExecCommand<StatsCommand>(args);
        }

        public void Info(List<string> args)
        {
            Console.WriteLine($"Payroll.Tool.exe version {AppVersion} ({Shared.Setting.IniFilePath})");
            Console.WriteLine($"  Branch: {BuildInfo.Branch}, Built: {BuildInfo.Built}, Host: {BuildInfo.Host}, Hash: {BuildInfo.Commit}");
            Console.WriteLine();
            Console.WriteLine($"  Settings:");
            Console.WriteLine($"    DSN         = {Shared.SystemConfig.Dsn}");
            Console.WriteLine($"    SMTP HOST   = {Shared.SystemConfig.SmtpHost}");
            Console.WriteLine($"    SMTP UID    = {Shared.SystemConfig.SmtpUsername}");
            Console.WriteLine($"    SMTP PWD    = {Shared.SystemConfig.SmtpPassword}");
            Console.WriteLine();
            Console.WriteLine($"    Notify Address = {Shared.SystemConfig.NotifyAddress}");
            Console.WriteLine($"    Errors Notify Address = {Shared.SystemConfig.ErrorsNotifyAddress}");
            Console.WriteLine($"    Email Subject = {Shared.SystemConfig.EmailSubjectForErrors}");
            Console.WriteLine($"    Email Body    = {Shared.SystemConfig.EmailBodyForErrors}");
        }

        public int Json(List<string> args)
        {
            return ExecCommand<JsonCommand>(args);
        }

        public int Log(List<string> args)
        {
            return ExecCommand<LogCommand>(args);
        }

        public int Time(List<string> args)
        {
            return ExecCommand<TimeCommand>(args);
        }

        static int Main(string[] args)
        {
            if (args.Length == 0)
            {
                DoShowUsage();
                return -1;
            }

            // setup logging services...
            string command = String.Join(" ", args);
            Shared.Logger.Setup($"Payroll.Tool.Exe - Command: '{command}', Version: {AppVersion}");
            
            // Log version information
            Serilog.Log.Logger.Information($"Payroll.Tool.exe version {AppVersion} ({Shared.Setting.IniFilePath})");
            Serilog.Log.Logger.Information($"  Branch: {BuildInfo.Branch}, Built: {BuildInfo.Built}, Host: {BuildInfo.Host}, Hash: {BuildInfo.Commit}");

            int rc = 0;
            try
            {
                Parser.Default.ParseArguments<ProgramArguments>(args)
                    .MapResult((ProgramArguments opts) => rc = ProgramDriver<Program>.Run(opts), errs => 1);
            }
            catch (Exception e)
            {
                Console.WriteLine(e.Message);
                Console.WriteLine(e.StackTrace);
                return 1;
            }

            return rc;
        }
    }
}
