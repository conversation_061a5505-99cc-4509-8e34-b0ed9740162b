﻿using Payroll.Shared;
using System;
using System.Collections.Generic;
using System.IO;
using MongoDB.Bson;
using MongoDB.Driver;
using MongoDB.Driver.Linq;
using Newtonsoft.Json;
using Serilog;
using System.Linq;

namespace Payroll.Tool
{
    public class EmployeeCommand
    {
        public void List(List<string> args)
        {
            if (args != null && args.Count > 0)
            {
                if (args[0] == "active") ViewActive(args);
                else if (args[0] == "terms") ViewTerminated(args);
            }
            else
                PrintSelectedEmployees(x => true);
        }

        public Int16 View(List<string> args)
        {
            if (args == null || args.Count < 1)
            {
                Console.WriteLine("Usage: Payroll.Tool.exe employee view <id>");
                return 1;
            }

            var empId = args[0].Trim();

            try
            {
                var database = Db.Connect();
                var collection = database.GetCollection<Employee>("employees");
                var filter = Builders<Employee>.Filter.Eq(x => x.Id, empId);
                var employee = collection.Find(filter).First();
                Console.WriteLine(JsonConvert.SerializeObject(employee, Formatting.Indented));
            }
            catch (Exception e)
            {
                Log.Logger.Error(e.Message);
                Log.Logger.Error(e.StackTrace);
                return 2;
            }

            return 0;
        }

        public bool ViewActive(List<string> args)
        {
            return PrintSelectedEmployees(x => x.Active);
        }

        public bool ViewTerminated(List<string> args)
        {
           return PrintSelectedEmployees(x => !x.Active);
        }

        bool PrintSelectedEmployees(Func<Employee, bool> match)
        {
            try
            {
                var database = Db.Connect();
                var collection = database.GetCollection<Employee>("employees");
                var query = collection.AsQueryable<Employee>().Where(match);

                foreach (var employee in query)
                {
                    var empName = $"{employee.FirstName} {employee.LastName}";
                    Console.WriteLine($"{employee.Id.PadRight(10)}\t{employee.ClockSeq}\t{employee.HireDate?.ToShortDateString()}\t{empName.PadRight(22)}");
                }
            }
            catch (Exception e)
            {
                Log.Logger.Error(e.Message);
                Log.Logger.Error(e.StackTrace);
                return false;
            }

            return true;
        }
    }
}