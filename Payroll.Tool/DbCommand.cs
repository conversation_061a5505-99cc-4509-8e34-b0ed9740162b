﻿using Payroll.Shared;
using System;
using System.Collections.Generic;
using System.IO;
using MongoDB.Bson;
using MongoDB.Driver;
using Serilog;

namespace Payroll.Tool
{
    public class DbCommand
    {
        public void Test(List<string> args)
        {
            try
            {
                var database = Db.Connect();
                var collections = database.ListCollectionNames();
                foreach (var collection in collections.ToEnumerable())
                {
                    Console.WriteLine(collection);
                }
            }
            catch (Exception e)
            {
                Log.Logger.Error(e.Message);
            }
        }
    }
}