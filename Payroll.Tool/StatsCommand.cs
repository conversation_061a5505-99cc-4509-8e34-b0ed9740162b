﻿using System;
using System.Collections.Generic;
using Serilog;
using Payroll.Shared;
using System.Linq;
using LiteDB;

namespace Payroll.Tool
{
    class StatsCommand
    {
        public static readonly string STATS_CACHE = "stats";

        public void Clear(List<string> args)
        {
            try
            {
                using (var cache = CacheService.OpenCache(SystemConfig.CacheName()))
                {
                    var col = cache.GetCollection(STATS_CACHE);
                    col.DeleteAll();
                    Console.WriteLine("Cleared '{0}' cache", STATS_CACHE);
                }
            }
            catch (Exception e)
            {
                Log.Logger.Fatal(e.Message);
                Log.Logger.Fatal(e.StackTrace);
            }
        }

        public void List(List<string> args)
        {
            try
            {
                using (var cache = CacheService.OpenCache(SystemConfig.CacheName()))
                {
                    var col = cache.GetCollection<Stat>(STATS_CACHE);
                    var stats = col.Query().OrderBy(x => x.Id).ToList();

                    int maxLen = 0;
                    foreach (var stat in stats)
                    {
                        if (stat.Id.Length > maxLen) maxLen = stat.Id.Length;
                    }

                    foreach (var stat in stats)
                    {
                        if (stat.Id == "LastUpdatedTime" || stat.Id.StartsWith("LastRunTime_"))
                            Console.WriteLine($"{stat.Id.PadRight(maxLen)} = {DateTime.FromBinary(stat.Value)}");
                        else
                            Console.WriteLine($"{stat.Id.PadRight(maxLen)} = {stat.Value}");
                    }
                }
            }
            catch (Exception e)
            {
                Log.Logger.Fatal(e.Message);
                Log.Logger.Fatal(e.StackTrace);
            }
        }
    }
}
