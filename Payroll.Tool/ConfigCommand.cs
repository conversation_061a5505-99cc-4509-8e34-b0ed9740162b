﻿using Payroll.Shared;
using System;
using System.Collections.Generic;
using System.IO;

namespace Payroll.Tool
{
    public class ConfigCommand
    {
        public void List(List<string> args)
        {
            var path = Setting.DefaultSettingsPath();
            var files = Directory.EnumerateFiles(path);
            foreach (var file in files)
            {
                Console.WriteLine(file);
            }
        }
    }
}