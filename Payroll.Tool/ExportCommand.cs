﻿using Payroll.Shared;
using System;
using System.Collections.Generic;
using System.IO;
using MongoDB.Bson;
using MongoDB.Driver;
using MongoDB.Driver.Linq;
using Newtonsoft.Json;
using Serilog;
using System.Linq;

namespace Payroll.Tool
{
    public class ExportCommand
    {
        public void Settings(List<string> args)
        {
            {
                var atoken = Environment.GetEnvironmentVariable(SystemConfig.ENV_KEY_TIMEZONE_API_KEY);
                Console.WriteLine($"export {SystemConfig.ENV_KEY_TIMEZONE_API_KEY}={atoken}");
            }
        }

        public void Employees(List<string> args)
        {
            if (args != null && args.Count > 0)
            {
                if (args[0] == "location") PrintLocationEmployees(args);
            }
            else
                PrintSelectedEmployees(x => true);
        }

        public bool PrintLocationEmployees(List<string> args)
        {
            if (args != null && args.Count > 1)
            {
                string locationId = args[1];
                return PrintSelectedEmployees(x => {
                    if (x.PrimaryWorkLocation == locationId) return true;

                    if (x.Jobs != null)
                    {
                        foreach (var job in x.Jobs)
                        {
                            if (job.Location == locationId) return true;
                        }
                    }

                    return false;
                });
            }

            Console.WriteLine("Usage: dotnet Payroll.Tool export employees location <lid>");
            return false;
        }

        public bool ViewTerminated(List<string> args)
        {
           return PrintSelectedEmployees(x => !x.Active);
        }

        bool PrintSelectedEmployees(Func<Employee, bool> match)
        {
            try
            {
                var database = Db.Connect();
                var collection = database.GetCollection<Employee>("employees");
                var query = collection.AsQueryable<Employee>().Where(match);
                Console.WriteLine(JsonConvert.SerializeObject(query.ToList(), Formatting.Indented));
            }
            catch (Exception e)
            {
                Log.Logger.Error(e.Message);
                Log.Logger.Error(e.StackTrace);
                return false;
            }

            return true;
        }
    }
}