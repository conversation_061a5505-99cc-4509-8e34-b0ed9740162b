﻿using System;
using System.IO;
using System.Collections.Generic;
using Newtonsoft.Json;
using Payroll.Shared;
using System.Text;
using Serilog;
using MongoDB.Bson;
using MongoDB.Driver;
using MongoDB.Bson.Serialization;

namespace Payroll.Tool
{
    public class ClearCommand
    {
        private bool UserConfirmed()
        {
            // Ask the user to confirm the operation.
            Console.WriteLine("Are you sure you want to continue? (Y/N)");
            ConsoleKeyInfo yesNo = Console.ReadKey(true);

            // If the user presses 'Y', continue with the operation.
            return (yesNo.Key == ConsoleKey.Y);
        }

        public void Changes(List<string> args)
        {
            try
            {
                var database = Db.Connect();
                var collection = database.GetCollection<BsonDocument>("changes");
                var allDocsFilter = new BsonDocument();
                
                Console.WriteLine("This will delete all {0} changes.", collection.CountDocuments(allDocsFilter));
                if (!UserConfirmed()) return;
            
                collection.DeleteMany(allDocsFilter);
            }
            catch (Exception e)
            {
                Log.Logger.Error("Failed to delete all changes");
                Log.Logger.Error(e.Message);
                Log.Logger.Error(e.StackTrace);
            }
        }

        public void Employees(List<string> args)
        {
            try
            {
                var database = Db.Connect();
                var collection = database.GetCollection<Employee>("employees");
                var allDocsFilter = new BsonDocument();
                
                Console.WriteLine("This will delete all {0} employees.", collection.CountDocuments(allDocsFilter));
                if (!UserConfirmed()) return;
            
                collection.DeleteMany(allDocsFilter);
            }
            catch (Exception e)
            {
                Log.Logger.Error("Failed to delete all employees");
                Log.Logger.Error(e.Message);
                Log.Logger.Error(e.StackTrace);
            }
        }
    }
}