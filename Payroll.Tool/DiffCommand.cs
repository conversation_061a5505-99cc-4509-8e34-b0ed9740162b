﻿using LiteDB;
using Payroll.Shared;
using System;
using System.Collections.Generic;
using System.IO;

namespace Payroll.Tool
{
    public class Record
    {
        public string Id { get; set; }
        public DateTime Created { get; set; }
    }

    public class DiffCommand
    {
        private string BuildDatabaseFilename(string id)
        {
            var path = Setting.DefaultSettingsPath();
            return Path.Combine(path, $"{id}.db");
        }

        public void Punches(List<string> args)
        {
            var jsonText = Console.In.ReadToEnd();
            var json = JsonSerializer.DeserializeArray(jsonText);

            using (var db = new LiteDatabase(BuildDatabaseFilename("punches")))
            {
                var col = db.GetCollection<Record>("records");
            }

            Console.WriteLine(json);
        }
    }
}