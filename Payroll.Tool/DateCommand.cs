﻿using LiteDB;
using Payroll.Shared;
using System;
using System.Collections.Generic;
using System.IO;

namespace Payroll.Tool
{
    public class DateCommand
    {
        public void From(List<string> args)
        {
            ConsoleService.GetDateWindowFromArgs(args, out DateTime fromDate, out DateTime toDate);
            Console.WriteLine(fromDate.ToString("yyyyMMdd"));
        }

        public void Window(List<string> args)
        {
            ConsoleService.GetDateWindowFromArgs(args, out DateTime startDate, out DateTime endDate);
            Console.WriteLine($"Start: {startDate}");
            Console.WriteLine($"End: {endDate}");
        }
    }
}