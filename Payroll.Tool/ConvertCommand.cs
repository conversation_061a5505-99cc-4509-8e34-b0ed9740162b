﻿using Payroll.Shared;
using System;
using System.Collections.Generic;
using System.IO;
using MongoDB.Bson;
using MongoDB.Driver;
using MongoDB.Driver.Linq;
using Newtonsoft.Json;
using Serilog;
using System.Linq;
using System.Text.Json;

namespace Payroll.Tool
{
    public class ConvertCommand
    {
        public void Dentries(List<string> args)
        {
            try
            {
                if (!ConsoleService.TryGetDirectoryEntriesFromInput(out var dentries))
                {
                    Log.Logger.Error("Failed to parse employee directory entries list");
                    return;
                }

                if (dentries == null || dentries.Count == 0)
                {
                    Log.Logger.Error("Failed to load employee directory entries list");
                    return;
                }

                var employees = new List<Employee>();
                foreach (var dentry in dentries)
                {
                    var e = Converter.DirectoryEntryToEmployee(dentry);
                    employees.Add(e);
                }

                string formattedJson = System.Text.Json.JsonSerializer.Serialize(employees, new JsonSerializerOptions { WriteIndented = true });
                Console.WriteLine(formattedJson);
            }
            catch (Exception e)
            {
                Log.Logger.Fatal(e.Message);
                if (!string.IsNullOrEmpty(e.StackTrace)) Log.Logger.Fatal(e.StackTrace);
            }
        }
    }
}