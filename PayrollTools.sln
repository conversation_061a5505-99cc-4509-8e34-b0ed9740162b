
Microsoft Visual Studio Solution File, Format Version 12.00
# Visual Studio Version 17
VisualStudioVersion = 17.4.33103.184
MinimumVisualStudioVersion = 10.0.40219.1
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "Solution Items", "Solution Items", "{66C5AF00-AEDA-442D-B4DC-10D31E3FEFB2}"
	ProjectSection(SolutionItems) = preProject
		.gitattributes = .gitattributes
		.gitignore = .gitignore
		.gitlab-ci.yml = .gitlab-ci.yml
		Changelog.txt = Changelog.txt
		Makefile = Makefile
		Rules.mk = Rules.mk
		AGENT.md = AGENT.md
	EndProjectSection
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "Payroll.Shared", "Payroll.Shared\Payroll.Shared.csproj", "{FE0BD7B5-630B-4E25-A6D5-AE91350B9BB9}"
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "Clients", "Clients", "{FFFDD2A0-11E5-4A4F-A923-6B6C7B1FF447}"
	ProjectSection(SolutionItems) = preProject
		Clients\init-machine.sh = Clients\init-machine.sh
	EndProjectSection
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "Brink.Tool", "Brink.Tool\Brink.Tool.csproj", "{42B8CB45-3DCC-45C4-9A05-77ABB8833F98}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "Payroll.Tool", "Payroll.Tool\Payroll.Tool.csproj", "{DD2C22E1-0BEA-4C1C-A9C0-1E0F2ACBE58A}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "Toast.Tool", "Toast.Tool\Toast.Tool.csproj", "{5B221B45-E2A9-4733-A396-A9F1B4248D16}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "Brink.Shared", "Brink.Shared\Brink.Shared.csproj", "{B77CEF3B-B0BE-44E3-81DA-095F4D900BAD}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "Square.Tool", "Square.Tool\Square.Tool.csproj", "{DF5DD962-B76D-43C3-BE2F-A17680A4852D}"
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "ArtBrew", "ArtBrew", "{53E441AD-E906-4886-B622-7DDEE62E74B0}"
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "docs", "docs", "{C8C2F454-B14F-4B07-BB87-2048DA2192D3}"
	ProjectSection(SolutionItems) = preProject
		docs\filelogging.json = docs\filelogging.json
		docs\googlelogging.json = docs\googlelogging.json
	EndProjectSection
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "7shifts.Tool", "7shifts.Tool\7shifts.Tool.csproj", "{B282EB1A-8EBB-4F4B-BA46-0F4C5114F210}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "Radar.Tool", "Radar.Tool\Radar.Tool.csproj", "{7BA36281-80F8-4FE3-B5D5-A60588808872}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "Paylocity.Tool", "Paylocity.Tool\Paylocity.Tool.csproj", "{2E40931F-09FA-471B-8EFA-DF148367984A}"
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "Upward", "Upward", "{144B4FBA-0A39-4375-809F-D4718CB37C08}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "AD.Tool", "AD.Tool\AD.Tool.csproj", "{E95694D1-93A5-4033-95EE-A8DD1FD2D531}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "Paycom2.Tool", "Paycom2.Tool\Paycom2.Tool.csproj", "{FE43DF1D-09F9-4DCB-A4E6-864482949554}"
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "RockHill", "RockHill", "{EC5F50C7-8E52-401F-A6F1-404C6790AB29}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "Paycom2.Shared", "Paycom2.Shared\Paycom2.Shared.csproj", "{C9D5F93E-15F1-4BCD-BF65-8A4BA1D13799}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "AD.Shared", "AD.Shared\AD.Shared.csproj", "{A9D9ACAF-6791-4413-9391-5064CDAD9342}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "RockHill.Tool", "Clients\RockHill\Tool\RockHill.Tool.csproj", "{41CB72E7-975A-4F0E-A322-D48D5E33FDD4}"
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "Scripts", "Scripts", "{D2D5D98A-2FBE-4279-B41D-11D14A8B748B}"
	ProjectSection(SolutionItems) = preProject
		Clients\RockHill\Scripts\create-mailboxes.ps1 = Clients\RockHill\Scripts\create-mailboxes.ps1
		Clients\RockHill\Scripts\init-exchange-auth.ps1 = Clients\RockHill\Scripts\init-exchange-auth.ps1
		Clients\RockHill\Scripts\init-system.sh = Clients\RockHill\Scripts\init-system.sh
		Clients\RockHill\Scripts\README.txt = Clients\RockHill\Scripts\README.txt
		Clients\RockHill\Scripts\remove-mailbox.ps1 = Clients\RockHill\Scripts\remove-mailbox.ps1
		Clients\RockHill\Scripts\RockHillWorkFlowProcess.pdf = Clients\RockHill\Scripts\RockHillWorkFlowProcess.pdf
		Clients\RockHill\Scripts\settings.ini = Clients\RockHill\Scripts\settings.ini
		Clients\RockHill\Scripts\sync-existing.sh = Clients\RockHill\Scripts\sync-existing.sh
		Clients\RockHill\Scripts\sync-terms.sh = Clients\RockHill\Scripts\sync-terms.sh
		Clients\RockHill\Scripts\update-mailbox-list.ps1 = Clients\RockHill\Scripts\update-mailbox-list.ps1
		Clients\RockHill\Scripts\list-mailboxes.ps1 = Clients\RockHill\Scripts\list-mailboxes.ps1
		Clients\RockHill\Scripts\sync-hires-one.sh = Clients\RockHill\Scripts\sync-hires-one.sh
		Clients\RockHill\Scripts\sync-hires-all.sh = Clients\RockHill\Scripts\sync-hires-all.sh
		Clients\RockHill\Scripts\sync-terms-immediate.sh = Clients\RockHill\Scripts\sync-terms-immediate.sh
		Clients\RockHill\Scripts\cache-refresh.sh = Clients\RockHill\Scripts\cache-refresh.sh
		Clients\RockHill\Scripts\cache-changes.sh = Clients\RockHill\Scripts\cache-changes.sh
		Clients\RockHill\Scripts\sync-existing-one.sh = Clients\RockHill\Scripts\sync-existing-one.sh
		Clients\RockHill\Scripts\test.ps1 = Clients\RockHill\Scripts\test.ps1
		Clients\RockHill\Scripts\log-stats.sh = Clients\RockHill\Scripts\log-stats.sh
		Clients\RockHill\Scripts\errors.mail.template = Clients\RockHill\Scripts\errors.mail.template
		Clients\RockHill\Scripts\update-all-start-in-folders.ps1 = Clients\RockHill\Scripts\update-all-start-in-folders.ps1
		Clients\RockHill\Scripts\list-all-start-in-folders.ps1 = Clients\RockHill\Scripts\list-all-start-in-folders.ps1
	EndProjectSection
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "Scripts", "Scripts", "{FC92ED14-8957-40CE-8A35-45D004D47B49}"
	ProjectSection(SolutionItems) = preProject
		Clients\ArtBrew\Scripts\employee-sync-all-stores.sh = Clients\ArtBrew\Scripts\employee-sync-all-stores.sh
		Clients\ArtBrew\Scripts\export-time-for-all-stores.sh = Clients\ArtBrew\Scripts\export-time-for-all-stores.sh
		Clients\ArtBrew\Scripts\settings.ini = Clients\ArtBrew\Scripts\settings.ini
		Clients\ArtBrew\Scripts\export-time-biz-for-all-stores.sh = Clients\ArtBrew\Scripts\export-time-biz-for-all-stores.sh
	EndProjectSection
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "Scripts", "Scripts", "{9C208364-752C-41C1-93B3-02190A304861}"
	ProjectSection(SolutionItems) = preProject
		Clients\Upward\Scripts\active-sync.sh = Clients\Upward\Scripts\active-sync.sh
		Clients\Upward\Scripts\beta-sync.sh = Clients\Upward\Scripts\beta-sync.sh
		Clients\Upward\Scripts\emp-cloud-sync.sh = Clients\Upward\Scripts\emp-cloud-sync.sh
		Clients\Upward\Scripts\export-punches.sh = Clients\Upward\Scripts\export-punches.sh
		Clients\Upward\Scripts\settings.ini = Clients\Upward\Scripts\settings.ini
		Clients\Upward\Scripts\sync-to-toast.sh = Clients\Upward\Scripts\sync-to-toast.sh
		Clients\Upward\Scripts\report-exceptions.sh = Clients\Upward\Scripts\report-exceptions.sh
		Clients\Upward\Scripts\validate-toast-accounts.sh = Clients\Upward\Scripts\validate-toast-accounts.sh
	EndProjectSection
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "ArtBrew.Tool", "Clients\ArtBrew\Tool\ArtBrew.Tool.csproj", "{BB974E80-C6A7-4673-B78A-6B4BCB361D08}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "Upward.Tool", "Clients\Upward\Upward.Tool\Upward.Tool.csproj", "{F9A5B686-1B88-4BD2-94E2-C555D4FE903B}"
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "Graeters", "Graeters", "{269E00F9-CB4B-4661-A121-F971B7D02B43}"
	ProjectSection(SolutionItems) = preProject
		Clients\Graeters\README.txt = Clients\Graeters\README.txt
	EndProjectSection
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "Scripts", "Scripts", "{9E28EBB5-9104-4A86-9A75-14850BF7EAA8}"
	ProjectSection(SolutionItems) = preProject
		Clients\Graeters\Scripts\settings.ini = Clients\Graeters\Scripts\settings.ini
		Clients\Graeters\Scripts\export-punches.sh = Clients\Graeters\Scripts\export-punches.sh
		Clients\Graeters\Scripts\cache-refresh.sh = Clients\Graeters\Scripts\cache-refresh.sh
		Clients\Graeters\Scripts\cache-changes.sh = Clients\Graeters\Scripts\cache-changes.sh
		Clients\Graeters\Scripts\init-system.sh = Clients\Graeters\Scripts\init-system.sh
		Clients\Graeters\Scripts\sync-pto.sh = Clients\Graeters\Scripts\sync-pto.sh
		Clients\Graeters\Scripts\sync-employees.sh = Clients\Graeters\Scripts\sync-employees.sh
		Clients\Graeters\Scripts\export-punches.service = Clients\Graeters\Scripts\export-punches.service
		Clients\Graeters\Scripts\export-punches.timer = Clients\Graeters\Scripts\export-punches.timer
		Clients\Graeters\Scripts\timer-test.sh = Clients\Graeters\Scripts\timer-test.sh
	EndProjectSection
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "DataCentral.Tool", "DataCentral.Tool\DataCentral.Tool.csproj", "{D90D5BC7-3F2B-444A-8067-0D2025602F34}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "Graeters.Tool", "Clients\Graeters\Graeters.Tool\Graeters.Tool.csproj", "{4CA74DC9-3C2C-41F2-A4E3-48710D6EA94D}"
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "SlimChicken-Corp", "SlimChicken-Corp", "{D5D7CC65-81D6-4470-86C1-B15E94613776}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "CrunchTime.Tool", "CrunchTime.Tool\CrunchTime.Tool.csproj", "{7DA9BA61-6BF9-43FE-A3A7-BAC9B08AA7DF}"
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "Slim-FourJays", "Slim-FourJays", "{F4D145EE-2988-4279-8AFF-9DC848D86853}"
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "Scripts", "Scripts", "{258B5F80-3D7D-4E9A-9875-B9CA6DFE577F}"
	ProjectSection(SolutionItems) = preProject
		Clients\SlimChicken-FourJays\Scripts\README.txt = Clients\SlimChicken-FourJays\Scripts\README.txt
		Clients\SlimChicken-FourJays\Scripts\cache-changes.sh = Clients\SlimChicken-FourJays\Scripts\cache-changes.sh
		Clients\SlimChicken-FourJays\Scripts\cache-refresh.sh = Clients\SlimChicken-FourJays\Scripts\cache-refresh.sh
		Clients\SlimChicken-FourJays\Scripts\export-punches.sh = Clients\SlimChicken-FourJays\Scripts\export-punches.sh
		Clients\SlimChicken-FourJays\Scripts\init-system.sh = Clients\SlimChicken-FourJays\Scripts\init-system.sh
		Clients\SlimChicken-FourJays\Scripts\settings.ini = Clients\SlimChicken-FourJays\Scripts\settings.ini
		Clients\SlimChicken-FourJays\Scripts\locations.sh = Clients\SlimChicken-FourJays\Scripts\locations.sh
		Clients\SlimChicken-FourJays\Scripts\stores.csv = Clients\SlimChicken-FourJays\Scripts\stores.csv
		Clients\SlimChicken-FourJays\Scripts\sync-ct-employees.service = Clients\SlimChicken-FourJays\Scripts\sync-ct-employees.service
		Clients\SlimChicken-FourJays\Scripts\sync-ct-employees.sh = Clients\SlimChicken-FourJays\Scripts\sync-ct-employees.sh
		Clients\SlimChicken-FourJays\Scripts\sync-ct-employees.timer = Clients\SlimChicken-FourJays\Scripts\sync-ct-employees.timer
		Clients\SlimChicken-FourJays\Scripts\sync-aloha-employees.service = Clients\SlimChicken-FourJays\Scripts\sync-aloha-employees.service
		Clients\SlimChicken-FourJays\Scripts\sync-aloha-employees.sh = Clients\SlimChicken-FourJays\Scripts\sync-aloha-employees.sh
		Clients\SlimChicken-FourJays\Scripts\sync-aloha-employees.timer = Clients\SlimChicken-FourJays\Scripts\sync-aloha-employees.timer
		Clients\SlimChicken-FourJays\Scripts\fix-employees-with-eqy.sh = Clients\SlimChicken-FourJays\Scripts\fix-employees-with-eqy.sh
	EndProjectSection
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "Aloha.Tool", "Aloha.Tool\Aloha.Tool.csproj", "{2292032F-E21D-4848-A57C-9FFA4CB74990}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "FourJays.Tool", "Clients\SlimChicken-FourJays\FourJays.Tool\FourJays.Tool.csproj", "{E4627DA6-06A3-461E-95A5-B4C632AEACA5}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "SlimChicken.Tool", "Clients\SlimChicken-Corp\SlimChicken.Tool\SlimChicken.Tool.csproj", "{6E6FFCF1-6358-4F59-84F8-24E30B221647}"
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "Scripts", "Scripts", "{6DF29283-2A11-4010-8F31-27AD0A76BF5B}"
	ProjectSection(SolutionItems) = preProject
		Clients\SlimChicken-Corp\Scripts\cache-changes.service = Clients\SlimChicken-Corp\Scripts\cache-changes.service
		Clients\SlimChicken-Corp\Scripts\export-punches.sh = Clients\SlimChicken-Corp\Scripts\export-punches.sh
		Clients\SlimChicken-Corp\Scripts\init-system.sh = Clients\SlimChicken-Corp\Scripts\init-system.sh
		Clients\SlimChicken-Corp\Scripts\locations.sh = Clients\SlimChicken-Corp\Scripts\locations.sh
		Clients\SlimChicken-Corp\Scripts\settings.ini = Clients\SlimChicken-Corp\Scripts\settings.ini
		Clients\SlimChicken-Corp\Scripts\stores.csv = Clients\SlimChicken-Corp\Scripts\stores.csv
		Clients\SlimChicken-Corp\Scripts\sync-ct-employees.service = Clients\SlimChicken-Corp\Scripts\sync-ct-employees.service
		Clients\SlimChicken-Corp\Scripts\sync-ct-employees.sh = Clients\SlimChicken-Corp\Scripts\sync-ct-employees.sh
		Clients\SlimChicken-Corp\Scripts\sync-ct-employees.timer = Clients\SlimChicken-Corp\Scripts\sync-ct-employees.timer
		Clients\SlimChicken-Corp\Scripts\sync-aloha-employees.service = Clients\SlimChicken-Corp\Scripts\sync-aloha-employees.service
		Clients\SlimChicken-Corp\Scripts\sync-aloha-employees.sh = Clients\SlimChicken-Corp\Scripts\sync-aloha-employees.sh
		Clients\SlimChicken-Corp\Scripts\sync-aloha-employees.timer = Clients\SlimChicken-Corp\Scripts\sync-aloha-employees.timer
	EndProjectSection
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "Ap-Group", "Ap-Group", "{0DCACA5C-0286-453B-A8AB-573164726ECB}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "ApGroup.Tool", "Clients\Ap-Group\ApGroup.Tool\ApGroup.Tool.csproj", "{B22E056E-3139-4D1E-80D8-5E3ABDC7AA7A}"
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "Scripts", "Scripts", "{FEBD1B47-8312-4C96-81C8-5F25793E7C4D}"
	ProjectSection(SolutionItems) = preProject
		Clients\Ap-Group\Scripts\cache-changes.sh = Clients\Ap-Group\Scripts\cache-changes.sh
		Clients\Ap-Group\Scripts\cache-refresh.sh = Clients\Ap-Group\Scripts\cache-refresh.sh
		Clients\Ap-Group\Scripts\export-punches.sh = Clients\Ap-Group\Scripts\export-punches.sh
		Clients\Ap-Group\Scripts\sync-employees.sh = Clients\Ap-Group\Scripts\sync-employees.sh
		Clients\Ap-Group\Scripts\settings.ini = Clients\Ap-Group\Scripts\settings.ini
		Clients\Ap-Group\Scripts\init-system.sh = Clients\Ap-Group\Scripts\init-system.sh
	EndProjectSection
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "Common", "Common", "{B9C53464-81F7-4BB8-9C6E-516401B87AD8}"
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "Scripts", "Scripts", "{5FE7F238-C26E-4866-BA49-E9CB910E0853}"
	ProjectSection(SolutionItems) = preProject
		Clients\Common\Scripts\cache-changes.service = Clients\Common\Scripts\cache-changes.service
		Clients\Common\Scripts\cache-refresh.timer = Clients\Common\Scripts\cache-refresh.timer
		Clients\Common\Scripts\cache-changes.timer = Clients\Common\Scripts\cache-changes.timer
		Clients\Common\Scripts\cache-refresh.service = Clients\Common\Scripts\cache-refresh.service
		Clients\Common\Scripts\export-punches.service = Clients\Common\Scripts\export-punches.service
		Clients\Common\Scripts\export-punches.timer = Clients\Common\Scripts\export-punches.timer
		Clients\Common\Scripts\sync-employees.service = Clients\Common\Scripts\sync-employees.service
		Clients\Common\Scripts\sync-employees.timer = Clients\Common\Scripts\sync-employees.timer
		weekly-cleanup.sh = weekly-cleanup.sh
	EndProjectSection
EndProject
Global
	GlobalSection(SolutionConfigurationPlatforms) = preSolution
		CoreOnly|Any CPU = CoreOnly|Any CPU
		Debug|Any CPU = Debug|Any CPU
		Release|Any CPU = Release|Any CPU
	EndGlobalSection
	GlobalSection(ProjectConfigurationPlatforms) = postSolution
		{FE0BD7B5-630B-4E25-A6D5-AE91350B9BB9}.CoreOnly|Any CPU.ActiveCfg = CoreOnly|Any CPU
		{FE0BD7B5-630B-4E25-A6D5-AE91350B9BB9}.CoreOnly|Any CPU.Build.0 = CoreOnly|Any CPU
		{FE0BD7B5-630B-4E25-A6D5-AE91350B9BB9}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{FE0BD7B5-630B-4E25-A6D5-AE91350B9BB9}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{FE0BD7B5-630B-4E25-A6D5-AE91350B9BB9}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{FE0BD7B5-630B-4E25-A6D5-AE91350B9BB9}.Release|Any CPU.Build.0 = Release|Any CPU
		{42B8CB45-3DCC-45C4-9A05-77ABB8833F98}.CoreOnly|Any CPU.ActiveCfg = CoreOnly|Any CPU
		{42B8CB45-3DCC-45C4-9A05-77ABB8833F98}.CoreOnly|Any CPU.Build.0 = CoreOnly|Any CPU
		{42B8CB45-3DCC-45C4-9A05-77ABB8833F98}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{42B8CB45-3DCC-45C4-9A05-77ABB8833F98}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{42B8CB45-3DCC-45C4-9A05-77ABB8833F98}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{42B8CB45-3DCC-45C4-9A05-77ABB8833F98}.Release|Any CPU.Build.0 = Release|Any CPU
		{DD2C22E1-0BEA-4C1C-A9C0-1E0F2ACBE58A}.CoreOnly|Any CPU.ActiveCfg = CoreOnly|Any CPU
		{DD2C22E1-0BEA-4C1C-A9C0-1E0F2ACBE58A}.CoreOnly|Any CPU.Build.0 = CoreOnly|Any CPU
		{DD2C22E1-0BEA-4C1C-A9C0-1E0F2ACBE58A}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{DD2C22E1-0BEA-4C1C-A9C0-1E0F2ACBE58A}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{DD2C22E1-0BEA-4C1C-A9C0-1E0F2ACBE58A}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{DD2C22E1-0BEA-4C1C-A9C0-1E0F2ACBE58A}.Release|Any CPU.Build.0 = Release|Any CPU
		{5B221B45-E2A9-4733-A396-A9F1B4248D16}.CoreOnly|Any CPU.ActiveCfg = CoreOnly|Any CPU
		{5B221B45-E2A9-4733-A396-A9F1B4248D16}.CoreOnly|Any CPU.Build.0 = CoreOnly|Any CPU
		{5B221B45-E2A9-4733-A396-A9F1B4248D16}.Debug|Any CPU.ActiveCfg = Release|Any CPU
		{5B221B45-E2A9-4733-A396-A9F1B4248D16}.Debug|Any CPU.Build.0 = Release|Any CPU
		{5B221B45-E2A9-4733-A396-A9F1B4248D16}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{5B221B45-E2A9-4733-A396-A9F1B4248D16}.Release|Any CPU.Build.0 = Release|Any CPU
		{B77CEF3B-B0BE-44E3-81DA-095F4D900BAD}.CoreOnly|Any CPU.ActiveCfg = CoreOnly|Any CPU
		{B77CEF3B-B0BE-44E3-81DA-095F4D900BAD}.CoreOnly|Any CPU.Build.0 = CoreOnly|Any CPU
		{B77CEF3B-B0BE-44E3-81DA-095F4D900BAD}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{B77CEF3B-B0BE-44E3-81DA-095F4D900BAD}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{B77CEF3B-B0BE-44E3-81DA-095F4D900BAD}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{B77CEF3B-B0BE-44E3-81DA-095F4D900BAD}.Release|Any CPU.Build.0 = Release|Any CPU
		{DF5DD962-B76D-43C3-BE2F-A17680A4852D}.CoreOnly|Any CPU.ActiveCfg = Debug|Any CPU
		{DF5DD962-B76D-43C3-BE2F-A17680A4852D}.Debug|Any CPU.ActiveCfg = Release|Any CPU
		{DF5DD962-B76D-43C3-BE2F-A17680A4852D}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{B282EB1A-8EBB-4F4B-BA46-0F4C5114F210}.CoreOnly|Any CPU.ActiveCfg = Debug|Any CPU
		{B282EB1A-8EBB-4F4B-BA46-0F4C5114F210}.CoreOnly|Any CPU.Build.0 = Debug|Any CPU
		{B282EB1A-8EBB-4F4B-BA46-0F4C5114F210}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{B282EB1A-8EBB-4F4B-BA46-0F4C5114F210}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{B282EB1A-8EBB-4F4B-BA46-0F4C5114F210}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{B282EB1A-8EBB-4F4B-BA46-0F4C5114F210}.Release|Any CPU.Build.0 = Release|Any CPU
		{7BA36281-80F8-4FE3-B5D5-A60588808872}.CoreOnly|Any CPU.ActiveCfg = Debug|Any CPU
		{7BA36281-80F8-4FE3-B5D5-A60588808872}.CoreOnly|Any CPU.Build.0 = Debug|Any CPU
		{7BA36281-80F8-4FE3-B5D5-A60588808872}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{7BA36281-80F8-4FE3-B5D5-A60588808872}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{7BA36281-80F8-4FE3-B5D5-A60588808872}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{7BA36281-80F8-4FE3-B5D5-A60588808872}.Release|Any CPU.Build.0 = Release|Any CPU
		{2E40931F-09FA-471B-8EFA-DF148367984A}.CoreOnly|Any CPU.ActiveCfg = CoreOnly|Any CPU
		{2E40931F-09FA-471B-8EFA-DF148367984A}.CoreOnly|Any CPU.Build.0 = CoreOnly|Any CPU
		{2E40931F-09FA-471B-8EFA-DF148367984A}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{2E40931F-09FA-471B-8EFA-DF148367984A}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{2E40931F-09FA-471B-8EFA-DF148367984A}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{2E40931F-09FA-471B-8EFA-DF148367984A}.Release|Any CPU.Build.0 = Release|Any CPU
		{E95694D1-93A5-4033-95EE-A8DD1FD2D531}.CoreOnly|Any CPU.ActiveCfg = Debug|Any CPU
		{E95694D1-93A5-4033-95EE-A8DD1FD2D531}.CoreOnly|Any CPU.Build.0 = Debug|Any CPU
		{E95694D1-93A5-4033-95EE-A8DD1FD2D531}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{E95694D1-93A5-4033-95EE-A8DD1FD2D531}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{E95694D1-93A5-4033-95EE-A8DD1FD2D531}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{E95694D1-93A5-4033-95EE-A8DD1FD2D531}.Release|Any CPU.Build.0 = Release|Any CPU
		{FE43DF1D-09F9-4DCB-A4E6-864482949554}.CoreOnly|Any CPU.ActiveCfg = CoreOnly|Any CPU
		{FE43DF1D-09F9-4DCB-A4E6-864482949554}.CoreOnly|Any CPU.Build.0 = CoreOnly|Any CPU
		{FE43DF1D-09F9-4DCB-A4E6-864482949554}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{FE43DF1D-09F9-4DCB-A4E6-864482949554}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{FE43DF1D-09F9-4DCB-A4E6-864482949554}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{FE43DF1D-09F9-4DCB-A4E6-864482949554}.Release|Any CPU.Build.0 = Release|Any CPU
		{C9D5F93E-15F1-4BCD-BF65-8A4BA1D13799}.CoreOnly|Any CPU.ActiveCfg = Debug|Any CPU
		{C9D5F93E-15F1-4BCD-BF65-8A4BA1D13799}.CoreOnly|Any CPU.Build.0 = Debug|Any CPU
		{C9D5F93E-15F1-4BCD-BF65-8A4BA1D13799}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{C9D5F93E-15F1-4BCD-BF65-8A4BA1D13799}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{C9D5F93E-15F1-4BCD-BF65-8A4BA1D13799}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{C9D5F93E-15F1-4BCD-BF65-8A4BA1D13799}.Release|Any CPU.Build.0 = Release|Any CPU
		{A9D9ACAF-6791-4413-9391-5064CDAD9342}.CoreOnly|Any CPU.ActiveCfg = Debug|Any CPU
		{A9D9ACAF-6791-4413-9391-5064CDAD9342}.CoreOnly|Any CPU.Build.0 = Debug|Any CPU
		{A9D9ACAF-6791-4413-9391-5064CDAD9342}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{A9D9ACAF-6791-4413-9391-5064CDAD9342}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{A9D9ACAF-6791-4413-9391-5064CDAD9342}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{A9D9ACAF-6791-4413-9391-5064CDAD9342}.Release|Any CPU.Build.0 = Release|Any CPU
		{41CB72E7-975A-4F0E-A322-D48D5E33FDD4}.CoreOnly|Any CPU.ActiveCfg = CoreOnly|Any CPU
		{41CB72E7-975A-4F0E-A322-D48D5E33FDD4}.CoreOnly|Any CPU.Build.0 = CoreOnly|Any CPU
		{41CB72E7-975A-4F0E-A322-D48D5E33FDD4}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{41CB72E7-975A-4F0E-A322-D48D5E33FDD4}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{41CB72E7-975A-4F0E-A322-D48D5E33FDD4}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{41CB72E7-975A-4F0E-A322-D48D5E33FDD4}.Release|Any CPU.Build.0 = Release|Any CPU
		{BB974E80-C6A7-4673-B78A-6B4BCB361D08}.CoreOnly|Any CPU.ActiveCfg = CoreOnly|Any CPU
		{BB974E80-C6A7-4673-B78A-6B4BCB361D08}.CoreOnly|Any CPU.Build.0 = CoreOnly|Any CPU
		{BB974E80-C6A7-4673-B78A-6B4BCB361D08}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{BB974E80-C6A7-4673-B78A-6B4BCB361D08}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{BB974E80-C6A7-4673-B78A-6B4BCB361D08}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{BB974E80-C6A7-4673-B78A-6B4BCB361D08}.Release|Any CPU.Build.0 = Release|Any CPU
		{F9A5B686-1B88-4BD2-94E2-C555D4FE903B}.CoreOnly|Any CPU.ActiveCfg = Debug|Any CPU
		{F9A5B686-1B88-4BD2-94E2-C555D4FE903B}.CoreOnly|Any CPU.Build.0 = Debug|Any CPU
		{F9A5B686-1B88-4BD2-94E2-C555D4FE903B}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{F9A5B686-1B88-4BD2-94E2-C555D4FE903B}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{F9A5B686-1B88-4BD2-94E2-C555D4FE903B}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{F9A5B686-1B88-4BD2-94E2-C555D4FE903B}.Release|Any CPU.Build.0 = Release|Any CPU
		{D90D5BC7-3F2B-444A-8067-0D2025602F34}.CoreOnly|Any CPU.ActiveCfg = CoreOnly|Any CPU
		{D90D5BC7-3F2B-444A-8067-0D2025602F34}.CoreOnly|Any CPU.Build.0 = CoreOnly|Any CPU
		{D90D5BC7-3F2B-444A-8067-0D2025602F34}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{D90D5BC7-3F2B-444A-8067-0D2025602F34}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{D90D5BC7-3F2B-444A-8067-0D2025602F34}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{D90D5BC7-3F2B-444A-8067-0D2025602F34}.Release|Any CPU.Build.0 = Release|Any CPU
		{4CA74DC9-3C2C-41F2-A4E3-48710D6EA94D}.CoreOnly|Any CPU.ActiveCfg = Debug|Any CPU
		{4CA74DC9-3C2C-41F2-A4E3-48710D6EA94D}.CoreOnly|Any CPU.Build.0 = Debug|Any CPU
		{4CA74DC9-3C2C-41F2-A4E3-48710D6EA94D}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{4CA74DC9-3C2C-41F2-A4E3-48710D6EA94D}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{4CA74DC9-3C2C-41F2-A4E3-48710D6EA94D}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{4CA74DC9-3C2C-41F2-A4E3-48710D6EA94D}.Release|Any CPU.Build.0 = Release|Any CPU
		{7DA9BA61-6BF9-43FE-A3A7-BAC9B08AA7DF}.CoreOnly|Any CPU.ActiveCfg = CoreOnly|Any CPU
		{7DA9BA61-6BF9-43FE-A3A7-BAC9B08AA7DF}.CoreOnly|Any CPU.Build.0 = CoreOnly|Any CPU
		{7DA9BA61-6BF9-43FE-A3A7-BAC9B08AA7DF}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{7DA9BA61-6BF9-43FE-A3A7-BAC9B08AA7DF}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{7DA9BA61-6BF9-43FE-A3A7-BAC9B08AA7DF}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{7DA9BA61-6BF9-43FE-A3A7-BAC9B08AA7DF}.Release|Any CPU.Build.0 = Release|Any CPU
		{2292032F-E21D-4848-A57C-9FFA4CB74990}.CoreOnly|Any CPU.ActiveCfg = Debug|Any CPU
		{2292032F-E21D-4848-A57C-9FFA4CB74990}.CoreOnly|Any CPU.Build.0 = Debug|Any CPU
		{2292032F-E21D-4848-A57C-9FFA4CB74990}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{2292032F-E21D-4848-A57C-9FFA4CB74990}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{2292032F-E21D-4848-A57C-9FFA4CB74990}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{2292032F-E21D-4848-A57C-9FFA4CB74990}.Release|Any CPU.Build.0 = Release|Any CPU
		{E4627DA6-06A3-461E-95A5-B4C632AEACA5}.CoreOnly|Any CPU.ActiveCfg = Debug|Any CPU
		{E4627DA6-06A3-461E-95A5-B4C632AEACA5}.CoreOnly|Any CPU.Build.0 = Debug|Any CPU
		{E4627DA6-06A3-461E-95A5-B4C632AEACA5}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{E4627DA6-06A3-461E-95A5-B4C632AEACA5}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{E4627DA6-06A3-461E-95A5-B4C632AEACA5}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{E4627DA6-06A3-461E-95A5-B4C632AEACA5}.Release|Any CPU.Build.0 = Release|Any CPU
		{6E6FFCF1-6358-4F59-84F8-24E30B221647}.CoreOnly|Any CPU.ActiveCfg = Debug|Any CPU
		{6E6FFCF1-6358-4F59-84F8-24E30B221647}.CoreOnly|Any CPU.Build.0 = Debug|Any CPU
		{6E6FFCF1-6358-4F59-84F8-24E30B221647}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{6E6FFCF1-6358-4F59-84F8-24E30B221647}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{6E6FFCF1-6358-4F59-84F8-24E30B221647}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{6E6FFCF1-6358-4F59-84F8-24E30B221647}.Release|Any CPU.Build.0 = Release|Any CPU
		{B22E056E-3139-4D1E-80D8-5E3ABDC7AA7A}.CoreOnly|Any CPU.ActiveCfg = Debug|Any CPU
		{B22E056E-3139-4D1E-80D8-5E3ABDC7AA7A}.CoreOnly|Any CPU.Build.0 = Debug|Any CPU
		{B22E056E-3139-4D1E-80D8-5E3ABDC7AA7A}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{B22E056E-3139-4D1E-80D8-5E3ABDC7AA7A}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{B22E056E-3139-4D1E-80D8-5E3ABDC7AA7A}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{B22E056E-3139-4D1E-80D8-5E3ABDC7AA7A}.Release|Any CPU.Build.0 = Release|Any CPU
	EndGlobalSection
	GlobalSection(SolutionProperties) = preSolution
		HideSolutionNode = FALSE
	EndGlobalSection
	GlobalSection(NestedProjects) = preSolution
		{53E441AD-E906-4886-B622-7DDEE62E74B0} = {FFFDD2A0-11E5-4A4F-A923-6B6C7B1FF447}
		{C8C2F454-B14F-4B07-BB87-2048DA2192D3} = {66C5AF00-AEDA-442D-B4DC-10D31E3FEFB2}
		{144B4FBA-0A39-4375-809F-D4718CB37C08} = {FFFDD2A0-11E5-4A4F-A923-6B6C7B1FF447}
		{EC5F50C7-8E52-401F-A6F1-404C6790AB29} = {FFFDD2A0-11E5-4A4F-A923-6B6C7B1FF447}
		{41CB72E7-975A-4F0E-A322-D48D5E33FDD4} = {EC5F50C7-8E52-401F-A6F1-404C6790AB29}
		{D2D5D98A-2FBE-4279-B41D-11D14A8B748B} = {EC5F50C7-8E52-401F-A6F1-404C6790AB29}
		{FC92ED14-8957-40CE-8A35-45D004D47B49} = {53E441AD-E906-4886-B622-7DDEE62E74B0}
		{9C208364-752C-41C1-93B3-02190A304861} = {144B4FBA-0A39-4375-809F-D4718CB37C08}
		{BB974E80-C6A7-4673-B78A-6B4BCB361D08} = {53E441AD-E906-4886-B622-7DDEE62E74B0}
		{F9A5B686-1B88-4BD2-94E2-C555D4FE903B} = {144B4FBA-0A39-4375-809F-D4718CB37C08}
		{269E00F9-CB4B-4661-A121-F971B7D02B43} = {FFFDD2A0-11E5-4A4F-A923-6B6C7B1FF447}
		{9E28EBB5-9104-4A86-9A75-14850BF7EAA8} = {269E00F9-CB4B-4661-A121-F971B7D02B43}
		{4CA74DC9-3C2C-41F2-A4E3-48710D6EA94D} = {269E00F9-CB4B-4661-A121-F971B7D02B43}
		{D5D7CC65-81D6-4470-86C1-B15E94613776} = {FFFDD2A0-11E5-4A4F-A923-6B6C7B1FF447}
		{F4D145EE-2988-4279-8AFF-9DC848D86853} = {FFFDD2A0-11E5-4A4F-A923-6B6C7B1FF447}
		{258B5F80-3D7D-4E9A-9875-B9CA6DFE577F} = {F4D145EE-2988-4279-8AFF-9DC848D86853}
		{E4627DA6-06A3-461E-95A5-B4C632AEACA5} = {F4D145EE-2988-4279-8AFF-9DC848D86853}
		{6E6FFCF1-6358-4F59-84F8-24E30B221647} = {D5D7CC65-81D6-4470-86C1-B15E94613776}
		{6DF29283-2A11-4010-8F31-27AD0A76BF5B} = {D5D7CC65-81D6-4470-86C1-B15E94613776}
		{0DCACA5C-0286-453B-A8AB-573164726ECB} = {FFFDD2A0-11E5-4A4F-A923-6B6C7B1FF447}
		{B22E056E-3139-4D1E-80D8-5E3ABDC7AA7A} = {0DCACA5C-0286-453B-A8AB-573164726ECB}
		{FEBD1B47-8312-4C96-81C8-5F25793E7C4D} = {0DCACA5C-0286-453B-A8AB-573164726ECB}
		{B9C53464-81F7-4BB8-9C6E-516401B87AD8} = {FFFDD2A0-11E5-4A4F-A923-6B6C7B1FF447}
		{5FE7F238-C26E-4866-BA49-E9CB910E0853} = {B9C53464-81F7-4BB8-9C6E-516401B87AD8}
	EndGlobalSection
	GlobalSection(ExtensibilityGlobals) = postSolution
		SolutionGuid = {9261BD53-0EC2-4781-84C8-08F11B717F2D}
	EndGlobalSection
	GlobalSection(RiderSharedRunConfigurations) = postSolution
		File = Publishing\Thompson.run.xml~
	EndGlobalSection
EndGlobal
