PUBLISH=dotnet publish --self-contained=false
PUBLISH_LINUX=$(PUBLISH) -c Debug --runtime linux-x64
PUBLISH_RELEASE=dotnet publish -c Release

win8:
	$(MAKE) OPTS="--runtime win-x64" net8

linux8:
	$(PUBLISH_LINUX) -f net8.0 -o $(DEST)

mac:
	$(MAKE) OPTS="--runtime osx-arm64" net8

zip:
	$(PUBLISH) --runtime win-x64 -o $(DEST)

linux:
	$(PUBLISH) --runtime linux-x64 -o $(DEST)

win9:
	$(PUBLISH) -f net8.0 --runtime win-x64 -o $(DEST)

linux9:
	$(PUBLISH_LINUX) -f net8.0 -o $(DEST)

net9:
	$(PUBLISH_RELEASE) -f net9.0 -o $(DEST)

net8:
	$(PUBLISH_RELEASE) -f net8.0 $(OPTS) -o $(DEST)

clean:
	rm -rf obj/ bin/
