using System;
using System.Text.Json;
using System.Text.Json.Serialization;

namespace Paycom2.Tool;

public class ZeroDateTimeConverter : JsonConverter<DateTime?>
{
    public override DateTime? Read(ref Utf8JsonReader reader, Type typeToConvert, JsonSerializerOptions options)
    {
        string s = reader.GetString();
        if (s == "0000-00-00")
        {
            return null;
        }
        return JsonSerializer.Deserialize<DateTime>(s, options);
    }

    public override void Write(Utf8JsonWriter writer, DateTime? value, JsonSerializerOptions options)
    {
        if (value == null) writer.WriteStringValue("0000-00-00");
        else
        {
            var s = JsonSerializer.Serialize<DateTime>(value.Value, options);
            writer.WriteStringValue(s);
        }
    }
}
