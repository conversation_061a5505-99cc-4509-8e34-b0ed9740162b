﻿using System;
using System.Collections.Generic;
using Serilog;
using System.Text.Json;
using Payroll.Shared;

namespace Paycom2.Tool
{
    class LaborCommand : BaseCommand
    {
        public void List(List<string> args)
        {
            try
            {
                using (var paycomService = new PaycomRestService())
                {
                    var allocations = paycomService.GetLaborAllocations().Result;
                    string formattedJson = JsonSerializer.Serialize(allocations, Json.DefaultSerializerOutputStyle);
                    Console.WriteLine(formattedJson);
                }
            }
            catch (Exception e)
            {
                Log.Logger.Error(e.Message);
                Log.Logger.Error(e.StackTrace);
            }
        }

        public void View(List<string> args)
        {
            if (args == null || args.Count < 2)
            {
                Console.WriteLine("Usage: Paycom2.Tool.exe labor view <laborid> <divcode>");
                return;
            }

            var laborid = args[0];
            var divcode = args[1];

            try
            {
                using (var paycomService = new PaycomRestService())
                {
                    var locations = paycomService.GetLaborAllocationDetails(laborid, divcode).Result;
                    string formattedJson = JsonSerializer.Serialize(locations, Json.DefaultSerializerOutputStyle);
                    Console.WriteLine(formattedJson); 
                }
            }
            catch (Exception e)
            {
                Log.Logger.Error(e.Message);
                Log.Logger.Error(e.StackTrace);
            }
        }
    }
}
