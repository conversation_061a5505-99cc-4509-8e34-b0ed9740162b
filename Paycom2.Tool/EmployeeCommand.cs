﻿using Paycom2.Shared;
using Payroll.Shared;
using Serilog;
using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.Linq;
using System.Text.Json;

namespace Paycom2.Tool
{
    class EmployeeCommand : BaseCommand
    {
        public void List(List<string> args)
        {
            var filterLocation = "all";
            if (args != null && args.Count > 0)
            {
                filterLocation = args[0];
            }

            try
            {
                using (var paycomService = new PaycomRestService())
                {
                    var employees = paycomService.GetActiveEmployeeIds().Result;
                    
                    Console.WriteLine("Id   \tClockSeq       \tName                     \tLocation");
                    foreach (EmployeeIdentifier employee in employees)
                    {
                        var cachedEmp = CacheService.FetchRecord<Employee>(Config.EMPLOYEES_CACHE_COLLECTION, employee.EECode);
                        var location = cachedEmp == null ? "" : cachedEmp.PrimaryWorkLocation;
                        var fullName = $"{employee.FirstName} {employee.LastName}".PadRight(25);

                        if (filterLocation == "all" || filterLocation == location)
                            Console.WriteLine($"{employee.EECode}\t{employee.ClockSeq.PadRight(15)}\t{fullName}\t{location}");
                    }
                    
                }
            }
            catch (Exception e)
            {
                Log.Logger.Error(e.Message);
                Log.Logger.Error(e.StackTrace);
            }
        }

        public void Terms(List<string> args)
        {
            try
            {
                using (var paycomService = new PaycomRestService())
                {
                    var employees = paycomService.GetInactiveEmployeeIds().Result;

                    Console.WriteLine("Id   \tClockSeq       \tName");
                    foreach (var employee in employees)
                    {
                        Console.WriteLine($"{employee.EECode}\t{employee.ClockSeq.PadRight(15)}\t{employee.FirstName} {employee.LastName}");
                    }

                }
            }
            catch (Exception e)
            {
                Log.Logger.Error(e.Message);
                Log.Logger.Error(e.StackTrace);
            }
        }

        public void Update(List<string> args)
        {
            if (args == null || args.Count < 3)
            {
                Console.WriteLine("Usage: Paycom2.Tool.exe employee update <eecode> <fieldname> <value>");
                return;
            }

            var eecode = args[0];
            var fieldname = args[1];
            var value = args[2];

            try
            {
                using (var paycomService = new PaycomRestService())
                {
                    _ = paycomService.PatchEmployeeAsync(eecode, fieldname, value).Result;
                }
            }
            catch (Exception e)
            {
                Log.Logger.Error(e.Message);
                Log.Logger.Error(e.StackTrace);
            }
        }

        private void ProcessUpdateCommand(List<string> args, Func<PaycomRestService, string, string, bool> func)
        {
            var eecode = args[0];
            string newVal = args[1];
            Log.Logger.Information("Updating employee {0} with {1}", eecode, newVal);

            // basically another safety measure
            var dryRun = !(args != null && args.Count > 2 && args[2] == "doit");
            var executionMode = "execute";  //Config.ExecutionMode();

            if (executionMode != "execute")
            {
                dryRun = true;
                Log.Logger.Information("Paycom2.Tool Mode={mode}, DryRun={dr}", executionMode, dryRun);
            }

            try
            {
                using (PaycomRestService paycomService = new PaycomRestService())
                {
                    if (!dryRun)
                    {
                        var rc = func(paycomService, eecode, args[1]);
                        Log.Logger.Information("{0} update employee '{1}', new value: '{2}'", rc ? "Successful" : "Failed", eecode, newVal);
                    }
                    else
                        Log.Logger.Information("{0} update employee '{1}', new value: '{2}'", "(DryRun) Wanted to", eecode, newVal);

                }
            }
            catch (Exception e)
            {
                Log.Logger.Fatal(e.Message);
                Log.Logger.Fatal(e.StackTrace);
            }
        }

        public void Setclock(List<string> args)
        {
            if (args == null || args.Count < 2)
            {
                Console.WriteLine("Usage: Paycom2.Tool.exe employee setclock <eecode> <clock-sequence>");
                return;
            }

            ProcessUpdateCommand(args, (paycomService, eecode, clockSeq) =>
            {
                return paycomService.UpdateClockSeqAsync(eecode, clockSeq).Result;
            });
        }

        public void Setbadge(List<string> args)
        {
            if (args == null || args.Count < 2)
            {
                Console.WriteLine("Usage: Paycom2.Tool.exe employee setbadge <eecode> <badge-no>");
                return;
            }

            ProcessUpdateCommand(args, (paycomService, eecode, badgeNo) =>
            {
                return paycomService.UpdateBadgeNoAsync(eecode, badgeNo).Result;
            });
        }

        public void Setmail(List<string> args)
        {
            if (args == null || args.Count < 2)
            {
                Console.WriteLine("Usage: Paycom2.Tool.exe employee setmail <eecode> <email-addr>");
                return;
            }

            ProcessUpdateCommand(args, (paycomService, eecode, emailAddr) =>
            {
                return paycomService.UpdateEmailAddressAsync(eecode, emailAddr).Result;
            });
        }

        public int View(List<string> args)
        {
            if (args == null || args.Count < 1)
            {
                Console.WriteLine("Usage: Paycom2.Tool.exe employee view <eecode>");
                 return 1;
            }

            var eecode = args[0];

            try
            {
                using (var paycomService = new PaycomRestService())
                {
                    var mer = paycomService.GetEmployeeWithMaximalData(eecode).Result;
                    if (mer == null)
                    {
                        Log.Logger.Fatal("Failed to find employee with eecode {0}", eecode);
                        return 1;
                    }
                    else
                    {
                        string formattedJson = JsonSerializer.Serialize(mer, Json.DefaultSerializerOutputStyle);
                        Log.Logger.Debug(formattedJson);
                    }

                    var emp = paycomService.ConvertMasterEmployeeRecordToEmployee(mer).Result;                    
                    {
                        var employees = new List<Employee>();
                        employees.Add(emp);

                        var formattedJson = System.Text.Json.JsonSerializer.Serialize(employees, Json.DefaultSerializerOutputStyle);
                        Console.WriteLine(formattedJson);
                    }

                    if (Config.IncludeRatesbyAllocation)
                    {
                        try
                        {
                            var laos = paycomService.GetLaborAllocationsForEmployee(eecode).Result;
                            Console.WriteLine(); 
                            Console.WriteLine(laos.ValueKind);
                            Console.WriteLine();
                            if (laos.ValueKind == JsonValueKind.Undefined)
                                Console.WriteLine("No labor allocations found");
                            else
                            {
                                string formattedJson = JsonSerializer.Serialize(laos, Json.DefaultSerializerOutputStyle);
                                Console.WriteLine(formattedJson);
                            }
                        }
                        catch (Exception ex)
                        {
                            Log.Logger.Error(ex.Message);
                            Log.Logger.Error(ex.StackTrace);
                        }
                    }
                }
            }
            catch (Exception e)
            {
                Log.Logger.Error(e.Message);
                Log.Logger.Error(e.StackTrace);
                Environment.ExitCode = 1;
                return 1;
            }

            return 0;
        }
    }
}
