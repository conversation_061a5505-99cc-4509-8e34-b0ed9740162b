using System;
using System.Collections;
using System.Collections.Generic;
using System.Text.Json;
using System.Threading;
using Paycom2.Shared;
using Payroll.Shared;
using Serilog;
using Paycom2.Tool;

namespace Paycom2.Tool
{
    class ImportCommand : BaseCommand
    {
        // Static lock object to prevent multiple simultaneous executions of the Punches method
        private static readonly object _punchesLock = new object();

        /// <summary>
        /// Cleans up old cached punches (older than the configured number of days) and returns the IDs of recent punches
        /// </summary>
        /// <returns>A HashSet containing the IDs of recent cached punches</returns>
        private HashSet<string> CleanupOldCachedPunches()
        {
            var now = DateTime.UtcNow;
            var cutoffDate = now.AddDays(-Config.CacheCleanupDays);
            var allCachedPunches = CacheService.FetchRecords<CachedPunch>(Config.PUNCH_CACHE_COLLECTION) ?? new List<CachedPunch>();
            var recentCachedPunchIds = new HashSet<string>();
            var punchesToRemoveFromCache = new List<string>();

            Log.Logger.Information("Checking cache for punches older than {0} days...", Config.CacheCleanupDays);
            foreach (var cachedPunch in allCachedPunches)
            {
                if (cachedPunch.ImportTimestamp < cutoffDate)
                {
                    punchesToRemoveFromCache.Add(cachedPunch.Id);
                }
                else
                {
                    recentCachedPunchIds.Add(cachedPunch.Id);
                }
            }

            Log.Logger.Information("Removing {0} old punches from cache...", punchesToRemoveFromCache.Count);
            foreach (var id in punchesToRemoveFromCache)
            {
                CacheService.RemoveRecord<CachedPunch>(Config.PUNCH_CACHE_COLLECTION, id);
            }
            Log.Logger.Information("Cache cleanup complete. {0} recent punches found.", recentCachedPunchIds.Count);

            return recentCachedPunchIds;
        }
        private void ProcessImportCommand(List<string> args, Func<PaycomRestService, Employee, MasterEmployeeRecord, bool, bool> func)
        {
            try
            {
                if (!ConsoleService.TryGetEmployeesFromInput(out var employees))
                {
                    Log.Logger.Error("Failed to parse employees list");
                    return;
                }

                if (employees == null || employees.Count == 0)
                {
                    Log.Logger.Error("Failed to load employees list");
                    return;
                }

                var dryRun = !(args != null && args.Count > 0 && args[0] == "doit");

                // basically another safety measure
                var executionMode = Config.ExecutionMode();
                if (executionMode != ExecutionMode.Execute)
                {
                    dryRun = true;
                    Log.Logger.Information("Paycom2.Tool Mode={mode}, DryRun={dr}", executionMode, dryRun);
                }

                using (var paycomService = new PaycomRestService())
                {
                    foreach (var employee in employees)
                    {
                        Log.Logger.Information("Processing {0} {1}({2}/{3})...", employee.FirstName, employee.LastName,
                            employee.Id, employee.ClockSeq);

                        MasterEmployeeRecord mer = paycomService.GetEmployeeWithMaximalData(employee.Id).Result;
                        if (mer == null)
                        {
                            Log.Logger.Fatal("Failed to load employee record with eecode {0}", employee.Id);
                            continue;
                        }

                        func(paycomService, employee, mer, dryRun);
                    }
                }
            }
            catch (Exception e)
            {
                Log.Logger.Fatal(e.Message);
                Log.Logger.Fatal(e.StackTrace);
            }
        }

        public void Clockseq(List<string> args)
        {
            ProcessImportCommand(args, (paycomService, employee, mer, dryRun) =>
            {
                if (string.IsNullOrEmpty(employee.ClockSeq))
                {
                    Log.Logger.Information("{0} - empty clockseq, skipping...", employee.Id);
                    return false;
                }

                if (employee.ClockSeq == mer.clocksequencenumber)
                {
                    Log.Logger.Debug("{0} - clockseq already matches, no update required...", employee.Id);
                    return false;
                }

                if (dryRun)
                {
                    Log.Logger.Information("{0} update clockseq for {1} to {2}", "(DryRun) Wanted to", employee.Id, employee.ClockSeq);
                    return false;
                }

                var rc = paycomService.UpdateClockSeqAsync(employee.Id, employee.ClockSeq).Result;
                Log.Logger.Information("{0} update clockseq for {1} to {2}", rc ? "Successful" : "Failed", employee.Id, employee.ClockSeq);

                return true;
            });
        }

        public void Badge(List<string> args)
        {
            ProcessImportCommand(args, (paycomService, employee, mer, dryRun) =>
            {
                if (employee.Attributes == null) return false;
                if (!employee.Attributes.ContainsKey("badge_no")) return false;
                var badge = employee.Attributes["badge_no"];

                if (string.IsNullOrEmpty(badge))
                {
                    Log.Logger.Information("{0} - empty badgeno, skipping...", employee.Id);
                    return false;
                }

                if (badge == mer.employee_badge)
                {
                    Log.Logger.Debug("{0} - badgeno already matches, no update required...", employee.Id);
                    return false;
                }

                if (dryRun)
                {
                    Log.Logger.Information("{0} update badge for {1} to {2}", "(DryRun) Wanted to", employee.Id, badge);
                    return false;
                }

                var rc = paycomService.UpdateBadgeNoAsync(employee.Id, badge).Result;
                Log.Logger.Information("{0} update badge for {1} to {2}", rc ? "Successful" : "Failed", employee.Id, badge);
                return true;
            });
        }

        public void Email(List<string> args)
        {
            ProcessImportCommand(args, (paycomService, employee, mer, dryRun) =>
            {
                if (string.IsNullOrEmpty(employee.WorkEmail))
                {
                    Log.Logger.Information("{0} - empty work email addr, skipping...", employee.Id);
                    return false;
                }

                if (employee.WorkEmail == mer.work_email)
                {
                    Log.Logger.Debug("{0} - work email already matches, no update required...", employee.Id);
                    return false;
                }

                if (dryRun)
                {
                    Log.Logger.Information(messageTemplate: "{0} update email for {1} to {2}", "(DryRun) Wanted to", employee.Id, employee.WorkEmail);
                    return false;
                }

                var rc = paycomService.UpdateEmailAddressAsync(employee.Id, employee.WorkEmail).Result;
                Log.Logger.Information("{0} update email for {1} to {2}", rc ? "Successful" : "Failed", employee.Id, employee.WorkEmail);

                return true;
            });
        }

        public void Punches(List<string> args)
        {
            bool lockAcquired = false;
            try
            {
                // Try to acquire the lock to prevent multiple simultaneous executions
                Monitor.TryEnter(_punchesLock, ref lockAcquired);
                if (!lockAcquired)
                {
                    Log.Logger.Warning("Another punch import is already in progress. Please wait for it to complete.");
                    return;
                }

                if (!ConsoleService.TryGetPunchesFromInput(out List<PunchPair> punchPairs))
                {
                    Log.Logger.Error("Failed to parse punches list");
                    return;
                }

                if (punchPairs == null)
                {
                    Log.Logger.Warning("No punch pairs detected.");
                    return;
                }

                // Clean up old cached punches and get recent punch IDs
                var recentCachedPunchIds = CleanupOldCachedPunches();

                // Capture timestamp for later use when caching successfully imported punches
                var now = DateTime.UtcNow;

                var executionMode = ExecutionMode.DryRun;
                if (args != null && args.Count > 0 && args[0] == "doit" && Config.ExecutionMode() == ExecutionMode.Execute)
                    executionMode = ExecutionMode.Execute;

                // Get import type from args if provided, otherwise use default
                string importType = args != null && args.Count > 1 ? args[1] : "default";

                Log.Logger.Information("Paycom2.Tool Mode={mode}, DryRun={dr}, Freq={freq}, ImportType={type}", 
                    Config.ExecutionMode(), executionMode, Config.PunchImportFrequency, importType);

                var lastRun = Config.GetLastPunchImportRunTime(importType);
                if (lastRun != null)
                {
                    var daysSinceLastRun = (DateTime.Now.Date - lastRun.Value.Date).TotalDays;
                    Log.Logger.Information("Last {0} punch import happened on {1}, which is {2} days ago...", 
                        importType, lastRun, daysSinceLastRun);
                }

                if (Config.IsPunchImportTooSoon(DateTime.Now, importType))
                {
                    Log.Logger.Warning("This {0} punch import is too soon, ignoring this import request.", importType);
                    Log.Logger.Information("To clear this restriction, run 'dotnet payroll/Payroll.Tool.dll stats clear'");
                    return;
                }

                using (var paycomService = new PaycomRestService())
                {
                    // Pass the set of recent cached punch IDs to the service for duplicate checking
                    var punchesById = paycomService.PunchesFromPunchPairs(punchPairs, recentCachedPunchIds);
                    string formattedJson = JsonSerializer.Serialize(punchesById, Json.DefaultSerializerOutputStyle);

                    var rc = paycomService.ImportPunchesAsync(executionMode, punchesById).Result;
                    Log.Logger.Information("{0} imported {1} punches",
                        executionMode == ExecutionMode.Execute ? "Successfully" : "Would have imported", punchesById.Count);

                    // If a developer is just testing this, then always set the last punch import time.
                    // Otherwise, only do it if we have a real successful execution.
                    if (args.Count == 0)
                        Config.SetLastPunchImportRunTime(DateTime.Now, importType);
                    else if (executionMode == ExecutionMode.Execute && rc > 0)
                    {
                        Config.SetLastPunchImportRunTime(DateTime.Now, importType);

                        // Cache successfully imported punches
                        Log.Logger.Information("Caching successfully imported punches...");
                        foreach (var punchList in punchesById.Values)
                        {
                            foreach (var punch in punchList)
                            {
                                var cachedPunch = new CachedPunch
                                {
                                    Id = CachedPunch.GenerateId(punch.EECode, punch.PunchTime, punch.PunchType),
                                    EECode = punch.EECode,
                                    PunchTime = punch.PunchTime,
                                    PunchType = punch.PunchType,
                                    ImportTimestamp = now // Use the timestamp captured at the start
                                };
                                CacheService.CacheRecord(Config.PUNCH_CACHE_COLLECTION, cachedPunch);
                            }
                        }
                        Log.Logger.Information("Successfully cached imported punches.");
                    }
                }
            }
            catch (Exception e)
            {
                Log.Logger.Fatal(e.Message);
                Log.Logger.Fatal(e.StackTrace);
            }
            finally
            {
                // Always release the lock when done, even if an exception occurred
                if (lockAcquired)
                {
                    Monitor.Exit(_punchesLock);
                }
            }
        }
    }
}
