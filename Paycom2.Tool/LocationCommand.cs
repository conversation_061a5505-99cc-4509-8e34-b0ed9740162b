﻿using System;
using System.Collections.Generic;
using Serilog;
using System.Text.Json;
using Payroll.Shared;

namespace Paycom2.Tool
{
    class LocationCommand : BaseCommand
    {
        public void Dump(List<string> args)
        {
            try
            {
                using (var paycomService = new PaycomRestService())
                {
                    var locations = paycomService.GetLocationDirectory();
                    string formattedJson = JsonSerializer.Serialize(locations, Json.DefaultSerializerOutputStyle);
                    Console.WriteLine(formattedJson); 
                }
            }
            catch (Exception e)
            {
                Log.Logger.Error(e.Message);
                Log.Logger.Error(e.StackTrace);
            }
        }

        public void List(List<string> args)
        {
            try
            {
                using (var paycomService = new PaycomRestService())
                {
                    var locations = paycomService.GetLocationDirectory();
                    foreach (var location in locations)
                    {
                        Console.WriteLine($"{location.Key}\t{location.Value.description?.PadRight(30)}\t{location.Value.address?.PadRight(25)}\t{location.Value?.city}, {location.Value?.state} {location.Value?.zipCode}");
                    }
                }
            }
            catch (Exception e)
            {
                Log.Logger.Error(e.Message);
                Log.Logger.Error(e.StackTrace);
            }
        }
    }
}
