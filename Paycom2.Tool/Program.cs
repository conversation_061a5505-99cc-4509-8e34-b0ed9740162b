using CommandLine;
using LiteDB;
using Payroll.Shared;
using Serilog;
using System;
using System.Collections.Generic;
using Paycom2.Shared;

namespace Paycom2.Tool
{
    class Program : ProgramBase<SettingCommand>
    {
        static readonly string AppVersion = "1.1.2"; // paycom skip jobs

        static int DoShowUsage()
        {
            Console.WriteLine("Usage: Paycom2.Tool.exe <command> <command-args>");
            Console.WriteLine($"  Branch: {BuildInfo.Branch}, Built: {BuildInfo.Built}, Host: {BuildInfo.Host}, Hash: {BuildInfo.Commit}");
            Console.WriteLine();

            Console.WriteLine("  where <command> is one of...");
            Console.WriteLine("   - cache [ init, new, update, terms, dump <all|cseq|term|new> ] ");
            Console.WriteLine("   - change [view, sview <eecode>");
            Console.WriteLine("   - employee [list, view, setmail, setclock, setbadge, update]");
            Console.WriteLine("   - export [changes, mer <fetch limit>, raw <fetch limit>, employees <fetch limit>] ");
            Console.WriteLine("   - hire [dump, ids, view <nhid>] ");
            Console.WriteLine("   - import [badge, clockseq, email] ");
            Console.WriteLine("   - labor [list, view <lid>] ");
            Console.WriteLine("   - set [clockseq]");

            Console.WriteLine();

            Console.WriteLine("   - location [list] ");
            Console.WriteLine("   - info - show version and tool information");
            Console.WriteLine("   - setting [list, set, get] = settings management");
            Console.WriteLine();

            Console.WriteLine("  config options:");
            Console.WriteLine("   - sensitive = [0,1] - include sensitive data in output. default is 1 (on)");
            Console.WriteLine("  - punch_import_frequency options are [unspecified, daily, weekly, biweekly, every14days]");

            Console.WriteLine();
            Console.WriteLine("Notes");
            Console.WriteLine("- The 'cache dump' command is used to export records from the cache.");
            Console.WriteLine("- The 'import' command is used to update employee information in Paycom.");
            Console.WriteLine("- The 'employee view' command is examine a Paycom employee record by employee id");
            Console.WriteLine("- All other commands are used to explore Paycom records (new hires, labor, locations, etc).");

            return 0;
        }

        public override int ShowUsage()
        {
            return DoShowUsage();
        }

        public void Help(List<string> args)
        {
            ShowUsage();
        }

        public void Info(List<string> args)
        {
            Console.WriteLine($"Paycom.Tool.exe, version: {AppVersion}, settings: {Payroll.Shared.Setting.SettingFileName()}");
            Console.WriteLine();
            Console.WriteLine($"  Settings:");
            Console.WriteLine($"    Sid           = {Config.PaycomSid()}");
            Console.WriteLine($"    Token         = {Config.PaycomToken()}");
            Console.WriteLine($"    Timezone      = {Config.TimeZone()}");
            Console.WriteLine($"    Import Freq   = {Config.PunchImportFrequency}");
            Console.WriteLine("");
            Console.WriteLine("  Data:");
            Console.WriteLine($"    Incl. Pending = {Config.IncludePending}");
            Console.WriteLine($"    Sensitive API = {Config.IncludeSensitive}");

            Console.WriteLine("");
            Console.WriteLine("  Fields:");
            Console.WriteLine($"    LocationFld = {FieldService.LocationField}");
            Console.WriteLine($"    DivisionFld  = {FieldService.DivisionNameField}");
            Console.WriteLine($"    Fname  = {FieldService.FirstNameField}");

            Console.WriteLine("");
            Console.WriteLine("  Custom Fields:");
            foreach (var kvp in Config.CustomFields)
            {
                Console.WriteLine($"    {kvp.Key} = {kvp.Value}");
            }

            Console.WriteLine("");
            Console.WriteLine("  Time Zone Adjustments:");
            foreach (var kvp in Config.TimeZoneAdjustments)
            {
                Console.WriteLine($"    {kvp.Key} = {kvp.Value}");
            }
        }

        private int ExecCommand<T>(List<string> args) where T : new()
        {
            var command = new CommandArguments(args);

            return Command<T>.Invoke(command);
        }

        public void Cache(List<string> args)
        {
            ExecCommand<CacheCommand>(args);
        }

        public void Change(List<string> args)
        {
            ExecCommand<ChangeCommand>(args);
        }

        public int Employee(List<string> args)
        {
            return ExecCommand<EmployeeCommand>(args);
        }

        public void Export(List<string> args)
        {
            ExecCommand<ExportCommand>(args);
        }

        public void Hire(List<string> args)
        {
            ExecCommand<HireCommand>(args);
        }

        public void Location(List<string> args)
        {
            ExecCommand<LocationCommand>(args);
        }

        public void Import(List<string> args)
        {
            ExecCommand<ImportCommand>(args);
        }

        public void Labor(List<string> args)
        {
            ExecCommand<LaborCommand>(args);
        }

        public void Punch(List<string> args)
        {
            ExecCommand<PunchCommand>(args);
        }

        public void Set(List<string> args)
        {
            ExecCommand<SetCommand>(args);
        }

        static int Main(string[] args)
        {
            if (args.Length == 0)
            {
                DoShowUsage();
                return -1;
            }

            // setup logging services...
            var command = string.Join(" ", args);
            Logger.Setup($"Paycom.Tool, Command: '{command}', Version: {AppVersion}");

            // Log version information
            Log.Logger.Information($"Paycom.Tool.exe, version: {AppVersion}, settings: {Payroll.Shared.Setting.SettingFileName()}");

            using (var cache = CacheService.OpenCache(SystemConfig.CacheName()))
            {
                var employeeCollection = cache.GetCollection<Employee>(Config.EMPLOYEES_CACHE_COLLECTION);
                employeeCollection.EnsureIndex(x => x.ClockSeq);
            }

            try
            {
                Parser.Default.ParseArguments<ProgramArguments>(args)
                    .MapResult((ProgramArguments opts) => ProgramDriver<Program>.Run(opts), errs => 1);
            }
            catch (Exception e)
            {
                Log.Logger.Error(e.Message);
                Log.Logger.Error(e.StackTrace);
                return -1;
            }

            return 0;
        }
    }
}
