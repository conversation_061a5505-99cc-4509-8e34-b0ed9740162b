using System;

namespace Paycom2.Tool
{
    public class NewHire
    {
        public string firstName { get; set; }
        public string lastName { get; set; }
        public string middleName { get; set; }
        public int newHireId { get; set; }
        public string newEmployeeCode { get; set; }
        public string address { get; set; }
        public string apt_Suite_Other { get; set; }
        public string city { get; set; }
        public string state { get; set; }
        public string zip { get; set; }
        public DateTime? birthDate { get; set; }
        public string primaryPhone { get; set; }
        public string primaryPhoneCountryCode { get; set; }
        public string gender { get; set; }
        public string nickName { get; set; }
        public string zipCodeExtension { get; set; }
        public string secondaryPhone { get; set; }
        public string secondaryPhoneCountryCode { get; set; }
        public string personalEmail { get; set; }
        public string maritalStatus { get; set; }
        public string eeocEhnicity { get; set; }
        public string emergencyContact1 { get; set; }
        public string emergencyContact2 { get; set; }
        public string emergencyContact3 { get; set; }
        public string hireDate { get; set; }
        public string laborAllocation { get; set; }
        public string eeocCategory { get; set; }
        public string location { get; set; }
        public string workEmail { get; set; }
        public string primarySupervisor { get; set; }
        public string timeOffSupervisor { get; set; }
        public string secondarySupervisor { get; set; }
        public string tertiarySupervisor { get; set; }
        public string quarternarySupervisor { get; set; }
        public string talentManagementSupervisor { get; set; }
        public string participationDate401k { get; set; }
        public string workersCompCode { get; set; }
        public string clockSequenceNumber { get; set; }
        public string employeeGlCode { get; set; }
        public string payClass { get; set; }
        public string terminalAccessGroup { get; set; }
        public string primaryScheduleGroup { get; set; }
        public string scheduleTimeZone { get; set; }
        public string badgeNumber { get; set; }
        public string vets4212JobCategory { get; set; }
        public string vets4212EmployeeCategory { get; set; }
        public string positionSeat { get; set; }
        public string position { get; set; }
        public string positionTitle { get; set; }
        public string positionFamily { get; set; }
        public string positionLevel { get; set; }
        public string managerLevel { get; set; }
    }
}