using System;
using System.Collections;
using System.Collections.Generic;
using System.Linq;
using System.Text.Json;
using System.Threading;
using Paycom2.Shared;
using Payroll.Shared;
using Serilog;

namespace Paycom2.Tool
{
    class PunchCommand : BaseCommand
    {
        public int Coverage(List<string> args)
        {
            if (args == null || args.Count < 2)
            {
                Console.WriteLine("Usage: Paycom2.Tool.exe punch coverage <start> <end>");

                return 1;
            }

            try
            {
                DateTime startDate = new DateTime();
                DateTime endDate = new DateTime();

                ConsoleService.GetDateTimeWindow(args[0], args[1], ref startDate, ref endDate);
                var punchCountPerDay = new Dictionary<DateTime, int>();
                var punchCountPerImportTime = new Dictionary<string, int>();

                using (var paycomService = new PaycomRestService())
                {
                    var employeeIdentifiers = paycomService.GetActiveEmployeeIds().Result;
                    foreach (var eid in employeeIdentifiers)
                    {
                        var punches = paycomService.GetPunchHistoryAsync(eid.EECode, startDate, endDate).Result;
                        foreach (var punch in punches)
                        {
                            if (!punchCountPerDay.ContainsKey(punch.PunchTime.Date))
                            {   
                                punchCountPerDay[punch.PunchTime.Date] = 0;
                            }

                            punchCountPerDay[punch.PunchTime.Date]++;

                            if (!punchCountPerImportTime.ContainsKey(punch.TimeAdded.ToString("MM/dd/yyyy HH:mm")))
                            {
                                punchCountPerImportTime[punch.TimeAdded.ToString("MM/dd/yyyy HH:mm")] = 0;
                            }

                            punchCountPerImportTime[punch.TimeAdded.ToString("MM/dd/yyyy HH:mm")]++;
                        }

                        // get Paycom API a breather
                        Thread.Sleep(100);
                    }
                }
                foreach (var day in punchCountPerDay.OrderBy(x => x.Key))
                {
                    Console.WriteLine("{0}: {1}", day.Key.ToShortDateString(), day.Value);
                }

                Console.WriteLine("\nPunches per time:");
                foreach (var time in punchCountPerImportTime.OrderBy(x => x.Key))
                {
                    Console.WriteLine("{0}: {1}", time.Key, time.Value);
                }
            }
            catch (Exception e)
            {
                Log.Logger.Fatal(e.Message);
                Log.Logger.Fatal(e.StackTrace);
            }

            return 0;
        }

        public int Location(List<string> args)
        {
            if (args == null || args.Count < 3)
            {
                Console.WriteLine("Usage: Paycom2.Tool.exe punch location <start> <end> <location>");

                return 1;
            }

            try
            {
                DateTime startDate = new DateTime();
                DateTime endDate = new DateTime();
                string location = args[2];

                ConsoleService.GetDateTimeWindow(args[0], args[1], ref startDate, ref endDate);

                using (var paycomService = new PaycomRestService())
                {
                    var employeeIdentifiers = paycomService.GetActiveEmployeeIds().Result.ToList().OrderBy(eid => eid.EECode);
                    Console.WriteLine("EECode, Punch ID, Punch Time               , Punch Type, Time Added");

                    foreach (var eid in employeeIdentifiers)
                    {
                        var mer = paycomService.GetEmployeeWithMaximalData(eid.EECode).Result;
                        if (mer == null)
                        {
                            Log.Logger.Fatal("Failed to find employee with eecode {0}", eid.EECode);
                            continue;
                        }

                        var emp = paycomService.ConvertMasterEmployeeRecordToEmployee(mer).Result;
                        if (emp.PrimaryWorkLocation != location)
                            continue;

                        IEnumerable<HistoricalPunch> punches = paycomService.GetPunchHistoryAsync(eid.EECode, startDate, endDate).Result;
                        foreach (var punch in punches)
                        {
                            Console.WriteLine("{4}, {0}, {1}, {2}, {3}", punch.PunchId, punch.PunchTime, punch.PunchType, punch.TimeAdded, eid.EECode);
                        }

                        // get Paycom API a breather
                        Thread.Sleep(100);
                    }
                }
            }
            catch (Exception e)
            {
                Log.Logger.Fatal(e.Message);
                Log.Logger.Fatal(e.StackTrace);
            }

            return 0;
        }

        public void Rescue(List<string> args)
        {
            Console.WriteLine("Running rescue command...");
            if (args.Count > 3 && args[3] == "doit")
            {
                Console.WriteLine("DOIT flag provided - will actually delete punches");
            }
            else 
            {
                Console.WriteLine("DOIT flag not provided - this is a dry run");
            }
            if (args == null || args.Count < 2)
            {
                Console.WriteLine("Usage: Paycom2.Tool.exe punch rescue <location|all> <start> <end> [doit]");
                Console.WriteLine("Use 'rescue' to clean up problematic active punches.");
                return;
            }

            var filterLocation = "all";
            if (args != null && args.Count > 0)
            {
                filterLocation = args[0];
            }

            DateTime startDate = new DateTime();
            DateTime endDate = new DateTime();

            ConsoleService.GetDateTimeWindow(args[1], args[2], ref startDate, ref endDate);

            bool doit = args.Count > 3 && args[3].ToLower() == "doit";

            try
            {
                using (var paycomService = new PaycomRestService())
                {
                    var employees = paycomService.GetActiveEmployeeIds().Result;

                    foreach (EmployeeIdentifier employee in employees)
                    {
                        var cachedEmp = CacheService.FetchRecord<Employee>(Config.EMPLOYEES_CACHE_COLLECTION, employee.EECode);
                        var location = cachedEmp == null ? "" : cachedEmp.PrimaryWorkLocation;
                        if (filterLocation == "all" || filterLocation == location)
                        {
                            try
                            {
                                var punchPairs = new List<HistoricalPunch>();

                                IEnumerable<HistoricalPunch> punches = paycomService.GetPunchHistoryAsync(cachedEmp.Id, startDate, endDate).Result;
                                if (!punches.Any()) continue;
                                
                                Console.WriteLine("Punch ID, Punch Time               , Punch Type, Time Added");
                                foreach (var punch in punches)
                                {
                                    Console.WriteLine("{0}, {1}, {2}, {3}", punch.PunchId, punch.PunchTime, punch.PunchType, punch.TimeAdded);

                                    if (doit)
                                    {
                                        var success = paycomService.DeletePunchAsync(punch.PunchId.ToString()).Result;
                                        Console.WriteLine("{0}: {1}, {2}, {3}, {4}", success ? "Deleted" : "Failed", punch.PunchId, punch.PunchTime, punch.PunchType, punch.TimeAdded);
                                    }
                                    else
                                    {
                                        Console.WriteLine("Would have deleted: {0}, {1}, {2}, {3}", punch.PunchId, punch.PunchTime, punch.PunchType, punch.TimeAdded);
                                    }   
                                }
                            }
                            catch (Exception e)
                            {
                                Log.Logger.Fatal(e.Message);
                                Log.Logger.Fatal(e.StackTrace);
                            }

                        }
                    }
                }
            }
            catch (Exception e)
            {
                Log.Logger.Error(e.Message);
                Log.Logger.Error(e.StackTrace);
            }
        }

        public static void Issue(List<string> args)
        {
            Console.WriteLine("Running view issue command...");
            if (args.Count > 3 && args[3] == "doit")
            {
                Console.WriteLine("DOIT flag provided - will actually delete punches");
            }
            else 
            {
                Console.WriteLine("DOIT flag not provided - this is a dry run");
            }
            if (args == null || args.Count < 2)
            {
                Console.WriteLine("Usage: Paycom2.Tool.exe punch issue <location|all> <start> <end> [doit]");
                Console.WriteLine("Use 'issue' to clean up archived punches, not in current pay period");
                return;
            }

            var filterLocation = "all";
            if (args != null && args.Count > 0)
            {
                filterLocation = args[0];
            }

            DateTime startDate = new DateTime();
            DateTime endDate = new DateTime();

            ConsoleService.GetDateTimeWindow(args[1], args[2], ref startDate, ref endDate);

            bool doit = args.Count > 3 && args[3].ToLower() == "doit";

            try
            {
                using (var paycomService = new PaycomRestService())
                {
                    var employees = paycomService.GetActiveEmployeeIds().Result;

                    foreach (EmployeeIdentifier employee in employees)
                    {
                        var cachedEmp = CacheService.FetchRecord<Employee>(Config.EMPLOYEES_CACHE_COLLECTION, employee.EECode);
                        var location = cachedEmp == null ? "" : cachedEmp.PrimaryWorkLocation;
                        if (filterLocation == "all" || filterLocation == location)
                        {
                            try
                            {
                                var punchAudit = paycomService.GetPunchAuditAsync(cachedEmp.Id, startDate, endDate).Result;
                                if (punchAudit == null) continue;

                                if (punchAudit.Data?.Deleted != null)
                                {
                                    foreach (var punch in punchAudit.Data.Deleted)
                                    {
                                        if (doit)
                                        {
                                            var success = paycomService.DeletePunchAsync(punch.PunchId.ToString()).Result;
                                            Console.WriteLine("{0}: Punch {1} on {2} type {3} deleted by {4}", 
                                                success ? "Deleted" : "Failed", 
                                                punch.PunchId,
                                                punch.PunchTime,
                                                punch.PunchType,
                                                punch.DeletedBy);
                                        }
                                        else
                                        {
                                            Console.WriteLine(JsonSerializer.Serialize(punch, new JsonSerializerOptions { WriteIndented = true }));
                                        }
                                    }
                                }
                            }
                            catch (Exception e)
                            {
                                Log.Logger.Fatal(e.Message);
                                Log.Logger.Fatal(e.StackTrace);
                            }
                        }
                    }
                }
            }
            catch (Exception e)
            {
                Log.Logger.Error(e.Message);
                Log.Logger.Error(e.StackTrace);
            }
        }

        public int List(List<string> args)
        {
            if (args == null || args.Count < 3)
            {
                Console.WriteLine("Usage: Paycom2.Tool.exe punch list <eecode> <start> <end> <filter>");

                return 1;
            }

            var eecode = args[0];

            try
            {
                DateTime startDate = new DateTime();
                DateTime endDate = new DateTime();

                ConsoleService.GetDateTimeWindow(args[1], args[2], ref startDate, ref endDate);
                var punchPairs = new List<HistoricalPunch>();

                using (var paycomService = new PaycomRestService())
                {
                    DateTime timeAdded = new DateTime();
                    if (args.Count > 3)
                    {
                        timeAdded = DateTime.Parse(args[3]);
                    }

                    IEnumerable<HistoricalPunch> punches = paycomService.GetPunchHistoryAsync(eecode, startDate, endDate).Result;
                    Console.WriteLine("Punch ID, Punch Time               , Punch Type, Time Added");
                    foreach (var punch in punches)
                    {
                        if (timeAdded == DateTime.MinValue || punch.TimeAdded == timeAdded)
                        {
                            Console.WriteLine("{0}, {1}, {2}, {3}", punch.PunchId, punch.PunchTime, punch.PunchType, punch.TimeAdded);
                        }
                    }
                }
            }
            catch (Exception e)
            {
                Log.Logger.Fatal(e.Message);
                Log.Logger.Fatal(e.StackTrace);
            }

            return 0;
        }

        public int Clear(List<string> args)
        {
            if (args == null || args.Count < 3)
            {
                Console.WriteLine("Usage: Paycom2.Tool.exe punch clear <eecode> <start> <end> <doit>"); 
                Console.WriteLine("  where: <eecode> is the employee eecode");
                Console.WriteLine("  where: <start> and <end> are the start and end dates");
                Console.WriteLine("  where: <doit> is 'doit' to actually delete the punches");
                return 1;
            }

            var eecode = args[0];
            DateTime startDate = new DateTime();
            DateTime endDate = new DateTime();
            ConsoleService.GetDateTimeWindow(args[1], args[2], ref startDate, ref endDate);

            try
            {
                using (var paycomService = new PaycomRestService())
                {
                    IEnumerable<HistoricalPunch> punches = paycomService.GetPunchHistoryAsync(eecode, startDate, endDate).Result;
                    foreach (var punch in punches)
                    {
                        if (!punch.IsPaidTimeOff())
                        {
                            if (args.Count > 3 && args[3] == "doit")
                            {
                                var success = paycomService.DeletePunchAsync(punch.PunchId.ToString()).Result;
                                Console.WriteLine("{3}: {0}, on {1}, type {2}", 
                                    punch.PunchId, punch.PunchTime, punch.PunchType, success ? "Deleted" : "Failed to Delete");
                            }
                            else
                            {
                                Console.WriteLine("Would have deleted: {0}, on {1}, type {2}", punch.PunchId, punch.PunchTime, punch.PunchType);
                            }
                        }
                    }
                }
            }
            catch (Exception e)
            {
                Log.Logger.Fatal(e.Message);
                Log.Logger.Fatal(e.StackTrace);
            }

            return 0;
        }
        public int Delete(List<string> args)
        {
            if (args == null || args.Count < 1)
            {
                Console.WriteLine("Usage: Paycom2.Tool.exe punch delete <punch-id>");
                return 1;
            }

            var punchId = args[0];

            try
            {
                using (var paycomService = new PaycomRestService())
                {
                    var success = paycomService.DeletePunchAsync(punchId).Result;
                    Console.WriteLine("Punch deletion was {0}", success ? "successful" : "unsuccessful");
                }
            }
            catch (Exception e)
            {
                Log.Logger.Fatal(e.Message);
                Log.Logger.Fatal(e.StackTrace);
            }

            return 0;
        }

        public void Pad(List<string> args)
        {
            if (args.Count() == 0)
            {
                Console.WriteLine("Usage: Paycom2.Tool.exe punch pad <field> [<length> default=6]");
                Console.WriteLine("  Supported fields: 'cseq'");
                return;
            }

            if (!ConsoleService.TryGetPunchesFromInput(out List<PunchPair> punchPairs))
            {
                Log.Logger.Error("Failed to parse punches list");
                return;
            }

            if (punchPairs == null)
            {
                Log.Logger.Warning("No punch pairs detected.");
                return;
            }

            var padField = "cseq";
            if (args.Count() > 0)
            {
                padField = args[0];
            }

            // default pad length
            int padLength = 6;
            if (args.Count() > 1)
            {
                padLength = int.Parse(args[1]);
            }

            var breaks = 0;
            var pairs = 0;

            foreach (PunchPair pair in punchPairs)
            {
                switch (padField)
                {
                    case "cseq":
                        pair.ClockSeq = pair.ClockSeq.PadLeft(padLength, '0');
                        break;
                    default:
                        Log.Logger.Error("Invalid pad field: {0}", padField);
                        return;
                }

                pairs++;
                breaks += pair.Breaks.Count;
            }

            ConsoleService.PrintFormattedJson(punchPairs);
            Log.Logger.Information("Processed {x} punches, and {y} breaks", pairs, breaks);
        }


        public int Prune(List<string> args)
        {
            string doIt = "";
            if (args != null && args.Count > 0) doIt = args[0];

            using (var paycomService = new PaycomRestService())
            {
                string punchId;
                while ((punchId = Console.ReadLine()) != null)
                {
                    punchId = punchId.Trim();
                    if (string.IsNullOrEmpty(punchId))
                        continue;

                    if (doIt == "doit")
                    {
                        try
                        {
                            var success = paycomService.DeletePunchAsync(punchId).Result;
                            Console.WriteLine("Punch deletion for ID {0} was {1}", punchId, success ? "successful" : "unsuccessful");
                        }
                        catch (Exception e)
                        {
                            Log.Logger.Fatal(e.Message);
                            Log.Logger.Fatal(e.StackTrace);
                        }
                    }
                    else
                    {
                        Console.WriteLine("Punch deletion for ID {0} would have been successful", punchId);
                    }
                }
            }

            return 0;
        }
    }
}
