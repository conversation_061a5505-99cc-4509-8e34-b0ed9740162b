﻿using System;
using System.Collections;
using System.Collections.Generic;
using System.Text.Json;
using Paycom2.Shared;
using Payroll.Shared;
using Serilog;

namespace Paycom2.Tool
{
    class ExportCommand : BaseCommand
    {
        public void Settings(List<string> args)
        {
            var sid = Environment.GetEnvironmentVariable("PAYCOM_SID");
            Console.WriteLine($"export PAYCOM_SID={sid}");
            var token = Environment.GetEnvironmentVariable(variable: "PAYCOM_TOKEN");
            Console.WriteLine($"export PAYCOM_TOKEN={token}");
        }

        public void Changes(List<string> args)
        {
            try
            {
                var startDate = DateTime.Now.AddHours(-23);
                var endDate = DateTime.Now;
                ConsoleService.GetDateTimeWindowFromArgs(args, ref startDate, ref endDate);

                using (var paycomService = new PaycomRestService())
                {
                    var changes = paycomService.GetAuditLogsAsync(startDate, endDate).Result;
                    var json = JsonSerializer.Serialize(changes, Json.DefaultSerializerOutputStyle);
                    Console.WriteLine(json);
                }
            }
            catch (Exception e)
            {
                Log.Logger.Fatal(e.Message);
                Log.Logger.Fatal(e.StackTrace);
            }
        }

        private void PrintMerEmployeeData(PaycomRestService paycomService, IEnumerable<Shared.DirectoryEntry> directoryEntries)
        {
            foreach (var dEntry in directoryEntries)
            {
                var mer = paycomService.GetRawEmployeeWithSensitiveData(dEntry.eecode).Result;
                var jsonFormatted = JsonSerializer.Serialize(mer, Json.DefaultSerializerOutputStyle);
                Console.WriteLine(jsonFormatted);
            }
        }

        private void PrintDentryEmployeeData(IEnumerable<Shared.DirectoryEntry> directoryEntries)
        {
            var json = JsonSerializer.Serialize(directoryEntries, Json.DefaultSerializerOutputStyle);
            Console.WriteLine(json);
        }

        private void PrintStdEmployeeData(IEnumerable<Shared.DirectoryEntry> directoryEntries)
        {
            var employees = new List<Employee>();

            foreach (var directoryEntry in directoryEntries)
            {
                var emp = PaycomRestService.ConvertDirectoryEntryToEmployee(directoryEntry);
                employees.Add(emp);
            }

            var json = JsonSerializer.Serialize(employees, Json.DefaultSerializerOutputStyle);
            Console.WriteLine(json);
        }

        public void Raw(List<string> args)
        {
            // optional fetch limit
            uint fetchMax = 0;
            if (args != null && args.Count > 0)
            {
                fetchMax = Convert.ToUInt32(args[0]);
            }

            try
            {
                using (var paycomService = new PaycomRestService())
                {
                    var employees = paycomService.AllEmployeesAsync(fetchMax).Result;
                    PrintDentryEmployeeData(employees);
                }
            }
            catch (Exception e)
            {
                Log.Logger.Fatal(e.Message);
                Log.Logger.Fatal(e.StackTrace);
            }
        }

        public void Mer(List<string> args)
        {
            // optional fetch limit
            uint fetchMax = 0;
            if (args != null && args.Count > 0)
            {
                fetchMax = Convert.ToUInt32(args[0]);
            }

            try
            {
                using (var paycomService = new PaycomRestService())
                {
                    var employees = paycomService.AllEmployeesAsync(fetchMax).Result;
                    PrintMerEmployeeData(paycomService, employees);
                }
            }
            catch (Exception e)
            {
                Log.Logger.Fatal(e.Message);
                Log.Logger.Fatal(e.StackTrace);
            }
        }

        public void Employees(List<string> args)
        {
            // optional fetch limit
            uint fetchMax = 0;
            if (args != null && args.Count > 0)
            {
                fetchMax = Convert.ToUInt32(args[0]);
            }

            Log.Logger.Debug("Limiting fetches to {max} records", fetchMax);

            try
            {
                using (var paycomService = new PaycomRestService())
                {
                    var employees = paycomService.AllActiveDetailedEmployee(fetchMax).Result;
                    var json = JsonSerializer.Serialize(employees, Json.DefaultSerializerOutputStyle);
                    Console.WriteLine(json);
                }
            }
            catch (Exception e)
            {
                Log.Logger.Fatal(e.Message);
                Log.Logger.Fatal(e.StackTrace);
            }
        }

        public void Clockseq(List<string> args)
        {
            using (var paycomService = new PaycomRestService())
            {
                var employees = paycomService.AllEmployeesAsync().Result;
                
                var employeesByEecode = new Dictionary<string, DirectoryEntry>();
                foreach (var employee in employees)
                {
                    employeesByEecode[employee.eecode] = employee;
                }

                string eecode;
                while ((eecode = Console.ReadLine()) != null)
                {
                    eecode = eecode.Trim();
                    if (string.IsNullOrEmpty(eecode))
                        continue;

                    if (employeesByEecode.TryGetValue(eecode, out var employee))
                    {
                        Console.WriteLine(employee.clockseq);
                    }
                    else
                    {
                        Console.Error.WriteLine($"Employee with eecode {eecode} not found");
                    }
                }
            }
        }

        public void Eecode(List<string> args)
        {
            using (var paycomService = new PaycomRestService())
            {
                var employees = paycomService.AllEmployeesAsync().Result;
                var employeesByClockSeq = new Dictionary<string, DirectoryEntry>();
                string clockseq;

                foreach (var employee in employees)
                {
                    clockseq = employee.clockseq.PadLeft(6, '0');
                    employeesByClockSeq[clockseq] = employee;
                }

                while ((clockseq = Console.ReadLine()) != null)
                {
                    clockseq = clockseq.Trim();
                    clockseq = clockseq.PadLeft(6, '0');

                    if (string.IsNullOrEmpty(clockseq))
                        continue;

                    if (employeesByClockSeq.TryGetValue(clockseq, out var employee))
                    {
                        Console.WriteLine(employee.eecode);
                    }
                    else
                    {
                        Console.Error.WriteLine($"Employee with clockseq {clockseq} not found");
                    }
                }
            }
        }

        public int Pto(List<string> args)
        {
            if (args == null || args.Count < 2)
            {
                Console.WriteLine("Usage: Paycom2.Tool.exe export pto <start> <end>");
                return 1;
            }

            // optional fetch limit
            int fetchMax = -1;
            if (args != null && args.Count > 2)
            {
                fetchMax = Convert.ToInt32(args[2]);
            }

            Log.Logger.Information("Limiting fetches to {max} records", fetchMax);

            try
            {
                var startDate = DateTime.Now.AddDays(-7);
                var endDate = DateTime.Now;
                ConsoleService.GetDateTimeWindow(args[0], args[1], ref startDate, ref endDate);

                var timeSpan = endDate - startDate;
                if (timeSpan.TotalDays > 30)
                {
                    Console.WriteLine("  Error: Export window too large. Shorten to 30 days or less. Paycom restriction.");
                    return 1;
                }

                using (var paycomService = new PaycomRestService())
                {
                    var employeeIdentifiers = paycomService.GetActiveEmployeeIds().Result;
                    var punchPairs = new List<PunchPair>();

                    foreach (var eid in employeeIdentifiers)
                    {
                        bool foundPto = false;

                        var punches = paycomService.GetPunchHistoryAsync(eid.EECode, startDate, endDate).Result;
                        foreach (var punch in punches)
                        {
                            if (punch.IsPaidTimeOff())
                            {
                                var pp = Converter.HistoricalPunchToPunchPair(punch);

                                var mer = paycomService.GetEmployeeWithMaximalData(eid.EECode).Result;
                                var emp = paycomService.ConvertMasterEmployeeRecordToEmployee(mer).Result;
                                pp.JobCode = emp.Salaried ? "Salary" : "Hourly";

                                punchPairs.Add(pp);
                                foundPto = true;
                            }
                        }

                        if (fetchMax != -1)
                        {
                            if (foundPto) fetchMax--;
                            if (fetchMax == 0) break;
                        }
                    }

                    var json = JsonSerializer.Serialize(punchPairs, Json.DefaultSerializerOutputStyle);
                    Console.WriteLine(json);
                }
            }
            catch (Exception e)
            {
                Log.Logger.Fatal(e.Message);
                Log.Logger.Fatal(e.StackTrace);
            }

            return 0;
        }

        public int Punches(List<string> args)
        {
            if (args == null || args.Count < 1)
            {
                Console.WriteLine("Usage: Paycom2.Tool.exe export punches <eecode> <start> <end>");
                return 1;
            }

            var eecode = args[0];

            try
            {
                DateTime startDate = new DateTime();
                DateTime endDate = new DateTime();

                ConsoleService.GetDateTimeWindow(args[1], args[2], ref startDate, ref endDate);
                var punchPairs = new List<HistoricalPunch>();

                using (var paycomService = new PaycomRestService())
                {
                    IEnumerable<HistoricalPunch> punches = paycomService.GetPunchHistoryAsync(eecode, startDate, endDate).Result;
                    foreach (var punch in punches)
                    {
                        if (!punch.IsPaidTimeOff())
                        {
                            punchPairs.Add(punch);
                        }
                    }
                }

                var json = JsonSerializer.Serialize(punchPairs, Json.DefaultSerializerOutputStyle);
                Console.WriteLine(json);
            }
            catch (Exception e)
            {
                Log.Logger.Fatal(e.Message);
                Log.Logger.Fatal(e.StackTrace);
            }

            return 0;
        }
    }
}
