﻿using System;
using System.Collections;
using System.Collections.Generic;
using System.Text.Json;
using Paycom2.Shared;
using Payroll.Shared;
using Serilog;

namespace Paycom2.Tool
{
    class SetCommand : BaseCommand
    {
        public void Clockseq(List<string> args)
        {
            if (args.Count < 2)
            {
                Log.Logger.Information("Usage: set clockseq <employeeId> <clockseq>");
                return;
            }

            var employeeId = args[0];
            var clockseq = args[1];
            
            var dryRun = true;
            if (args.Count > 2 && args[2] == "doit")
            {
                dryRun = false;
            }
            
            if (string.IsNullOrEmpty(employeeId) || string.IsNullOrEmpty(clockseq))
            {
                Log.Logger.Information("{0} - empty id or clockseq, skipping...");
                return;
            }

            if (dryRun)
            {
                Log.Logger.Information("{0} update clockseq for {1} to {2}", "(DryRun) Wanted to", employeeId, clockseq);
                return;
            }

            using (var paycomService = new PaycomRestService())    
            {
                var rc = paycomService.UpdateClockSeqAsync(employeeId, clockseq).Result;
                Log.Logger.Information("{0} update clockseq for {1} to {2}", rc ? "Successful" : "Failed", employeeId, clockseq);  
            }
        }
    }
}
