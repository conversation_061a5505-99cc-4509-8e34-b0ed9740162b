﻿using Paycom2.Shared;
using Payroll.Shared;
using Serilog;
using System;
using System.Collections.Generic;
using System.Text.Json;

namespace Paycom2.Tool
{
    class ChangeCommand : BaseCommand
    {
        public void List(List<string> args)
        {
            var startDate = DateTime.Now.AddHours(-1);
            var endDate = DateTime.Now;

            try
            {
                using (var paycomService = new PaycomRestService())
                {
                    var changes = paycomService.GetAuditLogsAsync(startDate, endDate).Result;
                    string formattedJson = JsonSerializer.Serialize(changes, Json.DefaultSerializerOutputStyle);
                    Console.WriteLine(formattedJson);
                }
            }
            catch (Exception e)
            {
                Log.Logger.Error(e.Message);
                if (!string.IsNullOrEmpty(e.StackTrace)) Log.Logger.Error(e.StackTrace);
            }
        }

        public void View(List<string> args)
        {
            if (args == null || args.Count < 1)
            {
                Console.WriteLine("Usage: Paycom2.Tool.exe change view <eecode>");
                return;
            }

            var eecode = args[0];

            try
            {
                using (var paycomService = new PaycomRestService())
                {
                    var changes = paycomService.GetChangesAsync(eecode).Result;
                    string formattedJson = JsonSerializer.Serialize(changes, Json.DefaultSerializerOutputStyle);
                    Console.WriteLine(formattedJson);
                }
            }
            catch (Exception e)
            {
                Log.Logger.Error(e.Message);
                if (!string.IsNullOrEmpty(e.StackTrace)) Log.Logger.Error(e.StackTrace);
            }
        }

        public void Sview(List<string> args)
        {
            if (args == null || args.Count < 1)
            {
                Console.WriteLine("Usage: Paycom2.Tool.exe employee sview <eecode>");
                return;
            }

            var eecode = args[0];

            try
            {
                using (var paycomService = new PaycomRestService())
                {
                    var changes = paycomService.GetSensitiveChangesAsync(eecode).Result;
                    string formattedJson = JsonSerializer.Serialize(changes, Json.DefaultSerializerOutputStyle);
                    Console.WriteLine(formattedJson);
                }
            }
            catch (Exception e)
            {
                Log.Logger.Error(e.Message);
                if (!string.IsNullOrEmpty(e.StackTrace)) Log.Logger.Error(e.StackTrace);
            }
        }
    }
}
