﻿using Serilog;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text.Json;
using Payroll.Shared;
using Paycom2.Shared;

namespace Paycom2.Tool
{
    public static class Converter
    {
        /*
        Possible status codes:
        Active = A, Terminated = T, On Leave = V, Deceased = D, Retired = R, Inactive = I, NotHired = N
        */
        public static bool IsActiveEmployee(string paycom_status_code)
        {
            // these codes are treated as active
            if (paycom_status_code == MasterEmployeeRecord.ActiveStatus) return true;
            if (paycom_status_code == MasterEmployeeRecord.OnLeaveStatus) return true;
            if (paycom_status_code == MasterEmployeeRecord.InactiveStatus) return true;

            // some companies would like to include pending new hires and treat them as active
            if (Config.IncludePending && paycom_status_code == MasterEmployeeRecord.PendingStatus) return true;

            if (paycom_status_code == MasterEmployeeRecord.TerminatedStatus) return false;
            if (paycom_status_code == MasterEmployeeRecord.DeceasedStatus) return false;
            if (paycom_status_code == MasterEmployeeRecord.RetiredStatus) return false;
            if (paycom_status_code == MasterEmployeeRecord.NotHiredStatus) return false;

            Log.Logger.Warning("Unknown paycom status code: {x}", paycom_status_code);
            return false;
        }

        // Paycom does not seem to understand daylight savings timezones
        public static string ToStandardTimeZonesOnly(string tz)
        {
            switch (tz)
            {
                case "EDT":
                    return "EST";
                case "CDT":
                    return "CST";
                case "MDT":
                    return "MST";
                case "PDT":
                    return "PST";
                default:
                    return tz;
            }
        }

        public static PunchPair HistoricalPunchToPunchPair(HistoricalPunch hp)
        {
            var pp = new PunchPair()
            {
                Id = Convert.ToString(hp.PunchId),
                EECode = hp.EmployeeCode,
                ClockSeq = hp.ClockSequence,
                Created = hp.TimeAdded,
                TimeIn = hp.PunchTime,
                Description = hp.PunchDescription,
                Location = hp.DepartmentCode
            };

            // this is an "hours" punch
            if (hp.EntryType == 2)
            {
                pp.TimeOut = hp.PunchTime.AddHours(Convert.ToDouble(hp.Hours));
            }

            return pp;
        }
    }
}
