﻿using System;
using System.Collections.Generic;
using Serilog;
using System.Text.Json;
using Payroll.Shared;
using System.Linq;
using Location = Paycom2.Shared.Location;
using LiteDB;

namespace Paycom2.Tool
{
    class CacheCommand : BaseCommand
    {
        public void Dump(List<string> args)
        {
            var filter = "all";
            if (args != null && args.Count > 0)
            {
                filter = args[0];
            }

            try
            {
                using (var cache = CacheService.OpenCache(SystemConfig.CacheName()))
                {
                    var col = cache.GetCollection<Employee>(Config.EMPLOYEES_CACHE_COLLECTION);
                    var employees = new List<Employee>();
                    DateTime now = DateTime.Now;

                    if (filter == "all")
                        employees = col.Query().OrderBy(x => x.ClockSeq).ToList();
                    else if (filter == "new")
                        employees = col.Find(x => x.Active == true && (x.OrgCode == "new" || x.RehireDate > now.Date)).ToList();
                    else if (filter == "new2")
                        employees = col.Find(x => string.IsNullOrEmpty(x.ClockSeq) && x.Active == true && (x.OrgCode == "new" || x.RehireDate > now.Date)).ToList();
                    else if (filter == "cseq")
                        employees = col.Find(x => x.ClockSeq != null && x.Active == true).ToList();
                    else if (filter == "salaried")
                        employees = col.Find(x => x.Salaried == true).ToList();
                    else if (filter == "term")
                    {
                        employees = col.Find(x => x.ClockSeq != null && x.Active == false).OrderBy(x => x.TermDate).ToList();

                        var maxDaysOld = -1;
                        if (args.Count > 1)
                        {
                            maxDaysOld = Convert.ToInt32(args[1]);
                        }

                        if (maxDaysOld != -1)
                        {
                            var recentTerms = employees.FindAll(x =>
                            {
                                var age = now - x.TermDate;
                                Log.Logger.Debug("Emp: {0}, Now: {1}, TermDate: {2}, Age: {3}, Max: {4}, {5}",
                                    x.Id, now.ToString("MM/dd/yyyy hh:mm:ss tt zzz"), x.TermDate?.ToString("MM/dd/yyyy hh:mm:ss tt zzz"),
                                    age.HasValue ? Math.Floor(age.Value.TotalDays) : (double?)null, maxDaysOld, x.Description);
                                return age?.TotalDays < maxDaysOld;
                            });

                            employees = recentTerms;
                        }
                    }

                    var json = System.Text.Json.JsonSerializer.Serialize(employees, Json.DefaultSerializerOutputStyle);
                    Console.WriteLine(json);
                }
            }
            catch (Exception e)
            {
                Log.Logger.Fatal(e.Message);
                Log.Logger.Fatal(e.StackTrace);
            }
        }

        private void CacheEmployees(IEnumerable<Employee> employees)
        {
            CacheService.CacheLastUpdatedTime(DateTime.Now);
            if (employees == null) return;

            try
            {
                using (var cache = CacheService.OpenCache(SystemConfig.CacheName()))
                {
                    var empCache = cache.GetCollection<Employee>(Config.EMPLOYEES_CACHE_COLLECTION);
                    foreach (var emp in employees)
                    {
                        empCache.Upsert(emp);
                    }

                    empCache.EnsureIndex(x => x.ClockSeq);
                }
            }
            catch (Exception e)
            {
                Log.Logger.Fatal(e.Message);
                Log.Logger.Fatal(e.StackTrace);
            }
        }

        private void CacheLocations(IEnumerable<Location> locations)
        {
            CacheService.CacheLastUpdatedTime(DateTime.Now);
            if (locations == null) return;

            try
            {
                using (var cache = CacheService.OpenCache(SystemConfig.CacheName()))
                {
                    var locCache = cache.GetCollection<Location>("locations");
                    foreach (var loc in locations)
                    {
                        locCache.Upsert(loc);
                    }

                    locCache.EnsureIndex(x => x.locationid);
                }
            }
            catch (Exception e)
            {
                Log.Logger.Fatal(e.Message);
                Log.Logger.Fatal(e.StackTrace);
            }
        }

        public void Init(List<string> args)
        {
            try
            {
                using (var paycomService = new PaycomRestService())
                {
                    var employees = paycomService.AllActiveDetailedEmployee().Result;
                    CacheEmployees(employees);
                }
            }
            catch (Exception e)
            {
                Log.Logger.Fatal(e.Message);
                Log.Logger.Fatal(e.StackTrace);
            }
        }

        public void New(List<string> args)
        {
            try
            {
                using (var paycomService = new PaycomRestService())
                {
                    var employeeIds = paycomService.GetActiveEmployeeIds().Result;
                    foreach (var eid in employeeIds)
                    {
                        if (CacheService.FetchRecord<Employee>(Config.EMPLOYEES_CACHE_COLLECTION, eid.EECode) != null)
                        {
                            Log.Logger.Information("Skipping previously cached employee {0}...", eid.EECode);
                            continue;
                        }

                        var mer = paycomService.GetEmployeeWithMaximalData(eid.EECode).Result;
                        var emp = paycomService.ConvertMasterEmployeeRecordToEmployee(mer).Result;

                        Log.Logger.Information("Caching employee {0}, {1} {2}...", emp.Id, emp.FirstName, emp.LastName);
                        CacheService.CacheRecord(Config.EMPLOYEES_CACHE_COLLECTION, emp);
                    }
                }
            }
            catch (Exception e)
            {
                Log.Logger.Fatal(e.Message);
                Log.Logger.Fatal(e.StackTrace);
            }
        }

        private void CacheArgumentHelp()
        {
            Console.WriteLine("  <value> <type>:");
            Console.WriteLine("    <integer> days    - Maximum number of days old the term date can be");
            Console.WriteLine("    <integer> min-id  - Minimum allowed employee clock seq that will be cached");
        }

        // for testing purposes only
        public void Terms(List<string> args)
        {
            if (args == null || args.Count < 2)
            {
                Console.WriteLine("Usage: Paycom2.Tool.exe cache terms <value> <type>");
                CacheArgumentHelp();
                return;
            }

            // Parse the first argument as an integer
            if (!int.TryParse(args[0], out int value) || value < 0)
            {
                Console.WriteLine("Error: first argument must be a non-negative integer");
                return;
            }

            string type = args[1].ToLower();
            if (type != "days" && type != "min-id")
            {
                Console.WriteLine("Error: second argument must be either 'days' or 'min-id'");
                Console.WriteLine("Usage: Paycom2.Tool.exe cache terms <value> <type>");
                CacheArgumentHelp();
                return;
            }

            try
            {
                using (var paycomService = new PaycomRestService())
                {
                    var employees = paycomService.EmployeeBasicInfoTakeWhile(emp => !Converter.IsActiveEmployee(emp.eestatus)).Result;

                    var batch = new List<Employee>();
                    DateTime now = DateTime.Now;
                    int processedCount = 0;
                    int skippedCount = 0;

                    foreach (var e in employees.Keys)
                    {
                        // skip employees already cached
                        var cachedEmployee = CacheService.FetchRecord<Employee>(Config.EMPLOYEES_CACHE_COLLECTION, e);
                        if (cachedEmployee?.Active == false)
                        {
                            Log.Logger.Debug("Skipping previously cached employee {0}...", e);
                            skippedCount++;
                            continue;
                        }

                        var emp = employees[e];
                        Shared.MasterEmployeeRecord masterEmployeeRecord = paycomService.GetEmployeeWithMaximalData(e).Result;
                        paycomService.AddDetailedDataToEmployee(emp, masterEmployeeRecord).Wait();

                        bool shouldCache = false;

                        if (type == "days")
                        {
                            var age = now - emp.TermDate;
                            Log.Logger.Debug("Emp: {0}, Now: {1}, TermDate: {2}, Age: {3}, Max: {4}",
                                emp.Id, now.ToString("MM/dd/yyyy hh:mm:ss tt zzz"), emp.TermDate?.ToString("MM/dd/yyyy hh:mm:ss tt zzz"),
                                age.HasValue ? Math.Floor(age.Value.TotalDays) : (double?)null, value);

                            shouldCache = age?.TotalDays < value;
                        }
                        else if (type == "min-id")
                        {
                            // Check if employee code meets minimum requirement
                            if (int.TryParse(masterEmployeeRecord.clocksequencenumber, out int clockSeq))
                            {
                                Log.Logger.Debug("Emp: {0}, ClockSeq: {1}, Min Required: {2}",
                                    emp.ClockSeq, clockSeq, value);

                                shouldCache = clockSeq >= value;
                            }
                            else
                            {
                                Log.Logger.Warning("Could not parse employee code {0} as integer for employee {1}",
                                    masterEmployeeRecord.clocksequencenumber, emp.Id);
                                shouldCache = false;
                            }
                        }

                        if (shouldCache)
                        {
                            batch.Add(emp);
                            processedCount++;
                        }
                        else
                        {
                            skippedCount++;
                        }

                        if (batch.Count >= 20)
                        {
                            CacheEmployees(batch);
                            batch.Clear();
                        }
                    }

                    if (batch.Count > 0)
                    {
                        CacheEmployees(batch);
                    }

                    Log.Logger.Information("Terms caching completed. Processed: {0}, Skipped: {1}", processedCount, skippedCount);
                }
            }
            catch (Exception e)
            {
                Log.Logger.Fatal(e.Message);
                Log.Logger.Fatal(e.StackTrace);
            }
        }

        public int Employee(List<string> args)
        {
            if (args == null || args.Count < 1)
            {
                Console.WriteLine("Usage: Paycom2.Tool.exe cache employee <eecode>");
                return 1;
            }

            var eecode = args[0];
            try
            {
                using (var paycomService = new PaycomRestService())
                {
                    var mer = paycomService.GetEmployeeWithMaximalData(eecode).Result;
                    if (mer == null)
                    {
                        Log.Logger.Fatal("Failed to find employee with eecode {0}", eecode);
                        return 1;
                    }

                    var emp = paycomService.ConvertMasterEmployeeRecordToEmployee(mer).Result;
                    var employees = new List<Employee>();
                    employees.Add(emp);
                    CacheEmployees(employees);

                    var formattedJson = System.Text.Json.JsonSerializer.Serialize(employees, Json.DefaultSerializerOutputStyle);
                    Console.WriteLine(formattedJson);
                }
            }
            catch (Exception e)
            {
                Log.Logger.Fatal(e.Message);
                Log.Logger.Fatal(e.StackTrace);
                return 1;
            }

            return 0;
        }

        public void Update(List<string> args)
        {
            var startDate = DateTime.Now.AddHours(-23);
            var endDate = DateTime.Now;
            ConsoleService.GetDateTimeWindowFromArgs(args, ref startDate, ref endDate);

            Log.Logger.Information("* Changes from {start} to {end}", startDate, endDate);

            // Default term date cutoff is 1 day before start date
            int termDateCutoffDays = 1;

            // Check if a term date cutoff days argument was provided (third argument)
            if (args != null && args.Count > 2 && int.TryParse(args[2], out int cutoffDays))
            {
                termDateCutoffDays = cutoffDays;
            }

            var termDateCutoff = startDate.AddDays(-termDateCutoffDays);
            Log.Logger.Information("* Term Cutoff: {term} ({days} days before start date)", termDateCutoff, termDateCutoffDays);

            try
            {
                var changedEmployees = new List<Employee>();
                using (var paycomService = new PaycomRestService())
                {
                    changedEmployees.AddRange(paycomService.GetChangedEmployeesAsync(startDate, endDate).Result);
                    Log.Logger.Information("Caching {0} changed employees...", changedEmployees.Count());

                    var newHires = paycomService.GetNewHires(startDate, endDate).Result;
                    Log.Logger.Information("Caching {0} new hires...", newHires.Count());
                    changedEmployees.AddRange(newHires);

                    // as a sanity check, make sure active employees match the cache
                    var disconnecteEmployees = new List<Employee>();
                    using (var cache = CacheService.OpenCache(SystemConfig.CacheName()))
                    {
                        var employees = paycomService.EmployeeBasicInfoTakeWhile(emp => true).Result;
                        foreach (var kvp in employees)
                        {
                            var paycomEmployee = kvp.Value;
                            if (paycomEmployee.TermDate.HasValue && paycomEmployee.TermDate.Value > termDateCutoff) continue;

                            var cachedEmployee = CacheService.FetchRecord<Employee>(Config.EMPLOYEES_CACHE_COLLECTION, paycomEmployee.Id);
                            if (cachedEmployee == null || cachedEmployee.Active != paycomEmployee.Active)
                            {
                                var why = cachedEmployee == null ? "null" : cachedEmployee.Active.ToString();
                                Log.Logger.Information("Cache disconnect for employee {0}, updating cache...({1})", paycomEmployee.Id, why);
                                var mer = paycomService.GetEmployeeWithMaximalData(paycomEmployee.Id).Result;
                                if (mer == null)
                                {
                                    Log.Logger.Fatal("Failed to find employee with eecode {0}", paycomEmployee.Id);
                                    continue;
                                }

                                var masterEmployeeRecord = paycomService.ConvertMasterEmployeeRecordToEmployee(mer).Result;
                                disconnecteEmployees.Add(masterEmployeeRecord);
                            }
                        }

                        Log.Logger.Information("Caching {0} disconnected employees...", disconnecteEmployees.Count());
                        changedEmployees.AddRange(disconnecteEmployees);
                    }
                }

                CacheEmployees(changedEmployees);
            }
            catch (Exception e)
            {
                Log.Logger.Fatal(e.Message);
                Log.Logger.Fatal(e.StackTrace);
            }
        }

        public void Export(List<string> args)
        {
            if (args == null || args.Count < 2 || args.Count % 2 != 0)
            {
                Console.WriteLine("Usage: Paycom2.Tool.exe cache export <property> <value> [<property2> <value2> ...]");
                Console.WriteLine("Example: export orgcode 15 active true");
                return;
            }

            try
            {
                using (var cache = CacheService.OpenCache(SystemConfig.CacheName()))
                {
                    var col = cache.GetCollection<Employee>(Config.EMPLOYEES_CACHE_COLLECTION);
                    var employees = col.Query().ToList();

                    // Process each property-value pair
                    for (int i = 0; i < args.Count; i += 2)
                    {
                        string propertyName = args[i].ToLower();
                        string value = args[i + 1];

                        employees = employees.Where(emp =>
                        {
                            var prop = typeof(Employee).GetProperty(propertyName,
                                System.Reflection.BindingFlags.IgnoreCase |
                                System.Reflection.BindingFlags.Public |
                                System.Reflection.BindingFlags.Instance);

                            if (prop == null)
                            {
                                // Check if the property exists in Attributes dictionary
                                if (emp.Attributes != null && emp.Attributes.ContainsKey(propertyName))
                                {
                                    var attributeValue = emp.Attributes[propertyName]?.ToString();
                                    return attributeValue?.ToLower() == value.ToLower();
                                }

                                Log.Logger.Warning("Property {PropertyName} not found on Employee or in Attributes", propertyName);
                                return true; // Skip invalid properties
                            }

                            var propValue = prop.GetValue(emp)?.ToString();

                            // Handle different property types
                            if (prop.PropertyType == typeof(bool) || prop.PropertyType == typeof(bool?))
                            {
                                return bool.TryParse(value, out bool boolValue) &&
                                       propValue?.ToLower() == boolValue.ToString().ToLower();
                            }
                            else if (prop.PropertyType == typeof(int) || prop.PropertyType == typeof(int?))
                            {
                                return int.TryParse(value, out int intValue) &&
                                       propValue == intValue.ToString();
                            }
                            else if (prop.PropertyType == typeof(DateTime) || prop.PropertyType == typeof(DateTime?))
                            {
                                if (value.EndsWith("d"))
                                {
                                    string durationValue = value.Substring(0, value.Length - 1);
                                    var sinceDate = DateTime.Now.AddDays(-Convert.ToInt32(durationValue));
                                    var propDate = prop.GetValue(emp) as DateTime?;
                                    return propDate.HasValue && propDate.Value >= sinceDate;
                                }
                                else
                                {
                                    return DateTime.TryParse(value, out DateTime dateValue) && propValue == dateValue.ToString();
                                }
                            }
                            else // Handle as string
                            {
                                return propValue?.ToLower() == value.ToLower();
                            }
                        }).ToList();

                        Log.Logger.Information("Filtered by {PropertyName} = {Value}, Remaining records: {Count}",
                            propertyName, value, employees.Count());
                    }

                    var json = System.Text.Json.JsonSerializer.Serialize(employees, Json.DefaultSerializerOutputStyle);
                    Console.WriteLine(json);
                    Log.Logger.Information("Exported {Count} records", employees.Count());
                }
            }
            catch (Exception e)
            {
                Log.Logger.Fatal(e.Message);
                Log.Logger.Fatal(e.StackTrace);
            }
        }

        public void Prune(List<string> args)
        {
            if (args == null || args.Count < 2)
            {
                Console.WriteLine("Usage: Paycom2.Tool.exe cache prune <value> <type> [doit]");
                CacheArgumentHelp();
                Console.WriteLine("  doit: If provided, actually delete the records; otherwise just print what would be deleted");
                return;
            }

            // Parse the first argument as an integer
            if (!int.TryParse(args[0], out int value) || value < 0)
            {
                Console.WriteLine("Error: first argument must be a non-negative integer");
                return;
            }

            string type = args[1].ToLower();
            if (type != "days" && type != "min-id")
            {
                Console.WriteLine("Error: second argument must be either 'days' or 'min-id'");
                Console.WriteLine("Usage: Paycom2.Tool.exe cache prune <value> <type> [doit]");
                CacheArgumentHelp();
                Console.WriteLine("  doit: If provided, actually delete the records; otherwise just print what would be deleted");
                return;
            }

            // Check if we should actually delete the records
            bool doDelete = args.Count > 2 && args[2].ToLower() == "doit";
            string actionText = doDelete ? "Deleting" : "Would delete";

            try
            {
                using (var cache = CacheService.OpenCache(SystemConfig.CacheName()))
                {
                    var col = cache.GetCollection<Employee>(Config.EMPLOYEES_CACHE_COLLECTION);
                    int deletedCount = 0;

                    if (type == "days")
                    {
                        // Find all terminated employees
                        var terminatedEmployees = col.Find(x => x.Active == false && x.TermDate != null).ToList();
                        Log.Logger.Information("Found {0} terminated employees in cache", terminatedEmployees.Count);

                        DateTime now = DateTime.Now;

                        // Filter employees with term dates older than maxDaysOld
                        foreach (var employee in terminatedEmployees)
                        {
                            if (!employee.TermDate.HasValue) continue;

                            var age = now - employee.TermDate.Value;
                            double daysSinceTermination = Math.Floor(age.TotalDays);

                            if (daysSinceTermination > value)
                            {
                                Log.Logger.Information("{0} employee {1}, {2} {3}, term date: {4} ({5} days ago)",
                                    actionText,
                                    employee.Id,
                                    employee.FirstName,
                                    employee.LastName,
                                    employee.TermDate?.ToString("MM/dd/yyyy"),
                                    daysSinceTermination);

                                if (doDelete)
                                {
                                    CacheService.RemoveRecord<Employee>(Config.EMPLOYEES_CACHE_COLLECTION, employee.Id);
                                }
                                deletedCount++;
                            }
                        }

                        string resultMessage = doDelete
                            ? $"Deleted {deletedCount} employees with term dates older than {value} days"
                            : $"Found {deletedCount} employees that would be deleted with term dates older than {value} days";

                        Log.Logger.Information(resultMessage);
                    }
                    else if (type == "min-id")
                    {
                        // Find all employees with employee codes below the minimum
                        var allEmployees = col.Query().ToList();
                        Log.Logger.Information("Found {0} total employees in cache", allEmployees.Count);

                        foreach (var employee in allEmployees)
                        {
                            // Try to parse employee ID as integer for comparison
                            if (int.TryParse(employee.ClockSeq, out int clockSeq) && clockSeq < value)
                            {
                                Log.Logger.Information("{0} employee {1}, {2} {3}, employee code: {4} (below minimum {5})",
                                    actionText,
                                    employee.Id,
                                    employee.FirstName,
                                    employee.LastName,
                                    clockSeq,
                                    value);

                                if (doDelete)
                                {
                                    CacheService.RemoveRecord<Employee>(Config.EMPLOYEES_CACHE_COLLECTION, employee.Id);
                                }
                                deletedCount++;
                            }
                        }

                        string resultMessage = doDelete
                            ? $"Deleted {deletedCount} employees with employee codes below {value}"
                            : $"Found {deletedCount} employees that would be deleted with employee codes below {value}";

                        Log.Logger.Information(resultMessage);
                    }
                }
            }
            catch (Exception e)
            {
                Log.Logger.Fatal(e.Message);
                Log.Logger.Fatal(e.StackTrace);
            }
        }
    }
}
