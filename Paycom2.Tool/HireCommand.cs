﻿using Paycom2.Shared;
using Payroll.Shared;
using Serilog;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text.Json;

namespace Paycom2.Tool
{
    class HireCommand : BaseCommand
    {
        public void Ids(List<string> args)
        {
            try
            {
                var newHires = new Dictionary<int, NewHireEntry>();

                for (var i = 0; i < 15; i++)
                {
                    var startDate = DateTime.Now.AddDays(-(i + 1)).AddMinutes(-59);
                    var endDate = DateTime.Now.AddDays(-i);

                    using (var paycomService = new PaycomRestService())
                    {
                        var nhData = paycomService.GetNewHireIdsAsync(startDate, endDate).Result;
                        foreach (var nh in nhData)
                        {
                            if (newHires.ContainsKey(nh.new_hire_id) == false)
                                newHires.Add(nh.new_hire_id, nh);
                        }
                    }
                }

                foreach (var nh in newHires.Values)
                {
                    Console.WriteLine("{0}\t{1}\t{2}\t{3}\t{4}\t{5}", nh.eecode, nh.eename, nh.new_hire_id,nh.status, nh.timeadded, nh.addedby);
                }
            }
            catch (Exception e)
            {
                Log.Logger.Error(e.Message);
                Log.Logger.Error(e.StackTrace);
            }
        }

        public void Dump(List<string> args)
        {
            var startDate = DateTime.Now.AddHours(-23);
            var endDate = DateTime.Now;
            ConsoleService.GetDateTimeWindowFromArgs(args, ref startDate, ref endDate);

            try
            {
                List<Employee> newEmployees = new List<Employee>();
                List<NewHire> newHires = new List<NewHire>();

                using (var paycomService = new PaycomRestService())
                {
                    var nhData = paycomService.GetNewHireIdsAsync(startDate, endDate).Result;
                    foreach (var nh in nhData)
                    {
                        // these new hires dont have enough info to process them yet
                        if (nh.status == "Pending New Hire")
                        {
                            NewHire nhDetails = paycomService.GetNewHireAsync(nh.new_hire_id).Result;
                            newHires.Add(nhDetails);
                            var emp = paycomService.ConvertNewHireRecordToEmployee(nhDetails);
                            newEmployees.Add(emp);
                        }
                        else
                        {
                            var mer = paycomService.GetEmployeeWithMaximalData(nh.eecode).Result;
                            var emp = paycomService.ConvertMasterEmployeeRecordToEmployee(mer).Result;
                            newEmployees.Add(emp);
                        }
                    }
                }

                Console.WriteLine(JsonSerializer.Serialize(newHires, Json.DefaultSerializerOutputStyle));
                Console.WriteLine(JsonSerializer.Serialize(newEmployees, Json.DefaultSerializerOutputStyle));
            }
            catch (Exception e)
            {
                Log.Logger.Error(e.Message);
                Log.Logger.Error(e.StackTrace);
            }
        }

        // this was written to see what data paycom provides via the new hire api, but it appears that once a new hire is in the "New Hire" status
        // you can get their MBR record using their employee id, just like a normal employee, so probably will not ever really use the approch in this code
        /*
        public void List(List<string> args)
        {
            try
            {
                using (var paycomService = new PaycomRestService())
                {
                    var nhData = paycomService.GetNewHires().Result;
                    var json = JsonSerializer.Serialize(nhData, Json.DefaultSerializerOutputStyle);
                    Console.WriteLine(json);
                }
            }
            catch (Exception e)
            {
                Log.Logger.Error(e.Message);
                Log.Logger.Error(e.StackTrace);
            }
        }
        */

        public void View(List<string> args)
        {
            if (args == null || args.Count < 1)
            {
                Console.WriteLine("Usage: Paycom2.Tool.exe hire view <nhid>");
                return;
            }

            var nhid = args[0];

            try
            {
                using (var paycomService = new PaycomRestService())
                {
                    var nh = paycomService.GetNewHireAsync(Convert.ToInt32(nhid)).Result;
                    Console.WriteLine(JsonSerializer.Serialize(nh, Json.DefaultSerializerOutputStyle));
                }
            }
            catch (Exception e)
            {
                Log.Logger.Error(e.Message);
                Log.Logger.Error(e.StackTrace);
            }
        }
    }
}
