<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <OutputType>Exe</OutputType>
    <TargetFramework>net8.0</TargetFramework>
    <Configurations>Debug;Release;CoreOnly</Configurations>
    <Platforms>AnyCPU</Platforms>
  </PropertyGroup>

  <PropertyGroup Condition=" '$(Configuration)' == 'Release' ">
    <DebugType>none</DebugType>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="CommandLineParser" Version="2.9.1" />
    <PackageReference Include="Newtonsoft.Json" Version="13.0.3" />
    <PackageReference Include="Serilog" Version="4.2.0" />
    <PackageReference Include="Serilog.Sinks.Console" Version="6.0.0" />
    <PackageReference Include="Serilog.Sinks.File" Version="7.0.0" />
    <PackageReference Include="System.ServiceModel.Duplex" Version="6.0.0" />
    <PackageReference Include="System.ServiceModel.Http" Version="8.1.2" />
    <PackageReference Include="System.ServiceModel.NetTcp" Version="8.1.2" />
    <PackageReference Include="System.ServiceModel.Security" Version="6.0.0" />
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\Payroll.Shared\Payroll.Shared.csproj" />
    <ProjectReference Include="..\UltiPro.Shared\UltiPro.Shared.csproj" />
  </ItemGroup>

  <ItemGroup>
    <WCFMetadata Include="ConnectedServices" />
  </ItemGroup>

</Project>
