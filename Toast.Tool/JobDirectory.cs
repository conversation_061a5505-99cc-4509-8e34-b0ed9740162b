using Payroll.Shared;
using System;
using System.Collections.Generic;
using System.Linq;
using Serilog;
using Newtonsoft.Json;

namespace Toast.Tool
{
    public class JobDirectory : SortedDictionary<string, Dictionary<string, Job>>
    {
        private static HashSet<string> ManagerJobsById;
        private HashSet<string> LoadedRestaurantJobs = new HashSet<string>();

        static JobDirectory()
        {
            ManagerJobsById = Config.ManagerJobsById();
        }

        public static bool IsManagerJob(Reference job)
        {
            return IsManagerJob(job.Guid);
        }

        public static bool IsManagerJob(Job job)
        {
            return IsManagerJob(job.Guid);
        }

        private static bool IsManagerJob(Guid guid)
        {
            return ManagerJobsById.Contains(guid.ToString());
        }

        private bool GetJobsForRestaurant(ToastService service, RestaurantInfo restaurantInfo, out Dictionary<string, Job> jobs)
        {
            if (!LoadedRestaurantJobs.Contains(restaurantInfo.Id))
            {
                jobs = service.JobsByCodeForRestaurant(restaurantInfo).Result;
                Add(restaurantInfo.Code, jobs);
                LoadedRestaurantJobs.Add(restaurantInfo.Id);
            }

            return TryGetValue(restaurantInfo.Code, out jobs);
        }

        public (bool needToUpdateJobs, bool needToUpdateWages) MapJobsToToastEmployee(Payroll.Shared.Employee employee,
            Employee toastEmployee)
        {
            Log.Logger.Debug("MapJobsToToastEmployee1 Jobs: {jobs}", JsonConvert.SerializeObject(toastEmployee.JobReferences));
            Log.Logger.Debug("MapJobsToToastEmployee1 Wages: {wages}", JsonConvert.SerializeObject(toastEmployee.WageOverrides));

            // get this restaurant's job code map
            //if (!GetJobsForRestaurant(service, employee.PrimaryWorkLocation, out var jobsForRestaurant)
            //{
            ///    Log.Logger.Error("  Failed to locate jobs for restaurant {rid}", employee.PrimaryWorkLocation);
            //    return (updatedJobs: false, updatedWages: false);
            //}

            if (!TryGetValue(employee.PrimaryWorkLocation, out var jobsForRestaurant))
            {
                Log.Logger.Error("  Failed to locate jobs for restaurant {rid}", employee.PrimaryWorkLocation);
                return (needToUpdateJobs: false, needToUpdateWages: false);
            }

            var updatedJobs = false;
            var updatedWages = false;

            toastEmployee.WageOverrides.RemoveAll(x => {
                if (Config.SkipWageOverride(x))
                {
                    Log.Logger.Debug("Removing blacklisted job {id} from wage overrides...", x.JobReference.Guid);
                    return true;
                }
                return false;
            });

            // iterate over employee's jobs
            foreach (Payroll.Shared.Job job in employee.Jobs)
            {
                if (Config.SkipJob(job.Code))
                {
                    Log.Logger.Debug("  Skipping add/update of blacklisted job code '{id}'", job.Code);
                    continue;
                }

                // try to find in restaurant's job code map
                if (!jobsForRestaurant.TryGetValue(job.Code, out var toastJob))
                {
                    Log.Logger.Warning("Failed to find toast job, will not add {jid} to employee {eid}", job.Code, employee.ClockSeq);
                    continue;
                }

                // skip jobs which are deleted in Toast
                if (toastJob.Deleted)
                {
                    Log.Logger.Information("  Skipping deleted toast job '{name}', deleted on {date}",
                        toastJob.Title, toastJob.DeletedDate);
                    continue;
                }

                // skip jobs with an effective date in the future
                if (job.EffectiveDate > DateTime.Now)
                {
                    Log.Logger.Information("  Skipping job '{name}', effective date is {date}",
                        toastJob.Title, job.EffectiveDate?.ToShortDateString());
                    continue;
                }

                // skip jobs with an effective date in the distant past
                if (job.EffectiveDate?.Year < Config.HireYearMin)
                {
                    Log.Logger.Debug("  Skipping job '{name}', effective date {date} is before {min}",
                        toastJob.Title, job.EffectiveDate?.ToShortDateString(), Config.HireYearMin);
                    continue;
                }

                // is this job already present, if not we should add.
                var jobReference = new Reference() { Guid = toastJob.Guid, EntityType = "RestaurantJob" };

                if (!toastEmployee.JobReferences.Any(x => x.Guid == toastJob.Guid))
                {
                    Log.Logger.Information("  Adding job... {jid} ({guid})", job.Code,
                        toastJob.Guid);
                    toastEmployee.JobReferences.Add(jobReference);
                    updatedJobs = true;
                }

                // manager salaries are considered sensitive, so they are not loaded into toast
                if (IsManagerJob(toastJob))
                {
                    Log.Logger.Debug("  Skipping wage override for manager job '{name}'", toastJob.Code);
                    continue;
                }

                // if the default wage is not their desired wage, add a wage override...
                if (toastJob.DefaultWage != job.Rate)
                {
                    var fndWageOverride = false;
                    var jobRate = Convert.ToDouble(job.Rate);
                    foreach (var wage in toastEmployee.WageOverrides)
                    {
                        if (toastJob.Guid == wage.JobReference.Guid)
                        {
                            if (wage.Wage != jobRate)
                            {
                                Log.Logger.Warning(
                                    "  Changing job rate: current {code} wage: {wrate}, new wage in import: {iwage}, default: {dwage}",
                                    job.Code, wage.Wage, job.Rate, toastJob.DefaultWage);
                                wage.Wage = jobRate;
                                updatedWages = true;
                            }
                            else
                            {
                                Log.Logger.Debug(
                                    "  Wage override rate: {wrate} matches import wage rate, no changes required, default: {dwage}",
                                    wage.Wage, toastJob.DefaultWage);
                            }

                            fndWageOverride = true;
                            break;
                        }
                    }

                    if (!fndWageOverride)
                    {
                        var wo = new WageOverride()
                        {
                            Wage = jobRate,
                            JobReference = jobReference
                        };

                        toastEmployee.WageOverrides.Add(wo);
                        updatedWages = true;

                        Log.Logger.Information("  Added wage override for job {code}, import wage rate: {twage}, effective {date}, to employee {eid}",
                            job.Code, job.Rate, job.EffectiveDate?.ToShortDateString(), employee.ClockSeq);
                    }
                }
            }

            Log.Logger.Debug("MapJobsToToastEmployee2 Jobs: {jobs}", JsonConvert.SerializeObject(toastEmployee.JobReferences));
            Log.Logger.Debug("MapJobsToToastEmployee2 Wages: {wages}", JsonConvert.SerializeObject(toastEmployee.WageOverrides));

            var reverseCache = jobsForRestaurant.Values.ToDictionary(x => x.Guid, x => x.Code);

            // iterate over employee's job references
            foreach (Reference jobRef in toastEmployee.JobReferences)
            {
                if (Config.SkipJob(jobRef.Guid.ToString()))
                {
                    Log.Logger.Information("  Skipping deletion of blacklisted job '{guid}'", jobRef.Guid);
                    continue;
                }

                // the jobRef.ExternalId is null here, so we need to look it up in the reverse cache
                var jobRefCode = reverseCache.TryGetValue(jobRef.Guid, out var code) ? code : null;
                if (jobRefCode == null)
                {
                    // this can happen if the job was deleted in Toast
                    Log.Logger.Debug("Failed to find toast job, will not remove {code} from {eid}", jobRef.Guid, employee.ClockSeq);
                    continue;
                }

                var fnd = false;
                foreach (var job in employee.Jobs)
                {
                    if (jobRefCode == job.Code)
                    {
                        fnd = true;
                        break;
                    }
                }

                if (!fnd)
                {
                    if (ExecutionMode.Execute == Config.RemoveJobsMode())
                    {
                        Log.Logger.Information("Job not found in import, removing job {jid} from emp {eid}/{lname}...",
                               jobRefCode, employee.Id, employee.LastName);

                        toastEmployee.JobReferences.Remove(jobRef);
                        updatedJobs = true;
                    }

                    if (ExecutionMode.DryRun == Config.RemoveJobsMode())
                    {
                        Log.Logger.Information("Job not found in import, would remove job {jid} from emp {eid}/{lname}...",
                               jobRefCode, employee.Id, employee.LastName);
                    }
                }
            }

            // now delete any wage overrides for jobs that no longer exist
            updatedWages |= toastEmployee.WageOverrides.RemoveAll(wo => 
            {
                var shouldRemove = !toastEmployee.JobReferences.Any(x => x.Guid == wo.JobReference.Guid);
                if (shouldRemove)
                {
                    Log.Logger.Information("  Deleting wage reference... {wage} ({guid}), from employee {eid}", 
                        wo.Wage, wo.JobReference.Guid, employee.ClockSeq);
                }
                return shouldRemove;
            }) > 0;

            return (updatedJobs, updatedWages);
        }
    }
}