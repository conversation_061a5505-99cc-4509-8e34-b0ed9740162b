using Newtonsoft.Json;
using Payroll.Shared;
using Serilog;
using System;
using System.Collections.Generic;
using System.Globalization;
using System.Linq;
using System.Net;
using System.Net.Http;
using System.Net.Http.Headers;
using System.Text;
using System.Text.Json;
using System.Threading;
using System.Threading.Tasks;

namespace Toast.Tool
{
    // map of restaurant codes to a dictionary of emp external ids to employee records
    public class EmployeeDirectory : Dictionary<string, Employee>
    {
    }

    // map of restaurant codes to a dictionary of emp external ids to employee records
    public class EmployeeDirectoryByLocation : SortedDictionary<string, EmployeeDirectory>
    {
    }

    public class ToastService : IDisposable
    {
        private static readonly string Host;
        private static readonly string GroupId;
        private readonly int ApiDelayInSeconds;

        private uint TotalApiRequestsMade { get; set; }
        private DateTime StartTime { get; set; }
        private AuthenticationHeaderValue AuthenticationToken { get; set; }
        private static AuthorizationBody AuthorizationBody { get; }

        static ToastService()
        {
            Host = Setting.Get(Config.SettingSection, "host");
            GroupId = Setting.Get(Config.SettingSection, "groupid");

            // cache authorization body
            var clientId = Setting.Get(Config.SettingSection, "clientid");
            var secret = Setting.Get(Config.SettingSection, "secret");

            AuthorizationBody = new AuthorizationBody()
            {
                ClientId = clientId,
                ClientSecret = secret,
                UserAccessType = "TOAST_MACHINE_CLIENT"
            };
        }

        public ToastService()
        {
            TotalApiRequestsMade = 0;
            StartTime = DateTime.Now;
            ApiDelayInSeconds = Config.ApiDelayInSeconds();
        }

        public Uri EndpointForPath(string path)
        {
            return new Uri($"https://{Host}/{path}");
        }

        public bool IsManager(Employee employee)
        {
            if (employee == null || employee.JobReferences == null) return false;

            foreach (var job in employee.JobReferences)
            {
                if (JobDirectory.IsManagerJob(job)) return true;
            }

            return false;
        }

        private void LogApiRequest()
        {
            TotalApiRequestsMade++;
            if (ApiDelayInSeconds > 0) Thread.Sleep(ApiDelayInSeconds * 1000);
        }

        public async Task Authenticate()
        {
            var endpoint = EndpointForPath("authentication/v1/authentication/login");

            using (var client = new HttpClient())
            {
                Log.Logger.Debug("Fetching authentication token via {url}", endpoint);

                var json = JsonConvert.SerializeObject(AuthorizationBody);
                Log.Logger.Debug("Authentication body is {json}", json);
                var content = new StringContent(json, Encoding.UTF8, "application/json");

                var response = await client.PostAsync(endpoint, content);
                LogApiRequest();

                var responseBody = await response.Content.ReadAsStringAsync();
                Log.Logger.Debug("Response body is {b}", responseBody);
                var authResponse = JsonConvert.DeserializeObject<AuthResponse>(responseBody);

                var authToken = authResponse.AuthToken;
                AuthenticationToken = new AuthenticationHeaderValue("Bearer", authToken.AccessToken);
                Log.Logger.Debug("Authentication token expires {when} (secs)", authToken.ExpiresIn);
            }
        }

        private async Task<HttpClient> HttpClientForRestaurant(string restaurantId)
        {
            if (AuthenticationToken == null)
                await Authenticate();

            var client = new HttpClient();
            client.DefaultRequestHeaders.Authorization = AuthenticationToken;
            client.DefaultRequestHeaders.Add("Toast-Restaurant-External-ID", restaurantId);
            Log.Logger.Debug("Toast Restaurant id {rid}", restaurantId);
            return client;
        }

        private async Task<List<Job>> JobsForRestaurant(string restaurantId)
        {
            using (var client = await HttpClientForRestaurant(restaurantId))
            {
                var endpoint = EndpointForPath($"labor/v1/jobs");
                Log.Logger.Debug("Fetching jobs via {url}", endpoint);
                var response = await client.GetStringAsync(endpoint);
                LogApiRequest();
                return JsonConvert.DeserializeObject<List<Job>>(response);
            }
        }

        public async Task<Dictionary<string, Job>> JobsByCodeForRestaurant(RestaurantInfo restaurant)
        {
            var jobList = await JobsForRestaurant(restaurant.Id);
            Log.Logger.Debug("Found {x} jobs for restaurant {rid}", jobList.Count, restaurant.Code);

            var jobsByCode = new Dictionary<string, Job>();
            foreach (var job in jobList)
            {
                if (string.IsNullOrEmpty(job.Code))
                {
                    Log.Logger.Debug("Restaurant {rid}, skipping job \"{name}\" - No job code. Guid: {gid}",
                        restaurant.Code, job.Title, job.Guid);
                    continue;
                }

                if (job.Deleted)
                {
                    Log.Logger.Debug("Restaurant {rid}, skipping deleted job \"{name}\". Guid: {gid}",
                        restaurant.Code, job.Title, job.Guid);
                    continue;
                }

                if (jobsByCode.ContainsKey(job.Code))
                {
                    Log.Logger.Warning("Restaurant {rid}, Skipping Job \"{name}\" - Duplicate job code {eid}",
                        restaurant.Code, job.Title, job.Code);
                    continue;
                }

                jobsByCode.Add(job.Code, job);
            }

            return jobsByCode;
        }

        public async Task<Dictionary<Guid, Job>> JobsByIdForRestaurant(string restaurantId)
        {
            var jobList = await JobsForRestaurant(restaurantId);
            return jobList.ToDictionary(x => x.Guid);
        }

        public async Task<JobDirectory> JobsByRestaurant()
        {
            var jobMap = new JobDirectory();
            var restaurants = Config.RestaurantsById();

            foreach (var (_, restaurantInfo) in restaurants)
            {
                var jobs = await JobsByCodeForRestaurant(restaurantInfo);
                jobMap.Add(restaurantInfo.Code, jobs);
            }

            return jobMap;
        }
        public async Task<bool> Deposits(string restaurantId, DateTime date)
        {
            using (var client = await HttpClientForRestaurant(restaurantId))
            {
                var dateStr = date.ToString("yyyyMMdd");
                var endpoint = EndpointForPath($"cashmgmt/v1/deposits?businessDate={dateStr}");
                Log.Logger.Debug("Fetching deposits via {url}", endpoint);

                var response = await client.GetAsync(endpoint);
                LogApiRequest();
                var responseBody = await response.Content.ReadAsStringAsync();

                if (!response.IsSuccessStatusCode)
                {
                    Log.Logger.Error("Failed to fetch deposits: {msg}", response.ReasonPhrase);
                    Log.Logger.Error(responseBody);
                    return false;
                }

                Log.Logger.Information(responseBody);
                return true;

            }
        }

        public async Task<bool> AddEmployee(string restaurantId, Employee employee)
        {
            using (var client = await HttpClientForRestaurant(restaurantId))
            {
                Employee addedEmployee = null;
                var endpoint = EndpointForPath($"labor/v1/employees");
                Log.Logger.Debug("Posting new employee via {url}", endpoint);

                var body = JsonConvert.SerializeObject(employee);
                var content = new StringContent(body, Encoding.UTF8, "application/json");

                var response = await client.PostAsync(endpoint, content);
                LogApiRequest();
                string responseBody = await response.Content.ReadAsStringAsync();

                if (!response.IsSuccessStatusCode)
                {
                    Log.Logger.Error("Failed to add employee: {msg}", response.ReasonPhrase);
                    Log.Logger.Error(responseBody);
                    Log.Logger.Debug(JsonConvert.SerializeObject(employee, Formatting.Indented));
                    return false;
                }

                Log.Logger.Debug(responseBody);
                addedEmployee = JsonConvert.DeserializeObject<Employee>(responseBody);

                return true;
            }
        }

        private async Task<bool> PatchEmployee(string restaurantId, Guid eid, string body)
        {
            using (var client = await HttpClientForRestaurant(restaurantId))
            {
                Employee addedEmployee = null;
                var endpoint = EndpointForPath($"labor/v1/employees/{eid}");
                Log.Logger.Debug("Updating employee via {url}", endpoint);

                var content = new StringContent(body, Encoding.UTF8, "application/json");
                var msg = new HttpRequestMessage()
                {
                    Content = content,
                    Method = HttpMethod.Patch,
                    RequestUri = endpoint
                };

                var response = await client.SendAsync(msg);
                LogApiRequest();
                string responseBody = await response.Content.ReadAsStringAsync();

                if (!response.IsSuccessStatusCode)
                {
                    Log.Logger.Error("Failed to update employee: {msg}", response.ReasonPhrase);
                    Log.Logger.Error(responseBody);
                    return false;
                }

                Log.Logger.Debug(responseBody);
                addedEmployee = JsonConvert.DeserializeObject<Employee>(responseBody);

                return true;
            }
        }

        public async Task<bool> UpdatePasscode(string restaurantId, Employee employee)
        {
            var passcodeUpdate = new PasscodeUpdate()
            {
                Passcode = employee.Passcode
            };

            var body = JsonConvert.SerializeObject(passcodeUpdate);
            return await PatchEmployee(restaurantId, employee.Guid, body);
        }

        public async Task<bool> UpdateEmployee(string restaurantId, Employee employee)
        {
            var employeeUpdate = new EmployeeUpdate()
            {
                FirstName = employee.FirstName,
                LastName = employee.LastName
            };

            var body = JsonConvert.SerializeObject(employeeUpdate);
            return await PatchEmployee(restaurantId, employee.Guid, body);
        }

        public async Task<bool> DeleteEmployee(string restaurantId, Employee employee)
        {
            using (var client = await HttpClientForRestaurant(restaurantId))
            {
                Employee deletedEmployee = null;
                var endpoint = EndpointForPath($"labor/v1/employees/{employee.Guid}");
                Log.Logger.Debug("Deleting employee via {url}", endpoint);

                var body = JsonConvert.SerializeObject(employee);
                var content = new StringContent(body, Encoding.UTF8, "application/json");

                var response = await client.DeleteAsync(endpoint);
                LogApiRequest();
                string responseBody = await response.Content.ReadAsStringAsync();

                if (!response.IsSuccessStatusCode)
                {
                    Log.Logger.Error("Failed to delete employee: {msg}", response.ReasonPhrase);
                    Log.Logger.Error(responseBody);
                    Log.Logger.Error(JsonConvert.SerializeObject(employee, Formatting.Indented));
                    return false;
                }

                Log.Logger.Debug(responseBody);
                deletedEmployee = JsonConvert.DeserializeObject<Employee>(responseBody);

                return true;
            }
        }

        public async Task<Employee> EmployeeInfo(string restaurantId, string guid)
        {
            using (var client = await HttpClientForRestaurant(restaurantId))
            {
                var endpoint = EndpointForPath($"labor/v1/employees/{guid}");
                Log.Logger.Debug("Fetching employee info via {url}", endpoint);
                var response = await client.GetAsync(endpoint);
                LogApiRequest();
                var responseBody = await response.Content.ReadAsStringAsync();

                if (!response.IsSuccessStatusCode)
                {
                    Log.Logger.Error("Failed to fetch employee: {msg}", response.ReasonPhrase);
                    Log.Logger.Error(responseBody);
                    return new Employee();
                }

                return JsonConvert.DeserializeObject<Employee>(responseBody);
            }
        }

        public async Task<bool> UpdateJobs(string restaurantId, Employee employee)
        {
            using (var client = await HttpClientForRestaurant(restaurantId))
            {
                var endpoint = EndpointForPath($"labor/v1/employees/{employee.Guid}/jobs");
                Log.Logger.Debug("Updating employee jobs via {url}", endpoint);

                var body = JsonConvert.SerializeObject(employee.JobReferences);
                Log.Logger.Debug(body);
                var content = new StringContent(body, Encoding.UTF8, "application/json");

                var msg = new HttpRequestMessage()
                {
                    Content = content,
                    Method = HttpMethod.Put,
                    RequestUri = endpoint
                };

                var response = await client.SendAsync(msg);
                LogApiRequest();
                string responseBody = await response.Content.ReadAsStringAsync();

                if (!response.IsSuccessStatusCode)
                {
                    Log.Logger.Error("Failed to update employee jobs: {msg}", response.ReasonPhrase);
                    Log.Logger.Error(responseBody);
                    return false;
                }

                Log.Logger.Debug(responseBody);
                return true;
            }
        }

        public async Task<bool> UpdateWageOverrides(string restaurantId, Employee employee)
        {
            using (var client = await HttpClientForRestaurant(restaurantId))
            {
                Employee updatedEmployee = null;
                var endpoint = EndpointForPath($"labor/v1/employees/{employee.Guid}/wageOverrides");
                Log.Logger.Debug("Updating employee wage overrides via {url}", endpoint);

                var body = JsonConvert.SerializeObject(employee.WageOverrides);
                var content = new StringContent(body, Encoding.UTF8, "application/json");

                var msg = new HttpRequestMessage()
                {
                    Content = content,
                    Method = HttpMethod.Put,
                    RequestUri = endpoint
                };

                var response = await client.SendAsync(msg);
                LogApiRequest();
                var responseBody = await response.Content.ReadAsStringAsync();

                if (!response.IsSuccessStatusCode)
                {
                    Log.Logger.Error("Failed to update employee wage overrides: {msg}", response.ReasonPhrase);
                    Log.Logger.Error(responseBody);
                    return false;
                }

                Log.Logger.Debug(responseBody);
                updatedEmployee = JsonConvert.DeserializeObject<Employee>(responseBody);

                return true;
            }
        }

        public async Task<Dictionary<Guid, Employee>> EmployeesForRestaurant(string restaurantId)
        {
            using (var client = await HttpClientForRestaurant(restaurantId))
            {
                var endpoint = EndpointForPath($"labor/v1/employees");
                Log.Logger.Debug("Fetching employees via {url}", endpoint);
                var response = await client.GetStringAsync(endpoint);
                LogApiRequest();
                var empList = JsonConvert.DeserializeObject<List<Employee>>(response);
                return empList.ToDictionary(x => x.Guid);
            }
        }

        public async Task<List<KeyValuePair<RestaurantInfo, List<Employee>>>> AllEmployeesAllRestaurants()
        {
            SortedDictionary<string, RestaurantInfo> restaurants = Config.RestaurantsByCode();
            var allEmployees = new List<KeyValuePair<RestaurantInfo, List<Employee>>>();

            foreach (var restaurant in restaurants)
            {

                try
                {
                    using (var client = await HttpClientForRestaurant(restaurant.Value.Id))
                    {
                        var endpoint = EndpointForPath($"labor/v1/employees");
                        Log.Logger.Debug("Fetching employees via {url} for {key}", endpoint, restaurant.Key);
                        var response = await client.GetAsync(endpoint);
                        var json = await response.Content.ReadAsStringAsync();

                        if (!response.IsSuccessStatusCode)
                        {
                            Log.Logger.Error(json);
                            Log.Logger.Error(response.ReasonPhrase);
                            return allEmployees;
                        }

                        LogApiRequest();
                        var restaurantEmployees = JsonConvert.DeserializeObject<List<Employee>>(json);
                        var sortedEmployees = restaurantEmployees.OrderBy(x => x.LastName).ThenBy(x => x.FirstName).ToList();
                        allEmployees.Add(new KeyValuePair<RestaurantInfo, List<Employee>>(restaurant.Value, sortedEmployees));
                    }
                }
                catch (Exception e)
                {
                    Log.Logger.Error("Error querying restaurant: {rd}", restaurant.Key);
                    Log.Logger.Error(e.Message);
                    Log.Logger.Error(e.GetType().Name);
                    Log.Logger.Error(e.StackTrace);
                }
            }

            return allEmployees;
        }

        public async Task<List<KeyValuePair<RestaurantInfo, SortedDictionary<string, Employee>>>> AllEmployeesAllRestaurantsByExternalId()
        {
            var allEmployees = await AllEmployeesAllRestaurants();
            var byExternalId = new List<KeyValuePair<RestaurantInfo, SortedDictionary<string, Employee>>>();
            foreach (var restaurant in allEmployees)
            {
                var employees = new SortedDictionary<string, Employee>();
                foreach (var employee in restaurant.Value)
                {
                    if (string.IsNullOrEmpty(employee.ExternalEmployeeId))
                    {
                        Log.Logger.Debug("Skipping {name} - {email} - {date}, no external id...",
                            employee.FirstName + " " + employee.LastName, employee.Email, employee.CreatedDate);
                        continue;
                    }

                    // normal state of things...
                    if (!employees.ContainsKey(employee.ExternalEmployeeId))
                    {
                        employees.Add(employee.ExternalEmployeeId, employee);
                        continue;
                    }

                    // duplicate handling
                    var currentEmp = employees[employee.ExternalEmployeeId];

                    if (!DateTime.TryParseExact(currentEmp.CreatedDate, "yyyy-MM-ddTHH:mm:ss.FFF+0000", CultureInfo.CurrentCulture, DateTimeStyles.AssumeLocal, out DateTime currentEmpDate))
                    {
                        Log.Logger.Warning($"{currentEmp.ExternalEmployeeId} - failed to parse date {currentEmp.CreatedDate} for duplicate, so skipping keep-oldest logic...");
                        continue;
                    }

                    if (!DateTime.TryParseExact(employee.CreatedDate, "yyyy-MM-ddTHH:mm:ss.FFF+0000", CultureInfo.CurrentCulture, DateTimeStyles.AssumeLocal, out DateTime employeeDate))
                    {
                        Log.Logger.Warning($"{employee.ExternalEmployeeId} - failed to parse date {employee.CreatedDate} for duplicate, so skipping keep-oldest logic...");
                        continue;
                    }

                    if (currentEmp.Deleted && !employee.Deleted)
                    {
                        Log.Logger.Warning($"{employee.ExternalEmployeeId} - dupe, prefer ACTIVE rec - was: {currentEmpDate.ToString("M/d/yy")} {currentEmp.Email}, now: {employeeDate.ToString("M/d/yy")} {employee.Email}");
                        employees[employee.ExternalEmployeeId] = employee;
                        continue;
                    }

                    if (currentEmpDate > employeeDate)
                    {
                        if (!(currentEmp.Deleted && employee.Deleted))
                            Log.Logger.Warning($"{employee.ExternalEmployeeId} - dupe, prefer OLDER rec - was: {currentEmpDate.ToString("M/d/yy")} {currentEmp.Email}, now: {employeeDate.ToString("M/d/yy")} {employee.Email}");
                        employees[employee.ExternalEmployeeId] = employee;
                    }
                }

                byExternalId.Add(new KeyValuePair<RestaurantInfo, SortedDictionary<string, Employee>>(restaurant.Key, employees));
            }

            return byExternalId;
        }

        public async Task<EmployeeDirectoryByLocation> EmployeesByRestaurant()
        {
            var employeeMap = new EmployeeDirectoryByLocation();
            var restaurants = Config.RestaurantsById();

            foreach (var restaurant in restaurants)
            {
                var employees = new EmployeeDirectory();
                // VERY IMPORTANT NOTE
                // DUPLICATE EXTERNAL IDS ACROSS RESTAURANTS
                // WILL HAPPEN. THIS CANNOT BE MOVED OUTSIDE OF THIS LOOP. MIGHT BE BEST TO JUST DELETE ENTIRELY
                var duplicateExternalIds = new List<string>();

                try
                {
                    using (var client = await HttpClientForRestaurant(restaurant.Key))
                    {
                        var endpoint = EndpointForPath($"labor/v1/employees");
                        Log.Logger.Debug("Fetching employees via {url} for {key}", endpoint, restaurant.Key);
                        var response = await client.GetAsync(endpoint);
                        var json = await response.Content.ReadAsStringAsync();

                        if (!response.IsSuccessStatusCode)
                        {
                            Log.Logger.Error(json);
                            Log.Logger.Error(response.ReasonPhrase);
                            return employeeMap;
                        }

                        LogApiRequest();

                        var empList = JsonConvert.DeserializeObject<List<Employee>>(json);

                        foreach (var emp in empList)
                        {
                            if (emp.Deleted)
                            {
                                Log.Logger.Debug("Skipping deleted employee {fname} {lname}. Guid: {gid}, Rid: {rid}",
                                    emp.FirstName, emp.LastName, emp.Guid, restaurant.Value.Code);
                                continue;
                            }

                            if (string.IsNullOrEmpty(emp.ExternalEmployeeId))
                            {
                                // all of these filters below help keep the log noise down in normal situations
                                if (emp.JobReferences == null || emp.JobReferences.Count == 0)
                                {
                                    Log.Logger.Debug(
                                        "Skipping employee {fname} {lname}, has no jobs. Guid: {gid}}",
                                        emp.FirstName, emp.LastName, emp.Guid);
                                    continue;
                                }

                                if (IsManager(emp))
                                {
                                    Log.Logger.Debug("Skipping {fname} {lname}, has manager job. Guid: {gid}, Rid: {rid}",
                                        emp.FirstName, emp.LastName, emp.Guid, restaurant.Value.Code);
                                    continue;
                                }

                                // only print this warning if a real employee record, but only print once...
                                if (!string.IsNullOrEmpty(emp.LastName))
                                {
                                    Log.Logger.Warning(
                                        "Skipping {fname} {lname}, no external employee id, Email: {eid}",
                                        emp.FirstName, emp.LastName, emp.Email);
                                    //Log.Logger.Debug(JsonConvert.SerializeObject(emp.JobReferences));
                                }

                                continue;
                            }

                            if (employees.ContainsKey(emp.ExternalEmployeeId))
                            {
                                Log.Logger.Fatal(
                                    "Skipping {fname} {lname}, duplicative external employee id: {eid}, Guid: {gid}, Rid: {rid}",
                                    emp.FirstName, emp.LastName, emp.ExternalEmployeeId, emp.Guid, restaurant.Value.Code);

                                // add this to our blacklist, it's too dangerous to process any changes
                                // to employees with duplicate emp.ExternalEmployeeId's
                                duplicateExternalIds.Add(emp.ExternalEmployeeId);

                                continue;
                            }

                            employees.Add(emp.ExternalEmployeeId, emp);
                        }
                    }
                }
                catch (Exception e)
                {
                    Log.Logger.Error("Error querying restaurant: {rd}", restaurant.Key);
                    Log.Logger.Error(e.Message);
                    Log.Logger.Error(e.GetType().Name);
                    Log.Logger.Error(e.StackTrace);
                }

                foreach (var id in duplicateExternalIds)
                {
                    // we could add the same id to this queue more than once, so be careful here...
                    if (!employees.ContainsKey(id)) continue;

                    var emp = employees[id];
                    Log.Logger.Fatal("Removing {fname} {lname}, blacklisted external employee id: {eid}, guid: {gid}",
                        emp.FirstName, emp.LastName, emp.ExternalEmployeeId, emp.Guid);

                    employees.Remove(id);
                }

                employeeMap.Add(restaurant.Value.Code, employees);
            }

            return employeeMap;
        }

        public async Task<List<Restaurant>> Restaurants()
        {
            var restaurant = Config.DefaultRestaurantId();

            if (restaurant == null)
            {
                Log.Logger.Fatal("No default restaurant location found in settings file.");
                return new List<Restaurant>();
            }

            using (var client = await HttpClientForRestaurant(restaurant.Id))
            {
                var endpoint = EndpointForPath($"restaurants/v1/groups/{GroupId}/restaurants");
                Log.Logger.Debug("Fetching restaurants via {url}", endpoint);
                var response = await client.GetStringAsync(endpoint);
                LogApiRequest();
                return JsonConvert.DeserializeObject<List<Restaurant>>(response);
            }
        }

        public async Task<Restaurant> RestaurantInfo(string restaurantId)
        {
            using (var client = await HttpClientForRestaurant(restaurantId))
            {
                var endpoint = EndpointForPath($"restaurants/v1/restaurants/{restaurantId}");
                Log.Logger.Debug("Fetching restaurant info via {url}", endpoint);
                var response = await client.GetStringAsync(endpoint);
                LogApiRequest();
                return JsonConvert.DeserializeObject<Restaurant>(response);
            }
        }

        public async Task<List<Client>> Clients()
        {
            Log.Logger.Debug("Client list generation...");
            using (var client = new HttpClient())
            {
                if (AuthenticationToken == null) await Authenticate();
                Log.Logger.Debug("About to use authorization token");
                client.DefaultRequestHeaders.Authorization = AuthenticationToken;

                var endpoint = EndpointForPath($"partners/v1/restaurants");
                Log.Logger.Debug("Fetching restaurant info via {url}", endpoint);
                var response = await client.GetStringAsync(endpoint);

                LogApiRequest();
                return JsonConvert.DeserializeObject<List<Client>>(response);
            }
        }

        public async Task<List<PunchPair>> TimePunchesForRestaurantUsingStartEndDate(string restaurantId,
            string restaurantAbbrev, DateTime startDate, DateTime endDate)
        {
            var punches = new List<PunchPair>();
            var tipScheme = Config.TipSchemeForRestaurant(restaurantAbbrev);
            Log.Logger.Information("Using tip scheme: {scheme}, for restaurant code: {code}", tipScheme, restaurantAbbrev);

            try
            {
                // load our employee map and job map
                var employeeMap = await EmployeesForRestaurant(restaurantId);
                var jobMap = await JobsByIdForRestaurant(restaurantId);

                using (var client = await HttpClientForRestaurant(restaurantId))
                {
                    var startDateStr = startDate.ToString("yyyy-MM-ddTHH:mm:ss");
                    var endDateStr = endDate.ToString("yyyy-MM-ddTHH:mm:ss");

                    var endpoint =
                        EndpointForPath(
                            $"labor/v1/timeEntries?startDate={startDateStr}.000-0000&endDate={endDateStr}.000-0000");
                    Log.Logger.Debug("Fetching punches via {url}", endpoint);

                    var response = await client.GetStringAsync(endpoint);
                    LogApiRequest();

                    var timeEntries = JsonConvert.DeserializeObject<List<TimeEntry>>(response);
                    Log.Logger.Debug(response);

                    if (timeEntries.Count == 0)
                    {
                        Log.Logger.Warning("No punches found for time period.");
                    }

                    foreach (var entry in timeEntries)
                    {
                        if (TryTimeEntryToPunchPair(entry, restaurantId, restaurantAbbrev,
                            employeeMap, jobMap, tipScheme, out var pp))
                        {
                            punches.Add(pp);
                        }
                    }
                }
            }
            catch (Exception e)
            {
                Log.Logger.Error(e.Message);
                Log.Logger.Error(e.GetType().Name);
                Log.Logger.Error(e.StackTrace);
            }

            return punches;
        }

        public async Task<List<PunchPair>> TimePunchesForRestaurantUsingBusinessDate(string restaurantId,
            string restaurantAbbrev, DateTime startDate, DateTime endDate)
        {
            var punches = new List<PunchPair>();
            var tipScheme = Config.TipSchemeForRestaurant(restaurantAbbrev);
            Log.Logger.Information("Using tip scheme: {scheme}, for restaurant code: {code}", tipScheme, restaurantAbbrev);

            try
            {
                // load our employee map and job map
                var employeeMap = await EmployeesForRestaurant(restaurantId);
                Dictionary<Guid, Job> jobMap = await JobsByIdForRestaurant(restaurantId);

                using (var client = await HttpClientForRestaurant(restaurantId))
                {
                    var spanOfTime = endDate - startDate;
                    for (int day = 0; day <= spanOfTime.Days; day++)
                    {
                        var businessDate = startDate.AddDays(day);
                        var businessDateStr = businessDate.ToString("yyyyMMdd");

                        var endpoint = EndpointForPath($"labor/v1/timeEntries?businessDate={businessDateStr}");
                        Log.Logger.Information("Fetching punches for day {day} via {url}", day, endpoint);

                        var response = await client.GetStringAsync(endpoint);
                        LogApiRequest();

                        var timeEntries = JsonConvert.DeserializeObject<List<TimeEntry>>(response);
                        Log.Logger.Debug(JsonConvert.SerializeObject(timeEntries, Formatting.Indented));

                        if (timeEntries.Count == 0)
                        {
                            Log.Logger.Warning("No punches found for day {day}", day);
                            continue;
                        }

                        foreach (var entry in timeEntries)
                        {
                            if (TryTimeEntryToPunchPair(entry, restaurantId, restaurantAbbrev,
                                employeeMap, jobMap, tipScheme, out var pp))
                            {
                                punches.Add(pp);
                            }
                        }
                    }
                }
            }
            catch (Exception e)
            {
                Log.Logger.Error(e.Message);
                Log.Logger.Error(e.GetType().Name);
                Log.Logger.Error(e.StackTrace);
            }

            return punches;
        }

        private bool TryTimeEntryToPunchPair(TimeEntry entry, string restaurantId, string restaurantAbbrev,
            Dictionary<Guid, Employee> employeeMap, Dictionary<Guid, Job> jobMap, string tipScheme, out PunchPair pp)
        {
            pp = null;

            if (entry.Deleted)
            {
                return false;
            }

            if (!employeeMap.TryGetValue(entry.EmployeeReference.Guid, out Employee employee))
            {
                Log.Logger.Warning("Failed to find employee {eid} at restaurant {rid}, skipping punch",
                    entry.EmployeeReference.Guid, restaurantId);
                return false;
            }

            if (entry.JobReference == null)
            {
                Log.Logger.Debug(
                    "Time entry {guid} missing job reference at restaurant {rid}, skipping punch for {fname} {lname} on {date}",
                    entry.Guid, restaurantId, employee.FirstName, employee.LastName, entry.InDate);
                return false;
            }

            if (!jobMap.TryGetValue(entry.JobReference.Guid, out Job job))
            {
                Log.Logger.Warning(
                    "Failed to find job {guid} at restaurant {rid}, skipping punch for {fname} {lname} on {date}",
                    entry.JobReference.Guid, restaurantId, employee.FirstName, employee.LastName,
                    entry.InDate);
                return false;
            }

            if (string.IsNullOrEmpty(employee.ExternalEmployeeId))
            {
                Log.Logger.Debug(
                    "Null ExternalEmployeeId, skipping punch for {fname} {lname} ({email}) on {date}",
                    employee.FirstName, employee.LastName, employee.Email, entry.InDate);
                return false;
            }

            if (entry.NonCashGratuityServiceCharges > 0 || entry.CashGratuityServiceCharges > 0)
            {
                Log.Logger.Information("Employee: {eid}, has gratuity totals: {non} + {cash} = {gratuity}",
                    employee.ExternalEmployeeId, entry.NonCashGratuityServiceCharges, entry.CashGratuityServiceCharges,
                    entry.NonCashGratuityServiceCharges + entry.CashGratuityServiceCharges);
            }

            var description = $"dct={entry.DeclaredCashTips ?? 0}, nct={entry.NonCashTips ?? 0}, ngs={entry.NonCashGratuityServiceCharges ?? 0}, cgs={entry.CashGratuityServiceCharges ?? 0}";

            pp = new PunchPair()
            {
                Id = entry.EmployeeReference.Guid.ToString(),
                ClockSeq = employee.ExternalEmployeeId,
                Hours = entry.RegularHours,
                Overtime = entry.OvertimeHours,
                Description = description,
                JobCode = job.Code,
                CashTip = (entry.DeclaredCashTips ?? 0) + (entry.NonCashTips ?? 0)
                    + (entry.NonCashGratuityServiceCharges ?? 0) + (entry.CashGratuityServiceCharges ?? 0),
                Location = restaurantAbbrev,
                Sales = entry.NonCashSales + entry.CashSales,
                FirstName = employee.FirstName,
                LastName = employee.LastName,
                //TimeIn = entry.BusinessDate
            };

            if (job.DefaultWage.HasValue)
                pp.Rate = job.DefaultWage.Value;

            // tip scheme allows you to override the default above (which is called "total")
            // the declared scheme means just the declared cash tips as seen below
            if (tipScheme == "declared")
            {
                Log.Logger.Debug("Employee: {eid}, Declared Tip: {dtip}, Total Tip: {ttip}",
                    employee.ExternalEmployeeId, entry.DeclaredCashTips, entry.DeclaredCashTips + entry.NonCashTips);
                pp.CashTip = entry.DeclaredCashTips ?? 0 + entry.CashGratuityServiceCharges ?? 0;
                pp.NonCashTip = entry.NonCashTips ?? 0 + entry.NonCashGratuityServiceCharges ?? 0;
                pp.ExceptionPay = entry.NonCashGratuityServiceCharges ?? 0; // Keep this amount separate in case we need to use it for a customer.
            }

            if (DateTime.TryParseExact(entry.BusinessDate, "yyyyMMdd",
                CultureInfo.CurrentCulture, DateTimeStyles.AssumeLocal, out DateTime businessDate))
            {
                pp.Date = businessDate;
            }
            else
            {
                Log.Logger.Warning("Failed to parse BusinessDate of {date}", entry.BusinessDate);
            }

            if (LoggedDateParsing($"In Date for Employee {employee.ExternalEmployeeId}",
                entry.InDate, out var inDate))
                pp.TimeIn = inDate;

            if (LoggedDateParsing($"Out Date for Employee {employee.ExternalEmployeeId}",
                entry.OutDate, out var outDate))
                pp.TimeOut = outDate;

            if (LoggedDateParsing($"Created Date for Employee {employee.ExternalEmployeeId}",
                entry.CreatedDate, out var createdDate))
                pp.Created = createdDate;

            if (LoggedDateParsing($"Modified Date for Employee {employee.ExternalEmployeeId}",
                entry.ModifiedDate, out var modifiedDate))
                pp.Modified = modifiedDate;

            // add breaks if present
            if (entry.Breaks != null)
            {
                foreach (var brk in entry.Breaks)
                {
                    if (brk.Missed)
                    {
                        Log.Logger.Information("Skipping missed break for {eid} on {dt}",
                            employee.ExternalEmployeeId, brk.InDate);
                        continue;
                    }

                    var sBrk = new Break()
                    {
                        Paid = brk.Paid
                    };

                    if (LoggedDateParsing($"Break In Date for Employee {employee.ExternalEmployeeId}",
                        brk.InDate, out var breakInDate))
                        sBrk.TimeIn = breakInDate;

                    if (LoggedDateParsing($"Break Out Date for Employee {employee.ExternalEmployeeId}",
                        brk.OutDate, out var breakOutDate))
                        sBrk.TimeOut = breakOutDate;

                    pp.Breaks.Add(sBrk);
                }
            }

            return true;
        }

        private bool LoggedDateParsing(string label, string date, out DateTime outDate)
        {
            if (string.IsNullOrEmpty(date))
            {
                Log.Logger.Debug("{label} - null or empty date", label);
                outDate = DateTime.MinValue;
                return false;
            }

            if (!DateTime.TryParseExact(date, "yyyy-MM-ddTHH:mm:ss.FFF+0000",
                CultureInfo.CurrentCulture, DateTimeStyles.AssumeLocal, out outDate))
            {
                Log.Logger.Warning("{label} - failed to parse date {date}", label, date);
                return false;
            }

            return true;
        }

        public async Task<List<PunchPair>> TimePunches(DateTime startDate, DateTime endDate)
        {
            Log.Logger.Debug("Fetching punches for {startDate} to {endDate}...", startDate, endDate);

            var punches = new List<PunchPair>();
            var restaurants = Config.RestaurantsById();

            foreach (var restaurant in restaurants)
            {
                var rPunches =
                    await TimePunchesForRestaurantUsingStartEndDate(restaurant.Key, restaurant.Value.Code, startDate, endDate);
                punches.AddRange(rPunches);
            }

            return punches;
        }

        void IDisposable.Dispose()
        {
            var endTime = DateTime.Now;
            var totalTime = endTime - StartTime;
            var requestsPerSec = TotalApiRequestsMade / totalTime.TotalSeconds;

            Log.Logger.Debug("Total Api Requests Made = {total}", TotalApiRequestsMade);
            Log.Logger.Debug("Elapsed Time in Seconds = {time}", totalTime.TotalSeconds);

            if (requestsPerSec > 15)
                Log.Logger.Warning("Requests Per Second     = {stat}", requestsPerSec);
            else
                Log.Logger.Debug("Requests Per Second     = {stat}", requestsPerSec);
        }
    }
}