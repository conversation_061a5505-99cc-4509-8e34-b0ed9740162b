using Newtonsoft.Json;
using Payroll.Shared;
using Serilog;
using System;
using System.Linq;
using System.Collections.Generic;
using System.Globalization;

namespace Toast.Tool
{
    class RestaurantCommand
    {
        public void List(List<string> args)
        {
            // brief listing or full
            bool brief = false;
            if (args != null && args.Count > 0) brief = true;

            try
            {
                using (var service = new ToastService())
                {
                    var defaultRestaurant = Config.DefaultRestaurantId();

                    if (defaultRestaurant == null)
                    {
                        Log.Logger.Fatal("No default restaurant location found in settings file.");
                        return;
                    }

                    if (!brief)
                    {
                        Console.WriteLine();
                        Console.WriteLine("Enabled\tCode                \tGuid");
                    }

                    var restaurants = service.Restaurants().Result;
                    var enabledRestaurants = Config.RestaurantsById();
                    var sortedRestaurants = new SortedDictionary<string, KeyValuePair<Guid, bool>>();

                    // sort by code before printing
                    foreach (var restaurant in restaurants)
                    {
                        var rsGuid = restaurant.Guid.ToString();
                        var enabled = enabledRestaurants.ContainsKey(rsGuid);
                        var code = enabled ? enabledRestaurants[rsGuid].Code : restaurant.Guid.ToString().Tail(6);
                        sortedRestaurants.Add(code, new KeyValuePair<Guid, bool>(restaurant.Guid, enabled));
                    }

                    foreach (var kvp in sortedRestaurants)
                    {
                        var rsGuid = kvp.Value.Key.ToString();
                        var enabled = kvp.Value.Value;
                        var code = kvp.Key;

                        if (brief)
                        {
                            // we only write out enabled entries in brief mode
                            if (enabled) Console.Write(code + " ");
                        }
                        else
                            Console.WriteLine($"{enabled}  \t{code.PadRight(20)}\t{rsGuid}");
                    }

                    if (brief)
                        Console.Write("\n");
                    else
                    {
                        Console.WriteLine();
                        Console.WriteLine("To enable a restaurant, name it using the command...");
                        Console.WriteLine("  Payroll.Tool.exe setting set toast_restaurants <code> <name>");
                    }
                }
            }
            catch (Exception e)
            {
                Log.Logger.Fatal(e.Message);
                Log.Logger.Fatal(e.StackTrace);
            }
        }

        public void View(List<string> args)
        {
            if (args == null || args.Count < 1)
            {
                Console.WriteLine("Usage: Toast.Tool.exe restaurant view <guid>");
                Console.WriteLine("For example, Toast.Tool.exe restaurant view b121007b-0b0b-4c0d-b4b9-89dc5678eb49");
                return;
            }

            try
            {
                using (var service = new ToastService())
                {
                    var restaurant = service.RestaurantInfo(args[0]).Result;
                    Console.WriteLine(JsonConvert.SerializeObject(restaurant, Formatting.Indented));
                }
            }
            catch (Exception e)
            {
                Log.Logger.Fatal(e.Message);
                Log.Logger.Fatal(e.StackTrace);
            }
        }

        public void Fixdupes(List<string> args)
        {
            bool dryRun = true;
            if (args != null && args.Count > 0)
            {
                if (args[0] == "doit") dryRun = false;
            }

            try
            {
                using (var service = new ToastService())
                {
                    var startDate = DateTime.Now.AddDays(-14);
                    var endDate = DateTime.Now;

                    var employees = service.AllEmployeesAllRestaurants().Result;
                    var punches = service.TimePunches(startDate, endDate).Result;
                    var troubleDate = new DateTime(2023, 12, 14);

                    foreach (var kvp in employees)
                    {
                        foreach (var emp in kvp.Value)
                        {
                            if (!DateTime.TryParseExact(emp.CreatedDate, "yyyy-MM-ddTHH:mm:ss.FFF+0000",
                                CultureInfo.CurrentCulture, DateTimeStyles.AssumeLocal, out DateTime cDate))
                            {
                                Log.Logger.Warning($"{emp.ExternalEmployeeId} - failed to parse date {emp.CreatedDate}");
                                continue;
                            }

                            if (cDate.Date == troubleDate.Date)
                            {
                                if (emp.Deleted) continue;
                                Log.Logger.Warning($"{emp.ExternalEmployeeId} {emp.Email} - created on {cDate}");

                                var fndPunchData = false;
                                foreach (var punch in punches)
                                {
                                    if (punch.Id == emp.Guid.ToString())
                                    {
                                        //ConsoleService.PrintFormattedJson(punch);
                                        fndPunchData = true;
                                        break;
                                    }
                                }

                                if (!fndPunchData)
                                {
                                    Log.Logger.Warning($"  {kvp.Key.Code} - {emp.ExternalEmployeeId} {emp.Email} - no punch data found");

                                    if (dryRun)
                                    {
                                        Log.Logger.Information(
                                            "Dry Run Mode: would have terminated employee {fname} {lname} - {cseq}, for restaurant {code}",
                                            emp.FirstName, emp.LastName, emp.Email, kvp.Key.Code);
                                    }
                                    else
                                    {
                                        Log.Logger.Information("Execution Mode: terminating employee {fname} {lname} - {cseq} for restaurant {code}",
                                            emp.FirstName, emp.LastName, emp.Email, kvp.Key.Code);
                                        var rc = service.DeleteEmployee(kvp.Key.Id, emp).Result;
                                        ConsoleService.PrintFormattedJson(rc);
                                    }
                                }
                            }
                        }
                    }
                }
            }
            catch (Exception e)
            {
                Log.Logger.Fatal(e.Message);
                Log.Logger.Fatal(e.StackTrace);
            }
        }

        public void Employees(List<string> args)
        {
            if (args == null || args.Count < 1)
            {
                Console.WriteLine("Usage:   Toast.Tool.exe restaurant employees <code> {status}");
                Console.WriteLine("Example: Toast.Tool.exe restaurant employees mm22");
                return;
            }

            bool showStatus = false;
            if (args != null && args.Count > 1)
            {
                if (args[1] == "status") showStatus = true;
            }

            try
            {
                using (var service = new ToastService())
                {
                    var codeMap = Config.RestaurantsByCode();
                    if (!codeMap.TryGetValue(args[0], out RestaurantInfo rInfo))
                    {
                        Console.WriteLine($"Invalid restaurant code: {args[0]}");
                        return;
                    }

                    var employees = service.EmployeesForRestaurant(rInfo.Id).Result;
                    var sortedEmployees = employees.Values.OrderBy(x => x.ExternalEmployeeId);

                    foreach (var employee in sortedEmployees)
                    {
                        // if status isn't of interest, only show active employee records
                        if (!showStatus && employee.Deleted) continue;

                        var eeId = employee.ExternalEmployeeId ?? "";
                        eeId.PadRight(20);

                        var state = "A";
                        if (employee.Deleted) state = "D";

                        Console.WriteLine(
                            $"{employee.Guid}\t{eeId}\t{state}\t{employee.FirstName} {employee.LastName}\t{employee.Email}\t{employee.CreatedDate}");
                    }
                }
            }
            catch (Exception e)
            {
                Log.Logger.Fatal(e.Message);
                Log.Logger.Fatal(e.StackTrace);
            }
        }
    }
}