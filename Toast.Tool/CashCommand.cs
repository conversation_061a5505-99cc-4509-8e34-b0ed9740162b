using System;
using System.Collections.Generic;
using System.Linq;
using Newtonsoft.Json;
using Payroll.Shared;
using Serilog;

namespace Toast.Tool
{
    class CashCommand
    {
        public void Deposits(List<string> args)
        {
            if (args == null || args.Count < 2)
            {
                Console.WriteLine("Usage: Toast.Tool.exe cash deposits <code> <day>");
                Console.WriteLine(
                    "  where: <day> is an integer representing how many days prior to today");
                return;
            }

            var codeMap = Config.RestaurantsByCode();
            if (!codeMap.TryGetValue(args[0], out var rInfo))
            {
                Console.WriteLine($"Invalid restaurant code: {args[0]}");
                return;
            }

            var daysBack = Convert.ToInt32(args[1]);
            var date = DateTime.UtcNow.Date.AddDays(-daysBack);

            try
            {
                using (var service = new ToastService())
                {
                    var deposits = service.Deposits(rInfo.Id, date).Result;
                }
            }
            catch (Exception e)
            {
                Log.Logger.Fatal(e.Message);
                Log.Logger.Fatal(e.StackTrace);
            }
        }
    }
}