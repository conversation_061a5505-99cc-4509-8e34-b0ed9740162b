using System;
using System.Collections.Generic;
using System.Linq;
using Newtonsoft.Json;
using Payroll.Shared;
using Serilog;

namespace Toast.Tool
{
    class ExportCommand : PosExportCommand
    {
        public int AllTime(List<string> args)
        {
            if (args == null || args.Count < 2)
            {
                Console.WriteLine("Usage: Toast.Tool.exe export time <start-day> <end-day>");
                Console.WriteLine(
                    "  where: <start-day> and <end-day> are integers representing how many days prior to today");
                Console.WriteLine(
                    "  e.g. 'Toast.Tool.exe export time 7 1' would export time from 7 days ago till yesterday.");
                return 1;
            }

            try
            {
                // initialize directories to reduce API calls
                using (var service = new ToastService())
                {
                    BuildDateWindowsFromCommandLine(args[1], args[2], out DateTime startDate, out DateTime endDate);
                    var punches = service.TimePunches(startDate, endDate).Result;
                    ConsoleService.PrintFormattedJson(punches);
                }
            }
            catch (Exception e)
            {
                Log.Logger.Fatal(e.Message);
                Log.Logger.Fatal(e.StackTrace);
                return 1;
            }

            return 0;
        }

        public int Employees(List<string> args)
        {
            try
            {
                using (var service = new ToastService())
                {
                    RestaurantJobCache cache = new RestaurantJobCache(service);
                    var employees = new Dictionary<string, Payroll.Shared.Employee>();
                    var employeeMap = service.EmployeesByRestaurant().Result;

                    foreach (KeyValuePair<string, EmployeeDirectory> employeeMapEntry in employeeMap)
                    {
                        var jobs = cache.HourlyJobMapForRestaurantByCode(employeeMapEntry.Key);

                        EmployeeDirectory employeesForRestaurant = employeeMap[employeeMapEntry.Key];

                        foreach (var kvp in employeesForRestaurant)
                        {
                            // skip deleted employees
                            if (kvp.Value.Deleted) continue;

                            var employee = Converter.ToastEmployeeToSharedEmployee(kvp.Value, jobs);

                            // make sure they have the work location too...
                            employee.PrimaryWorkLocation = employeeMapEntry.Key;
                            
                            if (!employees.ContainsKey(employee.Id))
                                employees.Add(employee.Id, employee);
                        }
                    }

                    ConsoleService.PrintFormattedJson(employees.Values);
                }
            }
            catch (Exception e)
            {
                Log.Logger.Fatal(e.Message);
                Log.Logger.Fatal(e.StackTrace);
                return 1;
            }

            return 0;
        }

        private void PrintTimeCommandInfo()
        {
            Console.WriteLine("Usage: Toast.Tool.exe export time <restaurant> <start-day> <end-day>");
            Console.WriteLine("  where: <restaurant> is the restaurant location code like MBMB");
            Console.WriteLine(
                "  where: <start-day> and <end-day> are integers representing how many days prior to today");
            Console.WriteLine(
                "  e.g. 'Toast.Tool.exe export time MBBR 7 1' would export time from 7 days ago till yesterday at the MBBR location.");
        }
        public int Time(List<string> args)
        {
            if (args == null || args.Count < 3)
            {
                PrintTimeCommandInfo();
                return 1;
            }

            try
            {
                // initialize directories to reduce API calls
                using (var service = new ToastService())
                {
                    var location = args[0].Trim();
                    BuildDateWindowsFromCommandLine(args[1], args[2], out DateTime startDate, out DateTime endDate);
                    var restaurants = Config.RestaurantsByCode();

                    if (!restaurants.TryGetValue(location, out RestaurantInfo restaurant))
                    {
                        Log.Logger.Fatal("Failed to find location {loc}.", location);
                        return 1;
                    }

                    List<PunchPair> punches = service.TimePunchesForRestaurantUsingStartEndDate(restaurant.Id, location,
                        startDate, endDate).Result;
                    Log.Logger.Debug("Punches returned: {cnt}", punches.Count);
                    ConsoleService.PrintFormattedJson(punches);
                }
            }
            catch (Exception e)
            {
                Log.Logger.Fatal(e.Message);
                Log.Logger.Fatal(e.StackTrace);
                return 1;
            }

            return 0;
        }

        public int Time2(List<string> args)
        {
            if (args == null || args.Count < 3)
            {
                PrintTimeCommandInfo();
                return 1;
            }

            try
            {
                // initialize directories to reduce API calls
                using (var service = new ToastService())
                {
                    var location = args[0].Trim();
                    BuildDateWindowsFromCommandLine(args[1], args[2], out DateTime startDate, out DateTime endDate);
                    var restaurants = Config.RestaurantsByCode();

                    if (!restaurants.TryGetValue(location, out RestaurantInfo restaurant))
                    {
                        Log.Logger.Fatal("Failed to find location {loc}.", location);
                        return 1;
                    }

                    List<PunchPair> punches = service.TimePunchesForRestaurantUsingBusinessDate(restaurant.Id, location,
                        startDate, endDate).Result;
                    Log.Logger.Information("Business Date Punches returned: {cnt}", punches.Count);
                    ConsoleService.PrintFormattedJson(punches);
                }
            }
            catch (Exception e)
            {
                Log.Logger.Fatal(e.Message);
                Log.Logger.Fatal(e.StackTrace);
                return 1;
            }

            return 0;
        }
    }
}