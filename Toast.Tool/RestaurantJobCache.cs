using Payroll.Shared;
using System;
using System.Collections.Generic;
using System.Linq;
using Serilog;
using Newtonsoft.Json;

namespace Toast.Tool
{
    public class RestaurantJobCache
    {
        private SortedDictionary<string, Dictionary<Guid, Job>> jobsForRestaurant = new SortedDictionary<string, Dictionary<Guid, Job>>();
        private SortedDictionary<string, Dictionary<Guid, Job>> hourlyJobsForRestaurant = new SortedDictionary<string, Dictionary<Guid, Job>>();
        private static SortedDictionary<string, RestaurantInfo> restaurantsByCode;
        private static HashSet<string> managerJobsById;
        private ToastService service;

        static RestaurantJobCache()
        {
            managerJobsById = Config.ManagerJobsById();
            restaurantsByCode = Config.RestaurantsByCode();
        }

        public RestaurantJobCache(ToastService s)
        {
            service = s;
        }

        public Dictionary<Guid, Job> JobMapForRestaurantByCode(string restaurantCode)
        {
            if (!restaurantsByCode.TryGetValue(restaurantCode, out var rInfo))
            {
                throw new ArgumentException($"Invalid restaurant code '{restaurantCode}'");
            }

            return JobMapForRestaurant(rInfo);
        }

        public Dictionary<Guid, Job> JobMapForRestaurant(RestaurantInfo rInfo)
        {
            if (!jobsForRestaurant.TryGetValue(rInfo.Id, out var jobMap))
            {
                jobMap = service.JobsByIdForRestaurant(rInfo.Id).Result;
                jobsForRestaurant.Add(rInfo.Id, jobMap);
            }

            return jobMap;
        }

        public Dictionary<Guid, Job> HourlyJobMapForRestaurantByCode(string restaurantCode)
        {
            if (!restaurantsByCode.TryGetValue(restaurantCode, out var rInfo))
            {
                throw new ArgumentException($"Invalid restaurant code '{restaurantCode}'");
            }

            return HourlyJobMapForRestaurant(rInfo);
        }

        public Dictionary<Guid, Job> HourlyJobMapForRestaurant(RestaurantInfo rInfo)
        {
            if (!hourlyJobsForRestaurant.TryGetValue(rInfo.Id, out var hourlyJobs))
            {
                var jobMap = JobMapForRestaurant(rInfo);
                hourlyJobs = new Dictionary<Guid, Job>();
                
                foreach (var j in jobMap)
                {
                    if (!IsManagerJob(j.Key))
                    {
                        hourlyJobs.Add(j.Key, j.Value);
                    }
                }

                hourlyJobsForRestaurant.Add(rInfo.Id, hourlyJobs);
            }

            return hourlyJobs;
        }

        public bool IsManagerJob(Guid guid)
        {
            return managerJobsById.Contains(guid.ToString());
        }
    }
}