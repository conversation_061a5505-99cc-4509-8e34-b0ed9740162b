using System;
using System.Collections.Generic;
using System.Linq;
using Newtonsoft.Json;
using Serilog;

namespace Toast.Tool
{
    class JobCommand
    {
        public void Audit(List<string> args)
        {
            try
            {
                var jobAudit = new SortedDictionary<string, Job>();
                using (var service = new ToastService())
                {
                    var jobMap = service.JobsByRestaurant().Result;
                    foreach (var restaurant in jobMap.Keys)
                    {
                        var jobs = jobMap[restaurant].OrderBy(x => x.Value.Code);
                        foreach (var kvp in jobs)
                        {
                            var job = kvp.Value;

                            if (job.Deleted) continue;
                            if (jobAudit.ContainsKey(job.Code)) continue;

                            jobAudit.Add(job.Code, job);
                        }
                    }
                }

                foreach (var (key, value) in jobAudit)
                {
                    Console.WriteLine($"{key}\t{value.Title}\t{value.ExternalId}");
                }
            }
            catch (Exception e)
            {
                Log.Logger.Fatal(e.Message);
                Log.Logger.Fatal(e.StackTrace);
            }
        }

        public void List(List<string> args)
        {
            string location = null;
            if (args != null && args.Count > 0) location = args[0];

            try
            {
                using (var service = new ToastService())
                {
                    var jobMap = service.JobsByRestaurant().Result;
                    foreach (var restaurant in jobMap.Keys)
                    {
                        // if user specified one location just show jobs for that location
                        if (!string.IsNullOrEmpty(location)
                            && restaurant != location) continue;

                        var jobs = jobMap[restaurant].OrderBy(x => x.Value.Code);
                        foreach (var kvp in jobs)
                        {
                            var job = kvp.Value;

                            var title = job.Title;
                            if (job.Deleted) title += " (Deleted)";
                            else if (JobDirectory.IsManagerJob(job)) title += "*";

                            Console.WriteLine(
                                $"{restaurant}\t{title.PadRight(16)}\t{job.Code}\t{job.ExternalId}\t{job.Guid}");
                        }
                    }
                }
            }
            catch (Exception e)
            {
                Log.Logger.Fatal(e.Message);
                Log.Logger.Fatal(e.StackTrace);
            }
        }
    }
}