using System;
using System.Collections.Generic;
using System.Linq;
using Newtonsoft.Json;
using Payroll.Shared;
using Serilog;

namespace Toast.Tool
{
    class ClientCommand
    {
        public void List(List<string> args)
        {
            try
            {
                using (var service = new ToastService())
                {
                    var clients = service.Clients().Result;
                    Console.WriteLine(JsonConvert.SerializeObject(clients, Formatting.Indented));
                }
            }
            catch (Exception e)
            {
                Log.Logger.Fatal(e.Message);
                Log.Logger.Fatal(e.StackTrace);
            }
        }
    }
}