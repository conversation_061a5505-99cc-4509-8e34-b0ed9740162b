using Payroll.Shared;
using System.Linq;
using System.Collections.Generic;
using System;
using System.Collections.Immutable;

namespace Toast.Tool
{
    public static class Config
    {
        public static readonly string StoreSection = "open";
        public static readonly string SettingSection = "toast";
        private static readonly string LocationSection = "toast_restaurants";
        public static readonly string ManagerSection = "toast_manager_jobs";
        public static readonly string AdministrativeSection = "toast_administrative_jobs";
        public static readonly string SkipJobSection = "toast_skip_jobs";
        public static readonly string SkipWageOverrideSection = "toast_skip_wage_overrides";
        private static readonly string TipScheme = "toast_tip_scheme";

        private static readonly HashSet<string> JobsToSkip = new HashSet<string>();
        private static readonly HashSet<string> AdministrativeJobsToSkip = new HashSet<string>();
        private static readonly HashSet<string> WageOverridesToSkip = new HashSet<string>();

        public static readonly int HireLimit;
        public static readonly int HireYearMin;
        public static readonly int SyncLimit;
        public static readonly int TermLimit;

        static Config()
        {
            Setting.Init();

            {
                var skiplist = Setting.ListSection(SkipJobSection);
                JobsToSkip = skiplist.Keys.ToHashSet();
            }

            {
                var skiplist = Setting.ListSection(AdministrativeSection);
                AdministrativeJobsToSkip = skiplist.Keys.ToHashSet();
            }

            {
                var skiplist = Setting.ListSection(SkipWageOverrideSection);
                WageOverridesToSkip = skiplist.Keys.ToHashSet();
            }

            {
                string limit = Setting.Get(SettingSection, "hire_limit");
                HireLimit = string.IsNullOrEmpty(limit) ? 0 : int.Parse(limit);
            }

            {
                string limit = Setting.Get(SettingSection, "hire_year_min");
                HireYearMin = string.IsNullOrEmpty(limit) ? 0 : int.Parse(limit);
            }

            {
                string limit = Setting.Get(SettingSection, "sync_limit");
                SyncLimit = string.IsNullOrEmpty(limit) ? 0 : int.Parse(limit);
            }

            {
                string limit = Setting.Get(SettingSection, "term_limit");
                TermLimit = string.IsNullOrEmpty(limit) ? 0 : int.Parse(limit);
            }
        }

        // this just created a bunch of dupes
        public static bool SkipJob(string jobGuid)
        {
            if (string.IsNullOrEmpty(jobGuid)) return false;

            return JobsToSkip.Contains(jobGuid);
        }

        public static bool SkipAdministrativeJob(string jobGuid)
        {
            if (string.IsNullOrEmpty(jobGuid)) return false;

            return AdministrativeJobsToSkip.Contains(jobGuid);
        }

        public static bool SkipWageOverride(WageOverride wo)
        {
            var jobGuid = wo.JobReference.Guid.ToString();
            if (string.IsNullOrEmpty(jobGuid)) return false;

            return WageOverridesToSkip.Contains(jobGuid);
        }


        public static int ApiDelayInSeconds()
        {
            var delay = Setting.Get(SettingSection, "delay");
            if (string.IsNullOrEmpty(delay)) return 0;
            return Convert.ToInt32(delay);
        }

        public static string Endpoint()
        {
            return Setting.Get(SettingSection, "endpoint");
        }

        public static RestaurantInfo DefaultRestaurantId()
        {
            var map = RestaurantsById();
            var did = Setting.Get(SettingSection, "defaultid");

            if (!map.TryGetValue(did, out RestaurantInfo val))
                return null;

            return val;
        }

        public static Dictionary<string, RestaurantInfo> RestaurantsById()
        {
            var map = new Dictionary<string, RestaurantInfo>();
            var section = Setting.ListSection(LocationSection);

            foreach (var item in section)
            {
                var ri = new RestaurantInfo()
                {
                    Id = item.Key.Trim(),
                    Code = item.Value.Trim(),
                };
                map.Add(item.Key, ri);
            }

            return map;
        }

        public static SortedDictionary<string, RestaurantInfo> RestaurantsByCode()
        {
            var map = new SortedDictionary<string, RestaurantInfo>();
            var section = Setting.ListSection(LocationSection);

            foreach (var item in section)
            {
                var ri = new RestaurantInfo()
                {
                    Id = item.Key.Trim(),
                    Code = item.Value.Trim()
                };
                map.Add(item.Value, ri);
            }

            return map;
        }

        public static HashSet<string> ManagerJobsById()
        {
            var section = Setting.ListSection(ManagerSection);
            if (section == null) return new HashSet<string>();
            return section.Keys.ToHashSet();
        }

        public static ExecutionMode ExecutionMode()
        {
            return Setting.ExecutionMode(SettingSection, "mode");
        }

        public static ExecutionMode HireMode()
        {
            return Setting.ExecutionMode(SettingSection, "hire_mode");
        }

        public static ExecutionMode SyncMode()
        {
            return Setting.ExecutionMode(SettingSection, "sync_mode");
        }

        public static ExecutionMode TermMode()
        {
            return Setting.ExecutionMode(SettingSection, "term_mode");
        }

        public static Dictionary<string, string> TipSchemeByRestaurantCode()
        {
            var section = Setting.ListSection(TipScheme);
            return section.ToDictionary(item => item.Key, item => item.Value);
        }

        public static string TipSchemeForRestaurant(string restaurantAbbrev)
        {
            var tipSchemes = TipSchemeByRestaurantCode();

            var tipScheme = "total";
            if (tipSchemes.ContainsKey(restaurantAbbrev))
                tipScheme = tipSchemes[restaurantAbbrev];

            return tipScheme;
        }

        public static ExecutionMode RemoveJobsMode()
        {
            var mode = Setting.Get(SettingSection, "rm_jobs_mode");

            if (string.IsNullOrEmpty(mode)) return Payroll.Shared.ExecutionMode.DryRun;
            if (mode == "disabled") return Payroll.Shared.ExecutionMode.Disabled;

            return mode == "execute" ? Payroll.Shared.ExecutionMode.Execute : Payroll.Shared.ExecutionMode.DryRun;
        }
    }
}