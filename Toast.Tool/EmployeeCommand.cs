using System;
using System.Collections.Generic;
using System.Linq;
using Newtonsoft.Json;
using Payroll.Shared;
using Serilog;

namespace Toast.Tool
{
    class EmployeeCommand
    {
        public void Add(List<string> args)
        {
            if (args == null || args.Count < 4)
            {
                Console.WriteLine("Usage: Toast.Tool.exe add employee <external-id> <first-name> <last-name> <email>");
                return;
            }

            var employee = new Payroll.Shared.Employee()
            {
                ClockSeq = args[0],
                FirstName = args[1],
                LastName = args[2],
                WorkEmail = args[3]
            };

            var toastEmployee = Converter.SharedEmployeeToToastEmployee(employee);

            var restaurant = Config.DefaultRestaurantId();
            Log.Logger.Information("Adding all jobs to employee...");

            try
            {
                using (var service = new ToastService())
                {
                    var jobs = service.JobsByCodeForRestaurant(restaurant).Result;

                    foreach (var job in employee.Jobs)
                    {
                        if (jobs.TryGetValue(job.Code, out Job toastJob))
                        {
                            Log.Logger.Information("  Adding job... {jid}, {jobname} ({guid})", job.Code, job.Name,
                                toastJob.Guid);
                            toastEmployee.JobReferences.Add(new Reference() {Guid = toastJob.Guid});
                        }
                        else
                        {
                            Log.Logger.Warning("  Failed to find toast job, will not add {jid}, {jobname}", job.Code,
                                job.Name);
                        }
                    }

                    var addedEmployee = service.AddEmployee(restaurant.Id, toastEmployee).Result;
                    if (addedEmployee)
                        Console.WriteLine($"Added employee");
                }
            }
            catch (Exception e)
            {
                Log.Logger.Fatal(e.Message);
                Log.Logger.Fatal(e.StackTrace);
            }
        }

        public void Terms(List<string> args)
        {
            PrintSelected(args, x => x.Deleted);
        }

        public void Exceptions(List<string> args)
        {
            PrintSelected(args, x => !x.Deleted);
        }

        public void All(List<string> args)
        {
            PrintSelected(args, x => true);
        }

        public void List(List<string> args)
        {
            Active(args);
        }

        public void Active(List<string> args)
        {
            PrintSelected(args, x => !x.Deleted);
        }

        public void Broken(List<string> args)
        {
            PrintSelected(args, x => (!x.Deleted && string.IsNullOrEmpty(x.ExternalEmployeeId)));
        }

        public void Valid(List<string> args)
        {
            PrintSelected(args, x => !string.IsNullOrEmpty(x.ExternalEmployeeId));
        }

        public void PrintSelected(List<string> args, Func<Employee, bool> match)
        {
            var showWages = false;
            if (args != null && args.Count > 0)
            {
                if (args[0] == "wages") showWages = true;
            }

            var showJobs = false;
            if (args != null && args.Count > 0)
            {
                if (args[0] == "jobs") showJobs = true;
            }

            var showGuids = false;
            if (args != null && args.Count > 0)
            {
                if (args[0] == "guids") showGuids = true;
            }

            try
            {
                using (var service = new ToastService())
                {
                    var startDate = DateTime.Now.AddDays(-14);
                    var endDate = DateTime.Now;

                    var punches = new List<PunchPair>();
                    if (showWages) punches = service.TimePunches(startDate, endDate).Result;

                    var employees = service.AllEmployeesAllRestaurants().Result;
                    JobDirectory jobMap = service.JobsByRestaurant().Result;

                    foreach (var kvp in employees)
                    {
                        foreach (var employee in kvp.Value)
                        {
                            if (!match(employee)) continue;
                            var jobs = jobMap[kvp.Key.Code];

                            var name = $"{employee.FirstName} {employee.LastName}";
                            if (service.IsManager(employee)) name += "*";

                            Console.Write(
                                $"{kvp.Key.Code}\t{employee.ExternalEmployeeId}\t{name.PadRight(16)}\t{employee.Email}");
                            if (showGuids)
                                Console.Write($"\t{employee.Guid}");

                            Console.WriteLine();

                            if (showJobs)
                            {
                                foreach (var job in employee.JobReferences)
                                {
                                    Console.WriteLine($"  {job.ExternalId} {job.Guid}");
                                }
                            }

                            if (showWages)
                            {
                                foreach (var wage in employee.WageOverrides)
                                {
                                    var jobRef = wage.JobReference;
                                    Job wageJob = null;

                                    foreach (var job in jobs)
                                    {
                                        if (job.Value.Guid == wage.JobReference.Guid)
                                        {
                                            wageJob = job.Value;
                                            break;
                                        }
                                    }

                                    if (wageJob == null)
                                        Log.Logger.Debug("Failed to find job {jid}", wage.JobReference.Guid);
                                    else
                                    {
                                        var overrideWage = System.Convert.ToDecimal(wage.Wage);
                                        decimal jobWage = wageJob.DefaultWage ?? overrideWage;
                                        Console.WriteLine($"  {wageJob.Code} {wageJob.Title} - Wage: {jobWage}");
                                    }
                                }
                            }
                        }
                    }
                }
            }
            catch (Exception e)
            {
                Log.Logger.Fatal(e.Message);
                Log.Logger.Fatal(e.StackTrace);
            }
        }

        public void Connections(List<string> args)
        {
            try
            {
                using (var service = new ToastService())
                {
                    var employeeMap = service.EmployeesByRestaurant().Result;
                    foreach (var restaurant in employeeMap.Keys)
                    {
                        var employees = employeeMap[restaurant];
                        foreach (var kvp in employees)
                        {
                            var employee = kvp.Value;
                            if (employee.Deleted) continue;

                            // not syncable
                            if (string.IsNullOrEmpty(employee.ExternalEmployeeId)) continue;

                            var name = $"{employee.FirstName} {employee.LastName}";
                            Console.WriteLine(
                                $"{kvp.Key}\t{employee.ExternalEmployeeId}\t{(employee.Deleted ? 'D' : 'A')}\t{name.PadRight(16)}\t{employee.Email}\t{employee.CreatedDate}");
                        }
                    }

                }
            }
            catch (Exception e)
            {
                Log.Logger.Fatal(e.Message);
                Log.Logger.Fatal(e.StackTrace);
            }
        }

        public void View(List<string> args)
        {
            if (args == null || args.Count < 2)
            {
                Console.WriteLine("Usage: Toast.Tool.exe employee view <restaurant id> <employee guid>");
                return;
            }

            var codeMap = Config.RestaurantsByCode();
            if (!codeMap.TryGetValue(args[0], out var rInfo))
            {
                Console.WriteLine($"Invalid restaurant code: {args[0]}");
                return;
            }

            try
            {
                using (var service = new ToastService())
                {
                    RestaurantJobCache cache = new RestaurantJobCache(service);
                    var toastEmployee = service.EmployeeInfo(rInfo.Id, args[1]).Result;
                    Console.WriteLine(JsonConvert.SerializeObject(toastEmployee, Formatting.Indented));

                    var jobMap = cache.JobMapForRestaurant(rInfo);
                    var employee = Converter.ToastEmployeeToSharedEmployee(toastEmployee, jobMap);

                    Console.WriteLine(JsonConvert.SerializeObject(value: employee, Formatting.Indented));
                }
            }
            catch (Exception e)
            {
                Log.Logger.Fatal(e.Message);
                Log.Logger.Fatal(e.StackTrace);
            }
        }
    }
}