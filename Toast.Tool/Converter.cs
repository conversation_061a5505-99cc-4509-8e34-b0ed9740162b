﻿using System;
using System.Collections.Generic;
using Serilog;

namespace Toast.Tool;

static class Converter
{
    public static Employee SharedEmployeeToToastEmployee(Payroll.Shared.Employee employee)
    {
        var toastEmployee = new Employee()
        {
            ExternalEmployeeId = employee.ClockSeq,
            FirstName = employee.FirstName,
            LastName = employee.LastName,
            Email = FieldService.GetEmailAddress(employee),
            PhoneNumber = employee.CellPhone
        };

        return toastEmployee;
    }

    public static Payroll.Shared.Employee ToastEmployeeToSharedEmployee(Employee toastEmployee)
    {
        var employee = new Payroll.Shared.Employee()
        {
            Id = toastEmployee.ExternalEmployeeId,
            WorkEmail = toastEmployee.Email,
            CellPhone = toastEmployee.PhoneNumber,
            LastName = toastEmployee.LastName,
            FirstName = toastEmployee.FirstName,
            Active = toastEmployee.Deleted ? false : true,
        };

        return employee;
    }

    public static Payroll.Shared.Employee ToastEmployeeToSharedEmployee(Employee toastEmployee, Dictionary<Guid, Job> jobMap)
    {
        var employee = ToastEmployeeToSharedEmployee(toastEmployee);

        foreach (var jobRef in toastEmployee.JobReferences)
        {
            if (!jobMap.ContainsKey(jobRef.Guid))
            {
                Log.Logger.Debug("Failed to find job {0}, cannot map...", jobRef.Guid);
                continue;
            }

            var toastJob = jobMap[jobRef.Guid];

            decimal currentWage = toastJob.DefaultWage ?? 0;
            foreach (var wage in toastEmployee.WageOverrides)
            {
                if (wage.JobReference.Guid == jobRef.Guid)
                {
                    currentWage = System.Convert.ToDecimal(wage.Wage);
                    break;
                }
            }

            employee.Jobs.Add(new Payroll.Shared.Job() { Code = toastJob.Code, Name = toastJob.Title, Rate = currentWage, IsPrimary = !toastJob.Deleted });
        }

        return employee;
    }
}