﻿using Payroll.Shared;

namespace Toast.Tool
{
    public static class FieldService
    {
        public readonly static string FieldSection = "toast_fields";
        public static string EmailFieldName { get; set; }
        public static string PasscodeFieldName { get; set; }

        static FieldService()
        {
            Setting.Init();
            EmailFieldName = Setting.Get(FieldSection, "email");
            PasscodeFieldName = Setting.Get(FieldSection, "passcode");
        }

        public static string GetPasscode(Payroll.Shared.Employee employee)
        {
            switch (PasscodeFieldName)
            {
                case "clockseq":
                    return employee.ClockSeq;
                default:
                    return string.Empty;
            }
        }

        public static string GetEmailAddress(Payroll.Shared.Employee employee)
        {
            switch (EmailFieldName)
            {
                case "personal":
                    return employee.PersonalEmail;
                default:
                    return employee.WorkEmail;
            }
        }
    }
}
