using System;
using System.Collections.Generic;
using System.Linq;
using Newtonsoft.Json;
using Serilog;
using Payroll.Shared;
using System.Globalization;
using System.Collections;

namespace Toast.Tool
{
    class ImportCommand
    {
        private EmployeeDirectoryByLocation EmployeeDirectoryByLocation { get; set; }
        private JobDirectory JobDirectory { get; set; }
        private SortedDictionary<string, RestaurantInfo> RestaurantDirectory { get; set; }

        public ImportCommand()
        {
            RestaurantDirectory = Config.RestaurantsByCode();
        }

        public void Info(List<string> args)
        {
            Console.WriteLine("Import - Command Usage");
            Console.WriteLine("  - import hires <restaurant code>|all <doit>");
            Console.WriteLine("    Create new employees from <STDIN> to Toast");
            Console.WriteLine("");
            Console.WriteLine("  - import terms <restaurant code>|all <doit>");
            Console.WriteLine("    Terminate employees from <STDIN> to Toast");
            Console.WriteLine("");
            Console.WriteLine("  - import sync <restaurant code>|all <doit>");
            Console.WriteLine("    Sync existing employees from <STDIN> to Toast");
            Console.WriteLine("");
            Console.WriteLine("  - import validate <doit>");
            Console.WriteLine("    Remove accounts from Toast that do not have a corresponding employee record in <STDIN>");
            Console.WriteLine("");
        }

        void LoadJobDirectory(ToastService service)
        {
            JobDirectory = service.JobsByRestaurant().Result;
        }

        void LoadDirectories(ToastService service)
        {
            EmployeeDirectoryByLocation = service.EmployeesByRestaurant().Result;
            LoadJobDirectory(service);
        }

        ///////////////////////////////// BEGIN NEW CODE /////////////////////////////////
        private void ProcessImportCommand(ExecutionMode executionMode, List<string> args, string syncLabel, int syncLimit,
            Func<ToastService, RestaurantInfo, IDictionary<string, Employee>, ExecutionMode, Payroll.Shared.Employee, bool> func)
        {
            try
            {
                if (!ConsoleService.TryGetEmployeesFromInput(out var employeesToImport))
                {
                    Log.Logger.Error("Failed to parse employee directory entries list");
                    return;
                }

                if (employeesToImport == null)
                {
                    Log.Logger.Error("Failed to load employee directory entries list");
                    return;
                }

                if (args == null || args.Count < 1)
                {
                    Console.WriteLine("Usage: Toast.Tool.exe import <hires|terms|sync> <restaurant code>|all");
                    return;
                }

                string targetLocation = args[0];

                if (executionMode == ExecutionMode.Execute)
                {
                    // both the config file and the command line must confirm execution
                    if (args.Count < 2) executionMode = ExecutionMode.DryRun;
                    else if (args[1] != "doit") executionMode = ExecutionMode.DryRun;
                }

                Log.Logger.Information("Toast.Tool Mode={mode}, Target={loc}, Op={op}, Limit={lmt}",
                    executionMode, targetLocation, syncLabel, syncLimit);
                using (var service = new ToastService())
                {
                    LoadJobDirectory(service);

                    var employeesByRestaurant = service.AllEmployeesAllRestaurantsByExternalId().Result;
                    var employeeDirectory = new Dictionary<string, SortedDictionary<string, Employee>>();

                    foreach (var kvp in employeesByRestaurant)
                    {
                        employeeDirectory.Add(kvp.Key.Code, kvp.Value);
                    }

                    var syncCnt = 0;
                    foreach (var employee in employeesToImport)
                    {
                        if (string.IsNullOrEmpty(employee.ClockSeq))
                        {
                            Log.Logger.Information("  No clock sequence specified, skipping {fname} {lname} - {cseq}",
                                employee.FirstName, employee.LastName, employee.ClockSeq);
                            continue;
                        }

                        if (employee.Jobs == null)
                        {
                            // only warn on the active ones
                            if (employee.Active)
                                Log.Logger.Warning("  Active employee with no jobs specified, skipping {fname} {lname} - {cseq}",
                                employee.FirstName, employee.LastName, employee.ClockSeq);
                            continue;
                        }

                        var locations = employee.Jobs.Select(e => e.Location).Distinct().ToList();
                        foreach (var location in locations)
                        {
                            if (string.IsNullOrEmpty(location))
                            {
                                // this employee may be terminated, so don't bark in that case
                                if (employee.Active)
                                {
                                    Log.Logger.Warning(
                                        "  No home location, skipping active employee record: {fname} {lname} - {cseq}",
                                        employee.FirstName, employee.LastName, employee.ClockSeq);
                                }

                                continue;
                            }

                            if (targetLocation == "all" || targetLocation == location)
                            {
                                if (!employeeDirectory.TryGetValue(location, out var directoryForLocation))
                                {
                                    Log.Logger.Error("  Failed to locate employee directory {rid}, skipping {fname} {lname} - {cseq}", location, employee.FirstName, employee.LastName, employee.ClockSeq);
                                    continue;
                                }

                                if (!RestaurantDirectory.TryGetValue(location, out RestaurantInfo restaurant))
                                {
                                    Log.Logger.Debug(
                                        "  Invalid or disabled toast restaurant code: {rid}, skipping {fname} {lname} - {cseq}",
                                        location, employee.FirstName, employee.LastName, employee.ClockSeq);
                                    continue;
                                }

                                var processed = func(service, restaurant, directoryForLocation, executionMode, employee);
                                if (processed) syncCnt++;

                                if (syncLimit > 0 && syncCnt >= syncLimit)
                                {
                                    Log.Logger.Warning("  {lbl} limit {x} reached, exiting...", syncLabel, syncLimit);
                                    return;
                                }
                            }
                        }
                    }
                } //using toast service
            }
            catch (Exception e)
            {
                Log.Logger.Fatal(e.Message);
                if (!string.IsNullOrEmpty(e.StackTrace)) Log.Logger.Fatal(e.StackTrace);
            }
        }

        public void Hires(List<string> args)
        {
            var executionMode = Config.HireMode();
            var results = new List<Result>();

            ProcessImportCommand(executionMode, args, "Hire", Config.HireLimit,
            (ToastService service, RestaurantInfo restaurant, IDictionary<string, Employee> employeeDirectory,
                ExecutionMode executionMode, Payroll.Shared.Employee employee) =>
            {
                if (!employee.Active)
                {
                    Log.Logger.Warning("  Skipping creation of inactive employee {cseq}", employee.ClockSeq);
                    return false;
                }

                if (employee.HireDate?.Year < Config.HireYearMin)
                {
                    Log.Logger.Warning(
                        "  Skipping creation of employee {cseq} with hire date {h} before {y}",
                        employee.ClockSeq, employee.HireDate?.ToShortDateString(), Config.HireYearMin);
                    return false;
                }

                var duplicateEmailAddress = false;
                var possibleDuplicate = FieldService.GetEmailAddress(employee);

                foreach (var edItem in employeeDirectory)
                {
                    if (String.Equals(edItem.Value.Email, possibleDuplicate, StringComparison.OrdinalIgnoreCase))
                    {
                        duplicateEmailAddress = true;
                        break;
                    }
                }

                var result = new Result();
                result.AddArg("pkey", employee.Id);
                result.AddArg("date", employee.HireDate?.ToShortDateString());
                result.AddArg(key: "name", $"{employee.FirstName} {employee.LastName}");
                result.AddArg("restaurant", val: restaurant.Code);

                if (duplicateEmailAddress)
                {
                    Log.Logger.Warning(
                        "  Skipping creation of employee {cseq} with duplicative email address {email}",
                        employee.ClockSeq, possibleDuplicate);
                    //result.AddArg("description", $"Skipping creation of employee with duplicative email address {possibleDuplicate}");
                    //result.ResultType = ResultType.Error;
                    //results.Add(result);
                    return false;
                }

                if (employeeDirectory.TryGetValue(employee.ClockSeq, out Employee toastEmployee))
                {
                    Log.Logger.Debug("  Skipping creation of an already present employee {cseq}",
                        employee.ClockSeq);

                    if (toastEmployee.Deleted)
                    {
                        // unfortunately, we cannot help in this scenario, since toast won't let us re-enable the account
                        result.ResultType = ResultType.ReHire;
                        result.AddArg("notice", "This employee must be manually re-enabled in Toast");
                        results.Add(result);
                    }

                    return false;
                }

                Log.Logger.Information("Creating new employee {fname} {lname} - {cseq} at location {loc}, hired: {h}",
                    employee.FirstName, employee.LastName, employee.ClockSeq, restaurant.Code, employee.HireDate?.ToShortDateString());

                // probably not a general issue, but when we imported manual RAS report
                // the numbers had commas in them
                if (!int.TryParse(employee.ClockSeq, NumberStyles.AllowThousands,
                    CultureInfo.InvariantCulture, out int clockseq))
                {
                    Log.Logger.Error("  Failed to convert external id {cseq} to integer passcode",
                        clockseq);
                    return false;
                }

                // setup a new employee record
                toastEmployee = new Employee()
                {
                    Passcode = clockseq.ToString(),
                    ExternalEmployeeId = clockseq.ToString(),
                    Email = FieldService.GetEmailAddress(employee),
                    FirstName = employee.FirstName,
                    LastName = employee.LastName
                };

                // employees HAVE to have an email address, so make up something
                // if we are here with an empty email address
                if (string.IsNullOrEmpty(toastEmployee.Email))
                {
                    var pseudoEmail = $"{employee.FirstName}.{employee.LastName}@toast-api-integration.com";
                    toastEmployee.Email = pseudoEmail.Replace(" ", "");
                }

                (bool needToUpdateJobs, bool needToUpdateWages) jobAndWageStatus = JobDirectory.MapJobsToToastEmployee(employee, toastEmployee);

                // new hire
                if (executionMode == ExecutionMode.DryRun)
                {
                    Log.Logger.Information(
                        "Dry Run Mode: would have added employee {fname} {lname} - {cseq} at location {loc}, hired: {h}",
                        toastEmployee.FirstName, toastEmployee.LastName, employee.ClockSeq, restaurant.Code, employee.HireDate?.ToShortDateString());
                }
                else
                {
                    Log.Logger.Information("Execution Mode: adding employee {fname} {lname} - {cseq} at location {loc}, hired: {h}",
                        toastEmployee.FirstName, toastEmployee.LastName, employee.ClockSeq, restaurant.Code, employee.HireDate?.ToShortDateString());

                    if (service.AddEmployee(restaurant.Id, toastEmployee).Result)
                    {
                        result.ResultType = ResultType.NewHire;
                        results.Add(result);
                    }
                }

                return true;
            });

            ConsoleService.PrintFormattedJson(results);
        }

        public void Sync(List<string> args)
        {
            var executionMode = Config.SyncMode();
            var results = new List<Result>();

            ProcessImportCommand(executionMode, args, "Sync", Config.SyncLimit,
            (ToastService service, RestaurantInfo restaurant, IDictionary<string, Employee> employeeDirectory,
                ExecutionMode executionMode, Payroll.Shared.Employee employee) =>
            {
                if (!employeeDirectory.TryGetValue(employee.ClockSeq, out Employee toastEmployee))
                {
                    Log.Logger.Debug("  Skipping sync of a non-existant employee {cseq}",
                        employee.ClockSeq);
                    return false;
                }

                if (!employee.Active)
                {
                    Log.Logger.Warning("  Skipping sync of inactive employee {cseq}", employee.ClockSeq);
                    return false;
                }

                if (toastEmployee.Deleted)
                {
                    Log.Logger.Warning("  Skipping sync of disabled/deleted toast employee {cseq}", employee.ClockSeq);
                    return false;
                }

                // update toast record as needed
                var employeeUpdate = false;

                if (toastEmployee.FirstName != employee.FirstName)
                {
                    //Log.Logger.Information(JsonConvert.SerializeObject(toastEmployee, Formatting.Indented));
                    Log.Logger.Information("  Updating first name from '{old}' to '{new}' - {cseq} at location {loc} ",
                        toastEmployee.FirstName, employee.FirstName, employee.ClockSeq, restaurant.Code);

                    toastEmployee.FirstName = employee.FirstName;
                    employeeUpdate = true;
                }

                if (toastEmployee.LastName != employee.LastName)
                {
                    Log.Logger.Information("  Updating last name from '{old}' to '{new}' - {cseq} at location {loc}",
                        toastEmployee.LastName, employee.LastName, employee.ClockSeq, restaurant.Code);

                    toastEmployee.LastName = employee.LastName;
                    employeeUpdate = true;
                }

                /* you are not allowed to update the phone number, so no code here for that - JWR 08/24/23 */
                var jobAndWageStatus = JobDirectory.MapJobsToToastEmployee(employee, toastEmployee);

                // Now handle updates...
                bool successfulUpdate = false;

                if (employeeUpdate)
                {
                    if (executionMode == ExecutionMode.DryRun)
                    {
                        Log.Logger.Information(
                            "Dry Run Mode: would have updated employee {fname} {lname} - {cseq}",
                            toastEmployee.FirstName, toastEmployee.LastName, employee.Id);
                    }
                    else
                    {
                        Log.Logger.Information("Execution Mode: updating employee {fname} {lname} - {cseq}",
                            toastEmployee.FirstName, toastEmployee.LastName, employee.Id);
                        if (service.UpdateEmployee(restaurant.Id, toastEmployee).Result)
                            successfulUpdate = true;
                    }
                }

                if (jobAndWageStatus.needToUpdateJobs)
                {
                    if (executionMode == ExecutionMode.DryRun)
                    {
                        Log.Logger.Information(
                            "Dry Run Mode: would have added jobs for employee {fname} {lname} ({eid})",
                            toastEmployee.FirstName, toastEmployee.LastName, employee.ClockSeq);
                    }
                    else
                    {
                        Log.Logger.Information("Execution Mode: adding jobs for employee {fname} {lname} ({eid})",
                            toastEmployee.FirstName, toastEmployee.LastName, employee.ClockSeq);
                        if (service.UpdateJobs(restaurant.Id, toastEmployee).Result)
                            successfulUpdate = true;
                    }
                }

                if (jobAndWageStatus.needToUpdateWages)
                {
                    if (executionMode == ExecutionMode.DryRun)
                    {
                        Log.Logger.Information(
                            "Dry Run Mode: would have modified wages for employee {fname} {lname} ({eid}), jobs: {jcnt}, wages: {wcnt}",
                            toastEmployee.FirstName, toastEmployee.LastName, employee.ClockSeq, toastEmployee.JobReferences.Count,
                            toastEmployee.WageOverrides.Count);
                    }
                    else
                    {
                        Log.Logger.Information(
                            "Execution Mode: modifying wages for employee {fname} {lname} ({eid}), jobs: {jcnt}, wages: {wcnt}",
                            toastEmployee.FirstName, toastEmployee.LastName, employee.ClockSeq,
                            toastEmployee.JobReferences.Count,
                            toastEmployee.WageOverrides.Count);
                        Log.Logger.Debug("Jobs: {jobs}", JsonConvert.SerializeObject(toastEmployee.JobReferences));
                        Log.Logger.Debug("Wages: {wages}", JsonConvert.SerializeObject(toastEmployee.WageOverrides));
                        if (service.UpdateWageOverrides(restaurant.Id, toastEmployee).Result)
                            successfulUpdate = true;
                    }
                }

                if (successfulUpdate)
                {
                    var result = new Result();
                    result.AddArg("pkey", employee.Id);
                    result.AddArg("date", employee.HireDate?.ToShortDateString());
                    result.AddArg(key: "name", $"{toastEmployee.FirstName} {toastEmployee.LastName}");
                    result.AddArg("restaurant", restaurant.Code);
                    result.ResultType = ResultType.Update;
                    results.Add(result);

                    return true;
                }

                return false;
            });

            ConsoleService.PrintFormattedJson(results);
        }

        public void Terms(List<string> args)
        {
            var executionMode = Config.TermMode();
            var results = new List<Result>();

            ProcessImportCommand(executionMode, args, "Term", Config.TermLimit,
            (ToastService service, RestaurantInfo restaurant, IDictionary<string, Employee> employeeDirectory,
                ExecutionMode executionMode, Payroll.Shared.Employee employee) =>
            {
                if (employee.Active) return false;

                if (!employeeDirectory.TryGetValue(employee.ClockSeq, out Employee toastEmployee))
                {
                    Log.Logger.Debug("  Skipping termination of employee not in directory {cseq}",
                        employee.ClockSeq);
                    return false;
                }

                if (employee.TermDate > DateTime.Now)
                {
                    Log.Logger.Information("Skipping termination of employee {cseq} with future term date {date}",
                        employee.ClockSeq, employee.TermDate?.ToShortDateString());
                    return false;
                }

                // Now handle deletions...
                // If employee has be deactivated, make sure in toast deleted
                if (!toastEmployee.Deleted)
                {
                    return HandleTermination(executionMode, service, restaurant, employee.TermDate, toastEmployee, results);
                }

                return false;
            });

            ConsoleService.PrintFormattedJson(results);
        }

        private bool HandleTermination(ExecutionMode executionMode, ToastService service, RestaurantInfo restaurant, DateTime? termDate, Employee toastEmployee, List<Result> results)
        {
            if (executionMode == ExecutionMode.DryRun)
            {
                Log.Logger.Information(
                    "Dry Run Mode: would have terminated employee {fname} {lname} - {cseq} at location {loc}",
                    toastEmployee.FirstName, toastEmployee.LastName, toastEmployee.ExternalEmployeeId, restaurant.Code);
            }
            else
            {
                Log.Logger.Information("Execution Mode: terminating employee {fname} {lname} - {cseq} at location {loc}",
                    toastEmployee.FirstName, toastEmployee.LastName, toastEmployee.ExternalEmployeeId, restaurant.Code);
                Log.Logger.Debug("Employee: {emp}", JsonConvert.SerializeObject(toastEmployee));
                if (service.DeleteEmployee(restaurant.Id, toastEmployee).Result)
                {
                    var result = new Result();
                    result.AddArg("pkey", toastEmployee.ExternalEmployeeId);
                    result.AddArg("date", termDate?.ToShortDateString());
                    result.AddArg(key: "name", $"{toastEmployee.FirstName} {toastEmployee.LastName}");
                    result.AddArg("restaurant", restaurant.Code);
                    result.ResultType = ResultType.Termination;
                    results.Add(result);
                    return true;
                }
            }

            return false;
        }

        public void Validate(List<string> args)
        {
            // basically another safety measure
            ExecutionMode executionMode = Config.SyncMode();
            var results = new List<Result>();
            var dryRun = !(args != null && args.Count > 0 && args[0] == "doit");

            if (executionMode == ExecutionMode.Execute)
            {
                // both the config file and the command line must confirm execution
                if (dryRun) executionMode = ExecutionMode.DryRun;
            }

            Log.Logger.Information("Toast.Tool Mode={mode}, Target=all, Op=Validate", executionMode);

            try
            {
                if (!ConsoleService.TryGetEmployeesFromInput(out var employees))
                {
                    Log.Logger.Error("Failed to parse employees list");
                    return;
                }

                if (employees == null || employees.Count == 0)
                {
                    Log.Logger.Error("Failed to load employees list");
                    return;
                }

                using (var service = new ToastService())
                {
                    LoadDirectories(service);

                    var activeEmployees = new Dictionary<string, Payroll.Shared.Employee>();
                    foreach (var employee in employees)
                    {
                        if (string.IsNullOrEmpty(employee.ClockSeq))
                        {
                            Log.Logger.Information("  No clock sequence specified, skipping {fname} {lname} - {cseq}",
                                employee.FirstName, employee.LastName, employee.ClockSeq);
                            continue;
                        }

                        if (string.IsNullOrEmpty(employee.PrimaryWorkLocation))
                        {
                            // this employee may be terminated, so don't bark in that case
                            if (employee.Active)
                            {
                                Log.Logger.Warning(
                                    "  No home location, skipping active employee record: {fname} {lname} - {cseq}",
                                    employee.FirstName, employee.LastName, employee.ClockSeq);
                            }

                            continue;
                        }
                        activeEmployees.Add(employee.ClockSeq, employee);
                    }

                    foreach (var restaurant in RestaurantDirectory.Values)
                    {
                        if (!EmployeeDirectoryByLocation.TryGetValue(restaurant.Code, out var employeeDirectory))
                        {
                            Log.Logger.Debug(
                                "  Invalid or disabled toast restaurant code: {rid}",
                                restaurant.Code);
                            continue;
                        }

                        foreach (var employee in employeeDirectory.Values)
                        {
                            // this is probably Upwards specific, need to turn into a configuration option
                            // filter out employee records with a Job named "Report Access"
                            Log.Logger.Debug(" {cseq} checking job {job}", employee.ExternalEmployeeId, employee.JobReferences.FirstOrDefault()?.Guid.ToString());
                            if (employee.JobReferences.Any(x => Config.SkipAdministrativeJob(x.Guid.ToString())))
                            {
                                Log.Logger.Debug("  Administrative job, skipping {fname} {lname} - {cseq}",
                                    employee.FirstName, employee.LastName, employee.ExternalEmployeeId);
                                continue;
                            }

                            if (!activeEmployees.ContainsKey(employee.ExternalEmployeeId))
                            {
                                var termDate = DateTime.Now;
                                Log.Logger.Debug(
                                    "  Employee {cseq}, {fname} {lname} not found in restaurant {rid}, should be terminated...",
                                    employee.ExternalEmployeeId, employee.FirstName, employee.LastName, restaurant.Code);
                                HandleTermination(executionMode, service, restaurant, termDate, employee, results);
                            }
                        }
                    }
                } //using toast service

                ConsoleService.PrintFormattedJson(results);
            }
            catch (Exception e)
            {
                Log.Logger.Fatal(e.Message);
                Log.Logger.Fatal(e.StackTrace);
            }
        }
    }
}