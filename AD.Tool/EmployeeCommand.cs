﻿using Serilog;
using System.Text.Json;
using System.Text.Json.Serialization;
using AD.Shared;
using System.DirectoryServices;
using Payroll.Shared;

namespace AD.Tool
{
    class EmployeeCommand
    {
        void PrintSelectedEmployees(SearchResultCollection employees, Func<DirectoryEntry, bool> match)
        { 
            foreach (SearchResult result in employees)
            {
                if (OperatingSystem.IsWindows())
                {
                    DirectoryEntry user = result.GetDirectoryEntry();
                    if (!match(user)) continue;
                    var empName = user.Properties["mail"];
                    var distinguishedName = user.Properties["distinguishedName"].Value;
                    Console.WriteLine($"Name: {empName?.Value?.ToString()}, DN: {distinguishedName}");
                }
            }
        }

        public void List(List<string> args)
        {
            try
            { 
                using (var service = new ActiveDirectoryService())
                {
                    SearchResultCollection? employees = service.ListEmployees();
                    if (args == null || args.Count == 0) PrintSelectedEmployees(employees, x => true);
                    else if (args[0] == "active") PrintSelectedEmployees(employees, x => service.IsActiveUser(x));
                    else if (args[0] == "terms") PrintSelectedEmployees(employees, x => !service.IsActiveUser(x));           
                }
            }
            catch (Exception e)
            {
                Log.Logger.Fatal(e.Message);
                if (e.StackTrace != null) Log.Logger.Fatal(e.StackTrace);
            }
        }

        public void Disable(List<string> args)
        {
            if (args == null || args.Count < 1)
            {
                Console.WriteLine("Usage: AD.Tool.exe employee disable <samaccountname>");
                return;
            }

            var samaccountname = args[0];
            try
            {
                using (var service = new ActiveDirectoryService())
                {
                    service.DisableUser(samaccountname);
                }
            }
            catch (Exception e)
            {
                Log.Logger.Fatal(e.Message);
                if (!string.IsNullOrEmpty(e.StackTrace)) Log.Logger.Fatal(e.StackTrace);
            }
        }

        public void Groups(List<string> args)
        {
            if (args == null || args.Count < 1)
            {
                Console.WriteLine("Usage: AD.Tool.exe employee groups <dn>");
                return;
            }

            var dn = args[0];

            try
            {
                using (var service = new ActiveDirectoryService())
                {
                    var entry = service.FindEntryByDistinguishedName(dn);
                    if (entry == null)
                    {
                        Console.WriteLine("Failed to find entry {0}", dn);
                        return;
                    }

                    // Get the memberOf attribute
                    object[] groups = (object[])entry.Properties["memberOf"].Value;

                    // Iterate through the group distinguished names and display them
                    foreach (object groupDN in groups)
                    {
                        var groupEntry = service.FindEntryByDistinguishedName((string)groupDN);
                        if (groupEntry == null)
                        {
                            Log.Logger.Error("Could not find group {0} in AD.", groupDN);
                            continue;
                        }

#if WINDOWS
                        if (groupEntry.Name.StartsWith("CN="))
                        {
                            var chunks = groupEntry.Name.Split('=');
                            var groupName = chunks[1];
                            var skip = Config.SkipGroup(groupName);
                            var skipText = skip ? "(skipped)" : "";
                            Console.WriteLine($"Name={groupName}{skipText}, DN={groupEntry.Path}");
                        }
#endif
                    }
                }
            }
            catch (Exception e)
            {
                Log.Logger.Fatal(e.Message);
                if (!string.IsNullOrEmpty(e.StackTrace)) Log.Logger.Fatal(e.StackTrace);
            }
        }

        public void View(List<string> args)
        {
            if (args == null || args.Count < 1)
            {
                Console.WriteLine("Usage: AD.Tool.exe employee view <samaccountname>");
                return;
            }

            var samaccountname = args[0];
            try
            {
                using (var service = new ActiveDirectoryService())
                {
                    var employee = service.GetEmployeeDirectoryEntry(samaccountname);
                    if (employee == null)
                    {
                        Console.WriteLine("Failed to find employee {0}", samaccountname); 
                        return;
                    }

                    Console.WriteLine(JsonSerializer.Serialize(employee, Json.DefaultSerializerOutputStyle));
                }
            }
            catch (Exception e)
            {
                Log.Logger.Fatal(e.Message);
                if (!string.IsNullOrEmpty(e.StackTrace)) Log.Logger.Fatal(e.StackTrace);
            }
        }
    }
}