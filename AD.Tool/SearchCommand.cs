﻿using Serilog;
using System.Text.Json;
using System.Text.Json.Serialization;
using AD.Shared;
using System.ComponentModel;
using Payroll.Shared;
using System.Runtime.CompilerServices;
using System.ComponentModel.Design;

namespace AD.Tool
{
    class SearchCommand
    {
        private void ExecSearch(string searchName, string searchArg, List<string> args, Func<ActiveDirectoryService, string, EmployeeDirectoryEntry?> func)
        {
            if (args == null || args.Count < 1)
            {
                Console.WriteLine("Usage: AD.Tool.exe search {0} <{1}>", searchName, searchArg);
                return;
            }

            var x = args[0];

            try
            {
                using (var service = new ActiveDirectoryService())
                {
                    var match = func(service, x);
                    if (match == null) return;

                    string formattedJson = JsonSerializer.Serialize(match, Json.DefaultSerializerOutputStyle);
                    Console.WriteLine(formattedJson);
                }
            }
            catch (Exception e)
            {
                Log.Logger.Fatal(e.Message);
            }
        }

        public void Cn(List<string> args)
        {
            ExecSearch("cn", "common name", args, (service, filter) =>
            {
                return service.FindEntryByCommonName(filter);
            });
        }

        public void Dn(List<string> args)
        {
            ExecSearch("dn", "distinguished name", args, (service, filter) =>
            {
                var entry = service.FindEntryByDistinguishedName(filter);
                return service.ConvertToEmployeeDirectoryEntry(entry);
            });
        }

        public void Pkey(List<string> args)
        {
            if (args == null || args.Count < 1)
            {
                Console.WriteLine("Usage: AD.Tool.exe search pkey <ID>");
                return;
            }

            var pkey = args[0];
            ExecSearch(pkey, $"primary key: {Config.PrimaryKey()}", args, (service, filter) =>
            {
                var entry = service.GetUserDirectoryEntryByPrimaryKey(pkey);
                return service.ConvertToEmployeeDirectoryEntry(entry);
            });
        }

        public void Sam(List<string> args)
        {
            ExecSearch("samaccountname", "samaccount name", args, (service, filter) =>
            {
                var entry = service.GetUserDirectoryEntryBySamAccountName(filter);

                if (entry == null)
                    Log.Logger.Warning("Failed to find user with samAccountName of {0}", filter);

                return service.ConvertToEmployeeDirectoryEntry(entry);
            });
        }

        public void Last(List<string> args)
        {
            if (args == null || args.Count < 1)
            {
                Console.WriteLine("Usage: AD.Tool.exe search last <lastname>");
                return;
            }

            var lastname = args[0];
            try
            {
                using (var service = new ActiveDirectoryService())
                {
                    var matches = service.SearchEmployeesByLastName(lastname);
                    string formattedJson = JsonSerializer.Serialize(matches, Json.DefaultSerializerOutputStyle);
                    Console.WriteLine(formattedJson);
                }
            }
            catch (Exception e)
            {
                Log.Logger.Fatal(e.Message);
            }
        }
    }
}