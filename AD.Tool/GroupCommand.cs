﻿using Serilog;
using System.Text.Json;
using System.Text.Json.Serialization;
using AD.Shared;
using System.DirectoryServices;

namespace AD.Tool
{
    class GroupCommand
    {
        public void List(List<string> args)
        {
            try
            { 
                using (var service = new ActiveDirectoryService())
                {
                    var groups = service.ListGroupsAll();
                    foreach (SearchResult group in groups)
                    {
                        if (OperatingSystem.IsWindows())
                        {
                            DirectoryEntry entry = group.GetDirectoryEntry();
                            Console.WriteLine(entry.Path);
                        }
                        else
                        {
                            Console.WriteLine(JsonSerializer.Serialize(group));
                        }
                    }
                }
            }
            catch (Exception e)
            {
                Log.Logger.Fatal(e.Message);
                if (e.StackTrace != null) Log.Logger.Fatal(e.StackTrace);
            }
        }
    }
}