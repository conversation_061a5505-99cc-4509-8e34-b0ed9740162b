﻿using Payroll.Shared;
using Serilog;
using AD.Shared;
using System.ComponentModel;
using System.Text.Json;

namespace AD.Tool
{
    class ImportCommand
    {
        private void ProcessImportCommand(ExecutionMode executionMode, List<string> args, Func<IEnumerable<Payroll.Shared.EmployeeDirectoryEntry>, ExecutionMode, bool> func)
        {
            try
            {
                if (!ConsoleService.TryGetDirectoryEntriesFromInput(out var employees))
                    return;

                var dryRun = !(args != null && args.Count > 0 && args[0] == "doit");

                if (executionMode == ExecutionMode.Execute)
                {
                    // both the config file and the command line must confirm execution
                    if (dryRun) executionMode = ExecutionMode.DryRun;
                }

                Log.Logger.Information("AD.Tool Mode={mode}, DryRun={dr}", executionMode, dryRun);
                func(employees, executionMode);
            }
            catch (Exception e)
            {
                Log.Logger.Fatal(e.Message);
                if (!string.IsNullOrEmpty(e.StackTrace)) Log.Logger.Fatal(e.StackTrace);
            }
        }

        public void Changes(List<string> args)
        {
            var executionMode = Config.SyncMode();
            int syncCnt = 0;
            var results = new List<Result>();

            ProcessImportCommand(executionMode, args, (employees, executionMode) =>
            {
                using (var service = new ActiveDirectoryService())
                {
                    foreach (var employee in employees)
                    {
                        var result = new Result() { ResultType = ResultType.NewHire };
                        result.Args.Add("id", employee.EmployeeId);
                        result.Args.Add("pkey", employee.PrimaryKey);
                        result.Args.Add("date", employee.HireDate?.ToShortDateString());
                        result.Args.Add("department", employee.Department);
                        result.Args.Add("description", employee.Description);
                        result.Args.Add("name", employee.Name);

                        // try to survive one update failing
                        try
                        {
                            if (!service.UpdateGroupMemberships(employee, executionMode)) continue;

                            syncCnt++;
                            results.Add(result);

                            if (Config.SyncLimit > 0 && syncCnt >= Config.SyncLimit)
                            {
                                Log.Logger.Information("Sync limit {x} reached, exiting...", Config.SyncLimit);
                                break;
                            }
                        }
                        catch (ArgumentNullException e)
                        {
                            result.ResultType = ResultType.Error;
                            result.Success = false;
                            
                            result.Args.Add("error", e.ParamName);

                            if (!string.IsNullOrEmpty(employee.Title))
                                result.Args.Add("title", employee.Title);
                            if (!string.IsNullOrEmpty(employee.ExtensionAttribute3))
                                result.Args.Add("ext3", employee.ExtensionAttribute3);

                            results.Add(result);
                        }
                        catch (Exception e)
                        {
                            Log.Logger.Fatal("Failed to update employee {0} ({1}/{2}) -> {3}", employee.SamAccountName,
                                employee.EmployeeId, employee.PrimaryKey, e.Message);
                            if (!string.IsNullOrEmpty(e.StackTrace))
                                Log.Logger.Debug(e.StackTrace);
                        }
                    }
                }

                Console.WriteLine(JsonSerializer.Serialize(results, Json.DefaultSerializerOutputStyle));
                return true;
            });
        }

        public void Employees(List<string> args)
        {
            var executionMode = Config.SyncMode();
            int syncCnt = 0;
            var results = new List<Result>();

            ProcessImportCommand(executionMode, args, (employees, executionMode) =>
            {
                using (var service = new ActiveDirectoryService())
                {
                    foreach (var employee in employees)
                    {
                        // try to survive one update failing
                        try
                        {
                            var changeLog = service.UpdateEmployee(employee, executionMode);

                            if (changeLog.Count == 0) continue;

                            foreach (var change in changeLog)
                            {
                                if (Config.NotifyOnChange(change.PropertyName))
                                {
                                    var result = new Result() { ResultType = ResultType.Change };
                                    result.Args.Add("id", employee.EmployeeId);
                                    result.Args.Add("pkey", employee.PrimaryKey);
                                    result.Args.Add("old", change.OldValue);
                                    result.Args.Add("new", change.NewValue);
                                    result.Args.Add("date", DateTime.Now.ToShortDateString());
                                    result.Args.Add("description", change.PropertyName);
                                    results.Add(result);
                                }
                            }

                            syncCnt++;

                            if (Config.SyncLimit > 0 && syncCnt >= Config.SyncLimit)
                            {
                                Log.Logger.Information("Sync limit {x} reached, exiting...", Config.SyncLimit);
                                break;
                            }
                        }
                        catch (Exception e)
                        {
                            Log.Logger.Fatal("Failed to update employee {0} ({1}/{2}) -> {3}", employee.SamAccountName,
                                employee.EmployeeId, employee.PrimaryKey, e.Message);
                            if (!string.IsNullOrEmpty(e.StackTrace))
                                Log.Logger.Debug(e.StackTrace);
                        }
                    }
                }

                Console.WriteLine(JsonSerializer.Serialize(results, Json.DefaultSerializerOutputStyle));
                return true;
            });
        }

        public void Groups(List<string> args)
        {
            var executionMode = Config.SyncMode();
            int syncCnt = 0;
            var results = new List<Result>();

            ProcessImportCommand(executionMode, args, (employees, executionMode) =>
            {
                using (var service = new ActiveDirectoryService())
                {
                    foreach (var employee in employees)
                    {
                        // try to survive one new hire failing
                        try
                        {
                            if (!service.AddPolicyGroups(employee, executionMode)) continue;

                            syncCnt++;

                            if (Config.SyncLimit > 0 && syncCnt >= Config.SyncLimit)
                            {
                                Log.Logger.Warning("Sync limit {x} reached, exiting...", Config.SyncLimit);
                                break;
                            }
                        }
                        catch (Exception e)
                        {
                            Log.Logger.Fatal(e.Message);
                            if (!string.IsNullOrEmpty(e.StackTrace)) Log.Logger.Fatal(e.StackTrace);
                        }
                    }
                }

                Console.WriteLine(JsonSerializer.Serialize(results, Json.DefaultSerializerOutputStyle));
                return true;
            });
        }

        public void Hires(List<string> args)
        {
            var executionMode = Config.HireMode();
            int hireCnt = 0;
            var results = new List<Result>();

            ProcessImportCommand(executionMode, args, (employees, executionMode) =>
            {
                using (var service = new ActiveDirectoryService())
                {
                    foreach (var employee in employees)
                    {
                        var result = new Result() { ResultType = ResultType.NewHire };
                        result.Args.Add("id", employee.EmployeeId);
                        result.Args.Add("pkey", employee.PrimaryKey);
                        result.Args.Add("date", employee.HireDate?.ToShortDateString());
                        result.Args.Add("department", employee.Department);
                        result.Args.Add("description", employee.Description);
                        result.Args.Add("name", employee.Name);

                        // try to survive one new hire failing
                        try
                        {
                            if (!service.AddEmployee(employee, executionMode)) continue;

                            hireCnt++;
                            results.Add(result);

                            if (Config.HireLimit > 0 && hireCnt >= Config.HireLimit)
                            {
                                Log.Logger.Warning("Hire limit {x} reached, exiting...", Config.HireLimit);
                                break;
                            }
                        }
                        catch (ArgumentNullException e)
                        {
                            result.ResultType = ResultType.Error;
                            result.Success = false;

                            result.Args.Add("error", e.ParamName);
                            results.Add(result);

                            if (!string.IsNullOrEmpty(employee.Title))
                                result.Args.Add("title", employee.Title);
                            if (!string.IsNullOrEmpty(employee.ExtensionAttribute3))
                                result.Args.Add("ext3", employee.ExtensionAttribute3);
                        }
                        catch (Exception e)
                        {
                            Log.Logger.Fatal(e.Message);
                            if (!string.IsNullOrEmpty(e.StackTrace)) Log.Logger.Fatal(e.StackTrace);
                        }
                    }
                }

                Console.WriteLine(JsonSerializer.Serialize(results, Json.DefaultSerializerOutputStyle));
                return true;
            });
        }

        public void Terms(List<string> args)
        {
            var executionMode = Config.TermMode();
            int termCnt = 0;
            var results = new List<Result>();

            ProcessImportCommand(executionMode, args, (employees, executionMode) =>
            {
                using (var service = new ActiveDirectoryService())
                {
                    foreach (EmployeeDirectoryEntry employee in employees)
                    {
                        try
                        {
                            if (!service.TermEmployee(employee, executionMode)) continue;

                            var result = new Result() { ResultType = ResultType.Termination };
                            result.Args.Add("id", employee.EmployeeId);
                            result.Args.Add("pkey", employee.PrimaryKey);
                            result.Args.Add("date", employee.TermDate?.ToShortDateString());
                            result.Args.Add("department", employee.Department);
                            result.Args.Add("description", employee.Description);
                            result.Args.Add("name", employee.Name);
                            results.Add(result);

                            termCnt++;

                            if (Config.TermLimit > 0 && termCnt >= Config.TermLimit)
                            {
                                Log.Logger.Warning("Term limit {x} reached, exiting...", Config.TermLimit);
                                break;
                            }
                        }
                        catch (Exception e)
                        {
                            Log.Logger.Fatal(e.Message);
                            if (!string.IsNullOrEmpty(e.StackTrace)) Log.Logger.Fatal(e.StackTrace);
                        }
                    }
                }

                Console.WriteLine(JsonSerializer.Serialize(results, Json.DefaultSerializerOutputStyle));
                return true;
            });
        }
    }
}