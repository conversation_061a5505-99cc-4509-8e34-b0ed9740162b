﻿using Payroll.Shared;
using Serilog;
using AD.Shared;
using System.ComponentModel;
using System.Text.Json;

namespace AD.Tool
{
    class ExportCommand
    {
        private void ProcessImportCommand(List<string> args, Func<IEnumerable<Payroll.Shared.EmployeeDirectoryEntry>, bool> func)
        {
            try
            {
                if (!ConsoleService.TryGetDirectoryEntriesFromInput(out var employees))
                {
                    Log.Logger.Error("Failed to parse employee directory entries list");
                    return;
                }

                if (employees == null)
                {
                    Log.Logger.Error("Failed to load employee directory entries list");
                    return;
                }

                func(employees);
            }
            catch (Exception e)
            {
                Log.Logger.Fatal(e.Message);
                if (!string.IsNullOrEmpty(e.StackTrace)) Log.Logger.Fatal(e.StackTrace);
            }
        }

        public void Employees(List<string> args)
        {
            ProcessImportCommand(args, (employeeDirectoryEntries) =>
            {
                var employees = new List<Employee>();
                using (var service = new ActiveDirectoryService())
                {
                    foreach (var ede in employeeDirectoryEntries)
                    {
                        var dEntry = service.GetUserDirectoryEntryByPrimaryKey(ede.PrimaryKey);
                        if (dEntry == null)
                        {
                            continue;
                        }
                        var employee = service.ConvertDirectoryEntryToEmployee(dEntry);

                        employee.AddAttribute("division_code", ede.DivisionCode);
                        employee.AddAttribute("badge_no", ede.BadgeNo);
                        
                        employees.Add(employee);
                    }

                    Console.WriteLine(JsonSerializer.Serialize(employees, Json.DefaultSerializerOutputStyle));
                    return true;
                }
            });
        }
    }
}