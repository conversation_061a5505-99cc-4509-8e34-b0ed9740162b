using System;
using System.Collections.Generic;
using Payroll.Shared;
using Serilog;
using CommandLine;

namespace AD.Tool
{
    public class Program : ProgramBase<SettingCommand>
    {
        static readonly string AppVersion = "1.1";

        public override int ShowUsage()
        {
            Console.WriteLine("Usage: AD.Tool.exe <command> <command-args>");
            Console.WriteLine("  where <command> is one of...");
            Console.WriteLine("   - employee [list, view] = employee management");
            Console.WriteLine("   - export [employees] = export utilities");
            Console.WriteLine("   - filter [exists] = filter utilities");
            Console.WriteLine("   - group [list] = group management");
            Console.WriteLine("   - info - show version and tool information");
            Console.WriteLine("   - import [changes, employees] = import utilities");
            Console.WriteLine("   - search [sam, pkey, cn, dn, last] = search utilities");
            Console.WriteLine("   - setting = settings management");

            Console.WriteLine();
            Console.WriteLine("Notes");
            Console.WriteLine("- The 'import employees' command is used to update existing employee records in AD.");
            Console.WriteLine("- The 'import hires' command is used to add new employee records in AD.");
            Console.WriteLine("- The 'export employees' command is used convert a JSON array of directory entries to employee records.");
            Console.WriteLine("- All other commands are used to explore AD view search and list operations.");

            return 0;
        }

        public int Help(List<string> args)
        {
            return ShowUsage();
        }

        public void Info(List<string> args)
        {
            try
            {
                Payroll.Shared.Setting.Init();
                Console.WriteLine($"AD.Tool.exe version {AppVersion} ({Payroll.Shared.Setting.IniFilePath})");
                Console.WriteLine($"  Branch: {BuildInfo.Branch}, Built: {BuildInfo.Built}, Host: {BuildInfo.Host}, Hash: {BuildInfo.Commit}");
                Console.WriteLine();

                Console.WriteLine("Settings");
                Console.WriteLine("===================================");
                var list = Payroll.Shared.Setting.ListSection("ad");
                foreach (var e in list)
                {
                    Console.WriteLine($"{e.Key} = {e.Value}");
                }

                Console.WriteLine();

                Console.WriteLine(value: "Skip Groups");
                Console.WriteLine("===================================");
                foreach (var e in Shared.Config.GroupsToSkip.ToList())
                {
                    Console.WriteLine(e);
                }

                Console.WriteLine(value: "Sensitive Properties");
                Console.WriteLine("===================================");
                foreach (var e in Shared.Config.SensitiveProps.ToList())
                {
                    Console.WriteLine(e);
                }

                Console.WriteLine();
                Console.WriteLine("To run in execution mode...");
                Console.WriteLine("  AD.Tool.exe setting term_mode execute - to enable terminated employee sync");
                Console.WriteLine("  AD.Tool.exe setting hire_mode execute - to enable new hire sync");
                Console.WriteLine("  AD.Tool.exe setting sync_mode execute - to enable existing employee sync");
            }
            catch (Exception e)
            {
                Log.Logger.Error(e.Message);
                Log.Logger.Error(e.StackTrace ?? "");
            }
        }

        private int ExecCommand<T>(List<string> args) where T : new()
        {
            var command = new CommandArguments(args);
            return Command<T>.Invoke(command);
        }

        public void Employee(List<string> args)
        {
            ExecCommand<EmployeeCommand>(args);
        }

        public void Export(List<string> args)
        {
            ExecCommand<ExportCommand>(args);
        }

        public void Filter(List<string> args)
        {
            ExecCommand<FilterCommand>(args);
        }

        public void Group(List<string> args)
        {
            ExecCommand<GroupCommand>(args);
        }

        public void Import(List<string> args)
        {
            ExecCommand<ImportCommand>(args);
        }

        public void Search(List<string> args)
        {
            ExecCommand<SearchCommand>(args);
        }

        public void Settings(List<string> args)
        {
            ExecCommand<SettingCommand>(args);
        }

        static int Main(string[] args)
        {
            // setup logging services...
            var command = string.Join(" ", args);
            Logger.Setup($"AD.Tool.Exe - Command: '{command}', Version: {AppVersion}");

            // Log version information
            Log.Logger.Information($"AD.Tool.exe version {AppVersion} ({Payroll.Shared.Setting.IniFilePath})");
            Log.Logger.Information($"  Branch: {BuildInfo.Branch}, Built: {BuildInfo.Built}, Host: {BuildInfo.Host}, Hash: {BuildInfo.Commit}");

            try
            {
                Parser.Default.ParseArguments<ProgramArguments>(args)
                    .MapResult((ProgramArguments opts) => ProgramDriver<Program>.Run(opts), errs => 1);
            }
            catch (Exception e)
            {
                Log.Logger.Error(e.Message);
                Log.Logger.Error(e.StackTrace);
                return -1;
            }

            return 0;
        }
    }
}