﻿using Payroll.Shared;
using Serilog;
using AD.Shared;
using System.ComponentModel;
using System.Text.Json;

namespace AD.Tool
{
    class FilterCommand
    {
        public void Exists(List<string> args)
        {
            try
            {
                if (!ConsoleService.TryGetEmployeesFromInput(out var employees))
                    return;

                var notFound = new List<Employee>();
                using (var service = new ActiveDirectoryService())
                {
                    foreach (var employee in employees)
                    {
                        var primaryKey = employee.Id;
                        var directoryEntry = service.GetUserDirectoryEntryByPrimaryKey(primaryKey);

                        if (directoryEntry == null)
                            notFound.Add(employee);
                        else
                        {
                            if (OperatingSystem.IsWindows())
                                Log.Logger.Debug("Found {0} {1}", primaryKey, directoryEntry.Path);
                        }
                    }
                }

                Console.WriteLine(JsonSerializer.Serialize(notFound, Json.DefaultSerializerOutputStyle));
            }
            catch (Exception e)
            {
                Log.Logger.Fatal(e.Message);
                if (!string.IsNullOrEmpty(e.StackTrace)) Log.Logger.Fatal(e.StackTrace);
            }
        }
    }
}