#!/bin/sh

# This script refreshes the local cache by asking Paycom for a complete record of all active employees.
# Terminations will be picked up by the cache refresh as terminations are a "change".
# This is a lengthy operation and makes the most API calls.

DOM=`date +%d`
LOGFILE=../Logs/Paycom/paycom-$DOM.log dotnet paycom2/Paycom2.Tool.dll cache init
LOGFILE=../Logs/Paycom/paycom-$DOM.log dotnet paycom2/Paycom2.Tool.dll cache terms
