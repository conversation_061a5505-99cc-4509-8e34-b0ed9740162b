#!/bin/sh

DOM=`date +%d`
dotnet paycom2/Paycom2.Tool.dll cache dump term 60 > ../terms.json
cat ../terms.json | LOGFILE=../Logs/Crunch/terms-$DOM.log dotnet crunch/CrunchTime.Tool.dll import terms all doit

dotnet paycom2/Paycom2.Tool.dll cache dump cseq | dotnet client/SlimChicken.Tool.dll import employees > ../employees.json
cat ../employees.json | LOGFILE=../Logs/Crunch/hires-$DOM.log dotnet crunch/CrunchTime.Tool.dll import hires all doit
cat ../employees.json | LOGFILE=../Logs/Crunch/sync-$DOM.log dotnet crunch/CrunchTime.Tool.dll import sync all doit
