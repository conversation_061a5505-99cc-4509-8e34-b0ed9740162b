#!/bin/bash

# HOUSEKEEPING
WEEKNUM=`date +%V`

LOGFILE=../Logs/Crunch/time-$WEEKNUM.log dotnet ./crunch/CrunchTime.Tool.dll export time all 7 1 > ../Mailbox/time.json
cat ../Mailbox/time.json | LOGFILE=../Logs/Paycom/paycom-punches-$WEEKNUM.log dotnet paycom2/Paycom2.Tool.dll import punches doit

cp ../Mailbox/time.json ../Archive/time-$WEEKNUM.json

# only keep X days worth of files
find /opt/openarc/Logs/ -type f -mtime +60 -delete
find /opt/openarc/Mailbox/ -type f -mtime +180 -delete
