#!/bin/sh
# This script makes sure that we have all the directories set up that we need, and that the cache is initialized
hcp profile set vault-secrets/app slimchicken-corporate && hcp profile init --vault-secrets
hcp vault-secrets run -- dotnet paycom2/Paycom2.Tool.dll export settings > /etc/profile.d/ptools.sh
hcp vault-secrets run -- dotnet crunch/CrunchTime.Tool.dll export settings >> /etc/profile.d/ptools.sh

echo "To enable the weekly punch export service, run the following commands:"
echo sudo cp configs/cache-changes.service /etc/systemd/system/
echo sudo cp configs/cache-refresh.service /etc/systemd/system/
echo sudo cp configs/export-punches.service /etc/systemd/system/
echo sudo cp configs/export-punches.timer /etc/systemd/system/
echo sudo cp configs/cache-changes.timer /etc/systemd/system/
echo sudo cp configs/cache-refresh.timer /etc/systemd/system/
echo sudo cp configs/sync-employees.service /etc/systemd/system/
echo sudo cp configs/sync-employees.timer /etc/systemd/system/
echo sudo systemctl daemon-reload
echo sudo systemctl enable export-punches.service
echo sudo systemctl enable export-punches.timer
echo sudo systemctl start export-punches.timer
echo sudo systemctl enable cache-changes.service
echo sudo systemctl enable cache-changes.timer
echo sudo systemctl start cache-changes.timer
echo sudo systemctl enable cache-refresh.service
echo sudo systemctl enable cache-refresh.timer
echo sudo systemctl start cache-refresh.timer
echo sudo systemctl enable sync-employees.service
echo sudo systemctl enable sync-employees.timer
echo sudo systemctl start sync-employees.timer
