﻿using System;
using System.Collections;
using System.Collections.Generic;
using System.Text.Json;
using Payroll.Shared;
using Serilog;

namespace SlimChicken.Tool
{
    class ImportCommand
    {
        public void Pto(List<string> args)
        {
            try
            {
                if (!ConsoleService.TryGetPunchesFromInput(out List<PunchPair> punchPairs))
                {
                    Log.Logger.Error("Failed to parse pto list");
                    return;
                }

                if (punchPairs == null)
                {
                    Log.Logger.Warning("No pto detected.");
                    return;
                }

                var retailPTO = new List<PunchPair>();
                foreach (PunchPair pair in punchPairs)
                {
                    
                    // if(pair.JobCode == "Salary"){ //remove Salaried employee pto
                    //     Log.Logger.Information("Skipping {0}/{1} as it is a salaried employee", pair.Id, pair.ClockSeq);
                    //     continue;
                    // }

                    if (string.IsNullOrEmpty(pair.Location))
                    {
                        Log.Logger.Warning("Employee {0}/{1} has an empty dept code.", pair.Id, pair.ClockSeq);
                        continue;
                    }
                    
                    // these are filters to remove non-retail employees
                    // if (pair.Location.StartsWith("100") || pair.Location.StartsWith("200"))
                    //     continue;

                    // pair.Location = pair.Location.Tail(2).Trim();
                    // pair.ClockSeq = pair.ClockSeq.TrimStart('0');

                    retailPTO.Add(pair);
                }

                string formattedJson = System.Text.Json.JsonSerializer.Serialize(retailPTO, Json.DefaultSerializerOutputStyle);
                Console.WriteLine(formattedJson);
            }
            catch (Exception e)
            {
                Log.Logger.Fatal(e.Message);
                Log.Logger.Fatal(e.StackTrace ?? "");
            }
        }

        public void Employees(List<string> args)
        {
            try
            {
                if (!ConsoleService.TryGetEmployeesFromInput(out var employees))
                {
                    Log.Logger.Error("Failed to parse employees list");
                    return;
                }

                if (employees.Count == 0)
                {
                    Console.WriteLine("[]");
                    return;
                }

                var retailEmployees = new List<Employee>();
                foreach (var employee in employees)
                {
                    // if (string.IsNullOrEmpty(employee.DeptCode))
                    // {
                    //     Log.Logger.Warning("Employee {0}/{1} has an empty dept code.", employee.Id, employee.ClockSeq);
                    //     continue;
                    // }
                    
                    // employee.PrimaryWorkLocation = employee.DeptCode.Tail(2).Trim();
                    // employee.ClockSeq = employee.ClockSeq.TrimStart('0');
                    
                    // these are filters to remove non-retail employees
                    if (employee.PrimaryWorkLocation == "100" || employee.PrimaryWorkLocation == "UN" || employee.PrimaryWorkLocation.StartsWith("999"))
                        continue;

                    if (string.IsNullOrEmpty(employee.PersonalEmail))
                    {
                        Log.Logger.Warning("Employee {0}/{1} has no email address. Skipping.", employee.Id, employee.ClockSeq);
                        continue;
                    }

                    retailEmployees.Add(employee);
                }

                //foreach (var employee in retailEmployees)
                //{
                //  Console.WriteLine($"{employee.Id}\t{employee.DeptCode.Tail(2)}\t{employee.PrimaryWorkLocation}");
                //}

                string formattedJson = System.Text.Json.JsonSerializer.Serialize(retailEmployees, Json.DefaultSerializerOutputStyle);
                Console.WriteLine(formattedJson);
            }
            catch (Exception e)
            {
                Log.Logger.Fatal(e.Message);
                Log.Logger.Fatal(e.StackTrace ?? "");
            }
        }
 
        public void Punches(List<string> args)
        {
            if (!ConsoleService.TryGetPunchesFromInput(out List<PunchPair> punchPairs))
            {
                Log.Logger.Error("Failed to parse punches list");
                return;
            }

            if (punchPairs == null)
            {
                Log.Logger.Warning("No punch pairs detected.");
                return;
            }

            var breaks = 0;
            var pairs = 0;

            foreach (PunchPair pair in punchPairs)
            {
                //remove ignored locations from punches
                if (Config.SkipLocation(pair.Location)) continue;
                pair.ClockSeq = pair.ClockSeq.PadLeft(6, '0');
                
                pairs++;
                breaks += pair.Breaks.Count;
            }

            ConsoleService.PrintFormattedJson(punchPairs);

            Log.Logger.Information("Processed {x} punches, and {y} breaks", pairs, breaks);
        }
    }
}
