﻿using System;
using System.Collections.Generic;
using CommandLine;
using Payroll.Shared;
using System.Linq;

namespace SlimChicken.Tool
{
    class Program : ProgramBase<SettingCommand>
    {
        private static readonly string AppVersion = "1.0";

        static int DoShowUsage()
        {
            Console.WriteLine("Usage: SlimChicken.Tool.exe <command> <command-args>");
            Console.WriteLine("  where <command> is one of...");
            Console.WriteLine("   - import [employees, punches, pto] = compare two input files and show differences");
            Console.WriteLine("   - info - show version and tool information");

            return 1;
        }

        public override int ShowUsage()
        {
            return DoShowUsage();
        }

        public int Help(List<string> args)
        {
            return DoShowUsage();
        }

        private int ExecCommand<T>(List<string> args) where T : new()
        {
            var command = new CommandArguments(args);
            return Command<T>.Invoke(command);
        }

        public int Import(List<string> args)
        {
            return ExecCommand<ImportCommand>(args);
        }

        public int Info(List<string> args)
        {
            Console.WriteLine($"SlimChicken.Tool.exe version {AppVersion}");
            Console.WriteLine();

            return 0;
        }

        static int Main(string[] args)
        {
            if (args.Length == 0)
            {
                DoShowUsage();
                return ExitCode.BadUsage;
            }

            // setup logging services...
            var command = string.Join(" ", args);
            Payroll.Shared.Logger.Setup($"SlimChicken.Tool.Exe - Command: '{command}', Version: {AppVersion}");

            int rc = 0;
            try
            {
                Parser.Default.ParseArguments<ProgramArguments>(args)
                    .MapResult((ProgramArguments opts) => rc = ProgramDriver<Program>.Run(opts), errs => 1);
            }
            catch (Exception e)
            {
                Console.WriteLine(e.Message);
                Console.WriteLine(e.StackTrace);
                return ExitCode.Exception;
            }

            return rc;
        }
    }
}
