using Payroll.Shared;
using Serilog;
using System.Text.Json;

namespace FourJays.Tool
{
    public class ExportCommand
    {
        public void Employees(List<string> args)
        {
            ProcessExportCommand(args, "Employee Export",
                (Payroll.Shared.Employee employee) =>
                {
                    var csvRow = new List<string>
                    {
                        employee.PrimaryWorkLocation,                    // Store Number
                        employee.ClockSeq,                              // Employee Number
                        employee.DateOfBirth?.ToString("MM/dd/yyyy") ?? "",   // Birthday
                        employee.HireDate?.ToString("MM/dd/yyyy") ?? "",      // Hire Date
                        employee.LastName,                              // Last Name
                        employee.FirstName,                             // First Name
                        "",                                             // Middle Initial
                        employee.Attributes?.GetValueOrDefault("state_marital_status", "") ?? "", // State Marital Status
                        employee.Attributes?.GetValueOrDefault("federal_marital_status", "") ?? "", // Federal Marital Status
                        employee.Attributes?.GetValueOrDefault("race_code", "") ?? "",    // Race Code
                        employee.Attributes?.GetValueOrDefault("citizen", "") ?? "",      // Citizen
                        employee.Jobs.FirstOrDefault()?.Code ?? "",     // Job Code
                        employee.Jobs.FirstOrDefault()?.Rate.ToString() ?? "", // Pay Rate
                        employee.Description ?? "",                     // Term Code
                        employee.TermDate?.ToString("MM/dd/yyyy") ?? "",     // Term Date
                        employee.Id,                                    // Payroll ID
                        employee.Attributes?.GetValueOrDefault("home_store_override", "") // Home Store Override
                    };

                    return string.Join(",", csvRow.Select(field => $"\"{field}\""));
                });
        }

        private void ProcessExportCommand(List<string> args, string operationLabel,
            Func<global::Payroll.Shared.Employee, string> processEmployee)
        {
            try
            {
                if (!ConsoleService.TryGetEmployeesFromInput(out var employeesToProcess))
                {
                    Log.Logger.Error("Failed to parse employee list");
                    return;
                }

                if (employeesToProcess == null)
                {
                    Log.Logger.Error("Failed to load employee list");
                    return;
                }

                var outputPath = Path.Combine(Environment.CurrentDirectory, $"employee_export_{DateTime.Now:yyyyMMdd_HHmmss}.csv");
                var headers = new[]
                {
                    "Store Number", "Employee Number", "Birthday", "Hire Date", "Last Name", "First Name",
                    "Middle Initial (Optional)", "Address 1", "Address 2 (Optional)", "City", "State Abbreviation",
                    "Zip Code", "Social Number", "Email", "Home Phone", "FT/PT Status", "State Marital Status",
                    "Federal Marital Status", "Race Code", "Gender", "Citizen", "Job Code", "Pay Rate",
                    "Term Code", "Term Date", "Payroll ID", "Home Store Override"
                };

                Log.Logger.Information("Starting {op}", operationLabel);

                using (var writer = new StreamWriter(outputPath))
                {
                    // Write headers
                    writer.WriteLine(string.Join(",", headers.Select(h => $"\"{h}\"")));

                    // Process each employee
                    foreach (var employee in employeesToProcess)
                    {
                        var csvLine = processEmployee(employee);
                        writer.WriteLine(csvLine);
                        Log.Logger.Information("Processed employee {id}/{seq}", employee.Id, employee.ClockSeq);
                    }
                }

                Log.Logger.Information("Export completed. File saved to: {path}", outputPath);
            }
            catch (Exception e)
            {
                Log.Logger.Fatal(e, "Error during {op}", operationLabel);
            }
        }
    }
}
