﻿// See https://aka.ms/new-console-template for more information
using CommandLine;
using LiteDB;
using Payroll.Shared;
using Serilog;

using System.Globalization;
using System.Reflection;

namespace FourJays.Tool
{
    [AttributeUsage(AttributeTargets.Assembly)]
    internal class BuildDateAttribute : Attribute
    {
        public BuildDateAttribute(string value)
        {
            DateTime = DateTime.ParseExact(value, "yyyyMMddHHmmss", CultureInfo.CurrentCulture, DateTimeStyles.None);
        }

        public DateTime DateTime { get; }
    }

    class Program : ProgramBase2
    {
        static readonly string AppVersion = "0.1"; // paycom skip jobs

        private static DateTime GetBuildDate()
        {
            var assembly = Assembly.GetExecutingAssembly();
            var attribute = assembly.GetCustomAttribute<BuildDateAttribute>();
            return attribute?.DateTime ?? default(DateTime);
        }

        static int DoShowUsage()
        {
            Console.WriteLine("Usage: FourJays.Tool.exe <command> <command-args>");
            Console.WriteLine($"  Branch: {BuildInfo.Branch}, Built: {BuildInfo.Built}, Host: {BuildInfo.Host}, Hash: {BuildInfo.Commit}");
            Console.WriteLine();

            Console.WriteLine("  where <command> is one of...");
            Console.WriteLine("   - import [employees | locations]");
            Console.WriteLine("   - export [employees]");
            Console.WriteLine("   - fix [employees]");

            return 0;
        }

        public override int ShowUsage()
        {
            return DoShowUsage();
        }

        public void Help(List<string> args)
        {
            ShowUsage();
        }

        public void Info(List<string> args)
        {
            var appConfig = new AppConfig();
            Console.WriteLine($"FourJays.Tool.exe, version: {AppVersion}, Built: {GetBuildDate()}");
        }

        private int ExecCommand<T>(List<string> args) where T : new()
        {
            var command = new CommandArguments(args);

            return Command<T>.Invoke(command);
        }

        public void Import(List<string> args)
        {
            ExecCommand<ImportCommand>(args);
        }

        public void Export(List<string> args)
        {
            ExecCommand<ExportCommand>(args);
        }

        static int Main(string[] args)
        {
            if (args.Length == 0)
            {
                DoShowUsage();
                return -1;
            }

            // setup logging services...
            var command = string.Join(" ", args);
            Logger.Setup($"FourJays.Tool, Command: '{command}', Version: {AppVersion}");

            try
            {
                Parser.Default.ParseArguments<ProgramArguments>(args)
                    .MapResult((ProgramArguments opts) => ProgramDriver<Program>.Run(opts), errs => 1);
            }
            catch (Exception e)
            {
                Log.Logger.Error(e.Message);
                Log.Logger.Error(e.StackTrace);
                return -1;
            }

            return 0;
        }
    }
}
