﻿using System;
using System.Collections.Generic;
using Payroll.Shared;

namespace FourJays.Tool
{
    public class AppConfig
    {
        public static readonly string SkipLocationSection = "fourjays_skip_locations";
        public static readonly string LocationMapSection = "location_map";

        public HashSet<string> LocationsToSkip { get; private set; } = new HashSet<string>();
        public Dictionary<string, string> CrunchTimeToPaycomLocationMap { get; private set; } = new Dictionary<string, string>();
        public Dictionary<string, string> PaycomToCrunchTimeLocationMap { get; private set; } = new Dictionary<string, string>();

        public AppConfig()
        {
            {
                var skiplist = Setting.ListSection(SkipLocationSection);
                LocationsToSkip = skiplist.Keys.ToHashSet();

                var locationMap = Setting.ListSection(LocationMapSection);
                CrunchTimeToPaycomLocationMap = locationMap;
                PaycomToCrunchTimeLocationMap = locationMap.ToDictionary(item => item.Value, item => item.Key);
            }
        }

        public bool SkipLocation(string location)
        {
            if (string.IsNullOrEmpty(location)) return false;

            return LocationsToSkip.Contains(location);
        }

        public string MapCrunchTimeLocationToPaycomLocation(string location)
        {
            if (string.IsNullOrEmpty(location)) return location;
            
            return CrunchTimeToPaycomLocationMap.TryGetValue(location, out var mappedLocation) 
                ? mappedLocation 
                : location;
        }

        public string MapPaycomLocationToCrunchTimeLocation(string location)
        {
            if (string.IsNullOrEmpty(location)) return location;
            
            return PaycomToCrunchTimeLocationMap.TryGetValue(location, out var mappedLocation) 
                ? mappedLocation 
                : location;
        }
    }
}

