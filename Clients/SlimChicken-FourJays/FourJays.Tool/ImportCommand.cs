using Serilog;
using System.Text.Json;
using CsvHelper;
using System.Globalization;
using System.IO;
using Payroll.Shared;
using CrunchTime.Tool;
using MongoDB.Driver;

namespace FourJays.Tool
{
    public class ImportCommand
    {
        public void Locations(List<string> args)
        {
            try
            {
                var locations = new List<Payroll.Shared.Location>();

                // Read CSV from STDIN
                using (var reader = new System.IO.StreamReader(Console.OpenStandardInput()))
                using (var csv = new CsvHelper.CsvReader(reader, System.Globalization.CultureInfo.InvariantCulture))
                {
                    // Skip the header row
                    csv.Read();
                    csv.ReadHeader();

                    // Read each row
                    while (csv.Read())
                    {
                        var location = new Payroll.Shared.Location
                        {
                            Id = csv.GetField("Store Number"),
                            Name = $"Store {csv.GetField("Store Number")}",
                            Code = csv.GetField("Corp ID"),
                            StreetAddress = csv.GetField("Street"),
                            CityAddress = csv.GetField("City"),
                            State = csv.GetField("State"),
                            Zip = csv.GetField("ZipCode"),
                            Active = true
                        };

                        locations.Add(location);
                    }
                }

                if (locations.Count == 0)
                {
                    Console.WriteLine("[]");
                    return;
                }

                ConsoleService.PrintFormattedJson(locations);
            }
            catch (Exception e)
            {
                Log.Logger.Error(e.Message);
                Log.Logger.Error(e.StackTrace);
            }
        }

        private int IsCachedEmployee(string employeeNumber, string locationCode)
        {
            // check if the employee is in the cache and has the correct location code
            var employee = CacheService.FetchRecord<CrunchTimeEmployee>(CrunchTime.Tool.AppConfig.POS_EMPLOYEES_CACHE_COLLECTION, employeeNumber);
            if (employee == null) return -1;
            if (employee.PrimaryLocationCode != locationCode) return 0;
            return 1;
        }

        private bool IsValidEmployee(Employee employee, AppConfig config)
        {
            if (string.IsNullOrEmpty(employee.ClockSeq))
            {
                Log.Logger.Debug($"Skipping employee {employee.Id}, clockSeq is empty");
                return false;
            }

            if (string.IsNullOrEmpty(employee.PrimaryWorkLocation))
            {
                Log.Logger.Debug($"Skipping employee {employee.Id}, primary work location is empty");
                return false;
            }

            if (config.SkipLocation(employee.PrimaryWorkLocation))
            {
                Log.Logger.Debug($"Skipping employee {employee.Id}, primary work location {employee.PrimaryWorkLocation}, found on skip list");
                return false;
            }

            return true;
        }

        private void MapLocationId(Employee employee)
        {
            if (employee.PrimaryWorkLocation.Length <= 2)
            {
                employee.PrimaryWorkLocation = "140" + employee.PrimaryWorkLocation;
            }
            else
            {
                var last2digits = employee.PrimaryWorkLocation.Substring(employee.PrimaryWorkLocation.Length - 2);
                employee.PrimaryWorkLocation = "124" + last2digits;
            }
        }

        private IEnumerable<Employee> ProcessEmployeesToImport(IEnumerable<Employee> employees, string franchiseId, AppConfig config, CrunchTimeService crunchTimeService)
        {
            var employeesToImport = new List<Employee>();

            foreach (var employee in employees)
            {
                if (!IsValidEmployee(employee, config))
                    continue;

                employee.PrimaryWorkLocation = config.MapPaycomLocationToCrunchTimeLocation(employee.PrimaryWorkLocation);

                // aloha just works with normal clockseqs
                if (franchiseId == "15m04")
                {
                     // employees with job codes after 202 are managers and should have a rate of 0
                    foreach (var job in employee.Jobs)
                    {
                        if (int.TryParse(job.Code, out int jobCode) && jobCode > 202)
                        {
                            job.Rate = 0;
                            Log.Logger.Information("Set rate to 0 for employee {id} with job code {code}", employee.Id, job.Code);
                        }
                    }

                    // employees with location code 100 are managers and should have a job code of 1000
                    if (employee.PrimaryWorkLocation == "100")
                    {
                        foreach (var job in employee.Jobs)
                        {
                            var oldCode = job.Code;
                            job.Code = "1000";
                            Log.Logger.Information("Set job code from {oldCode} to 1000 for employee {id} at location {location} with rate {rate}", 
                                oldCode, employee.Id, employee.PrimaryWorkLocation, job.Rate);
                        }
                    }

                    employeesToImport.Add(employee);
                    continue;
                }

                // minimize calls to crunchtime
                var rc1 = IsCachedEmployee(employee.ClockSeq, employee.PrimaryWorkLocation);
                if (1 == rc1)
                {
                    employeesToImport.Add(employee);
                    continue;
                }

                // minimize calls to crunchtime
                var clockSeqWithPrefix = "EQY" + employee.ClockSeq;
                var rc2 = IsCachedEmployee(clockSeqWithPrefix, employee.PrimaryWorkLocation);
                if (1 == rc2)
                {
                    employee.ClockSeq = clockSeqWithPrefix;
                    employeesToImport.Add(employee);
                    continue;
                }

                // maybe employee details are in crunchtime but not cached?
                Log.Logger.Warning($"Failed to fetch employee from the cache {employee.ClockSeq}, errors {rc1} {rc2}");
                var employeeDetails = crunchTimeService.GetEmployeeDetailsAsync(employee.ClockSeq, false).Result;
                if (employeeDetails != null && employeeDetails.PrimaryLocationCode == employee.PrimaryWorkLocation)
                {
                    employeesToImport.Add(employee);
                    continue;
                }

                Log.Logger.Warning($"Failed to fetch employee details for {employee.ClockSeq}, trying EQY prefix... {employee.PrimaryWorkLocation}, {employeeDetails?.PrimaryLocationCode}");
                employeeDetails = crunchTimeService.GetEmployeeDetailsAsync(clockSeqWithPrefix, false).Result;

                if (employeeDetails != null && employeeDetails.PrimaryLocationCode == employee.PrimaryWorkLocation)
                {
                    Log.Logger.Warning($"Found employee details for {clockSeqWithPrefix}, using it...");
                    employee.ClockSeq = clockSeqWithPrefix;
                    employeesToImport.Add(employee);
                }

               

                Log.Logger.Warning($"Failed to fetch employee details for {clockSeqWithPrefix}, possible new hire...");
                employeesToImport.Add(employee);
            }

            return employeesToImport;
        }

        public int Employees(List<string> args)
        {
            if (args == null || args.Count < 1)
            {
                Console.WriteLine("Usage: FourJays.Tool.exe import employees <franchise-id>");
                Console.WriteLine("  franchise-id: 15M02, 15M03, 15M04");
                return 1;
            }

            var franchiseId = args[0].Trim().ToLower();
            if (franchiseId != "15m02" && franchiseId != "15m03" && franchiseId != "15m04")
            {
                Console.WriteLine("Invalid franchise-id");
                return 1;
            }

            Log.Logger.Information("Processing employees for franchise {franchiseId}...", franchiseId);

            try
            {
                var config = new AppConfig();
                using var crunchTimeService = new CrunchTimeService();

                if (!ConsoleService.TryGetEmployeesFromInput(out var employees))
                {
                    Log.Logger.Error("Failed to parse employees list");
                    return 1;
                }

                if (employees.Count() == 0)
                {
                    Console.WriteLine("[]");
                    return 0;
                }

                var employeesToImport = ProcessEmployeesToImport(employees, franchiseId, config, crunchTimeService);
                ConsoleService.PrintFormattedJson(employeesToImport);
            }
            catch (Exception ex)
            {
                Log.Logger.Error(ex, "Error importing employees");
            }

            return 0;
        }

        public int Hires(List<string> args)
        {
            if (args == null || args.Count < 1)
            {
                Console.WriteLine("Usage: FourJays.Tool.exe import employees <franchise-id>");
                Console.WriteLine("  franchise-id: 15M02, 15M03, 15M04");
                return 1;
            }

            var franchiseId = args[0].Trim().ToLower();
            if (franchiseId != "15m02" && franchiseId != "15m03" && franchiseId != "15m04")
            {
                Console.WriteLine("Invalid franchise-id");
                return 1;
            }

            Log.Logger.Information("Processing employees for franchise {franchiseId}...", franchiseId);

            try
            {
                var config = new AppConfig();
                using var crunchTimeService = new CrunchTimeService();

                if (!ConsoleService.TryGetEmployeesFromInput(out var employees))
                {
                    Log.Logger.Error("Failed to parse employees list");
                    return 1;
                }

                if (employees.Count() == 0)
                {
                    Console.WriteLine("[]");
                    return 0;
                }

                var employeesToImport = ProcessEmployeesToImport(employees, franchiseId, config, crunchTimeService);
                ConsoleService.PrintFormattedJson(employeesToImport);
            }
            catch (Exception ex)
            {
                Log.Logger.Error(ex, "Error importing employees");
            }

            return 0;
        }

        public void Punches(List<string> args)
        {
            try
            {
                var config = new AppConfig();
                if (!ConsoleService.TryGetPunchesFromInput(out var punches))
                {
                    Log.Logger.Error("Failed to parse punches list");
                    return;
                }

                if (!punches.Any())
                {
                    Console.WriteLine("[]");
                    return;
                }

                var punchesToImport = new List<PunchPair>();
                foreach (var punch in punches)
                {

                     // fix clockseq non-digits - they will break aloha
                    if (punch.ClockSeq?.StartsWith("EQY") ?? false)
                    {
                        punch.ClockSeq = punch.ClockSeq.Substring(3);
                    }

                    if (!string.IsNullOrEmpty(punch.ClockSeq))
                    {
                        punch.ClockSeq = punch.ClockSeq.PadLeft(6, '0');
                    }

                    // Map location codes
                    if (!string.IsNullOrEmpty(punch.Location))
                    {
                        var mappedLocation = config.MapCrunchTimeLocationToPaycomLocation(punch.Location);
                        if (mappedLocation != punch.Location)
                        {
                            Log.Logger.Debug($"Mapping location {punch.Location} to {mappedLocation}");
                            punch.Location = mappedLocation;
                        }
                    }

                    punchesToImport.Add(punch);
                }

                ConsoleService.PrintFormattedJson(punchesToImport);
            }
            catch (Exception ex)
            {
                Log.Logger.Error(ex, "Error importing punches");
            }
        }
    }
}