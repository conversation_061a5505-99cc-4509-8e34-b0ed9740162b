<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <OutputType>Exe</OutputType>
    <TargetFrameworks>net8.0;net9.0</TargetFrameworks>
    <ImplicitUsings>enable</ImplicitUsings>
    <Nullable>enable</Nullable>
  </PropertyGroup>

    <ItemGroup>
    <PackageReference Include="CommandLineParser" Version="2.9.1" />
    <PackageReference Include="LiteDB" Version="5.0.21" />
    <PackageReference Include="Serilog" Version="4.2.0" />
    <PackageReference Include="Serilog.Sinks.Console" Version="6.0.0" />
    <PackageReference Include="Serilog.Sinks.File" Version="7.0.0" />
    <PackageReference Include="System.Text.Json" Version="9.0.4" />
    <PackageReference Include="CsvHelper" Version="33.0.1" />
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="../../../Payroll.Shared/Payroll.Shared.csproj" />
    <ProjectReference Include="../../../CrunchTime.Tool/CrunchTime.Tool.csproj" />
  </ItemGroup>

</Project>
