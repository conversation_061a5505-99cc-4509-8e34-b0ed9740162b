
#HOUSEKEEPING
WEEKNUM=`date +%V`

#Variables
REMOTE_FTP="sftp://<EMAIL>/Fourjay/Payroll/"
LOCAL_DIR="../Mailbox/ftp/"
LOG_FILE="lftp_mirror.log"  # Log file for lftp operations


# Step 1: Mirror the remote FTP directory to the local directory
echo "Mirroring remote FTP directory..."
mkdir -p $LOCAL_DIR
LFTP_PASSWORD=$ALOHA_FTP_PASSWORD lftp -c "open $REMOTE_FTP --env-password; mirror --verbose --delete --only-newer . $LOCAL_DIR" > $LOG_FILE

# Step 2: Find the newest file in the local directory
newest_file=$(ls -t "$LOCAL_DIR" | head -n 1)

if [ -z "$newest_file" ]; then
    echo "No files found in the local directory."
    exit 1
fi