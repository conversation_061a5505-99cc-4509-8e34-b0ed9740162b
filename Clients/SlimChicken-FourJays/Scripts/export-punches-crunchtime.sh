#!/bin/bash

#HOUSEKEEPING
WEEKNUM=`date +%V`

LOGFILE=../Logs/Crunch/time-$WEEKNUM.log dotnet ./crunch/CrunchTime.Tool.dll export time all 7 1 > ../Mailbox/crunch-time.json
cat ../Mailbox/crunch-time.json |  dotnet client/FourJays.Tool.dll import punches | LOGFILE=../Logs/Paycom/crunch-punches-$WEEKNUM.log dotnet paycom2/Paycom2.Tool.dll import punches doit

cp ../Mailbox/crunch-time.json ../Archive/crunch-time-$WEEKNUM.json

# only keep X days worth of files
find /opt/openarc/Logs/ -type f -mtime +60 -delete
find /opt/openarc/Mailbox/ -type f -mtime +180 -delete

