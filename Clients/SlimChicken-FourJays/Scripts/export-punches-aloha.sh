#!/bin/bash

#HOUSEKEEPING
WEEKNUM=`date +%V`

#Variables
REMOTE_FTP="sftp://<EMAIL>/Fourjay/Payroll/"
LOCAL_DIR="../Mailbox/ftp/"
LOG_FILE="lftp_mirror.log"  # Log file for lftp operations

# Step 1: Mirror the remote FTP directory to the local directory
echo "Mirroring remote FTP directory..."
mkdir -p $LOCAL_DIR
LFTP_PASSWORD=$ALOHA_FTP_PASSWORD lftp -c "open $REMOTE_FTP --env-password; mirror --verbose --delete --only-newer . $LOCAL_DIR" > $LOG_FILE

# Step 2: Find the newest file in the local directory
newest_file=$(ls -t "$LOCAL_DIR" | head -n 1)

if [ -z "$newest_file" ]; then
    echo "No files found in the local directory."
    exit 1
fi

cat $LOCAL_DIR/$newest_file | dotnet aloha/Aloha.Tool.dll import punches > ../Mailbox/aloha-time.json
cat ../Mailbox/aloha-time.json | LOGFILE=../Logs/Paycom/aloha-punches-$WEEKNUM.log dotnet paycom2/Paycom2.Tool.dll import punches

# Step 4: Remove all files from the remote FTP directory
#echo "Removing all files from the remote FTP directory..."
#lftp -c "open $REMOTE_FTP; rm -r *"

mv $LOCAL_DIR/$newest_file ../Archive/aloha-time-$WEEKNUM.csv
