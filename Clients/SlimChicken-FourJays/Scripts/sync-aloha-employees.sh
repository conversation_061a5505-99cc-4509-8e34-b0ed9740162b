#!/bin/sh

DAYNUM=`date +%u`
MONTHDAY=`date +%d`

#Variables
REMOTE_FTP="sftp://<EMAIL>/Fourjay/Employees/"

# 15M04 is Fourjay
dotnet paycom2/Paycom2.Tool.dll cache export gl_code 15M04 active true | dotnet client/FourJays.Tool.dll import employees 15M04 | dotnet aloha/Aloha.Tool.dll export employees > ../Mailbox/15m04-employees-$DAYNUM.csv
dotnet paycom2/Paycom2.Tool.dll cache export gl_code 15M04 active false termdate 4d | dotnet aloha/Aloha.Tool.dll export employees >> ../Mailbox/15m04-employees-$DAYNUM.csv
LFTP_PASSWORD=$ALOHA_FTP_PASSWORD lftp --env-password $REMOTE_FTP -e "put ../Mailbox/15m04-employees-$DAYNUM.csv -o 15m04-employees-$DAYNUM.csv; exit"

mv ../Mailbox/15m04-employees-$DAYNUM.csv ../Archive/Aloha/15m04-employees-$MONTHDAY.csv
