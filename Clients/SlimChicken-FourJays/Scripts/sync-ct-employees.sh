#!/bin/sh

DOM=`date +%d`

# 15M02 is Simply Slims AL and 15M04 is Fourjay
dotnet paycom2/Paycom2.Tool.dll cache export gl_code 15M02 active false termdate 7d | dotnet client/FourJays.Tool.dll import employees 15M02 > ../Mailbox/15m02-terms.json
cat ../Mailbox/15m02-terms.json | LOGFILE=../Logs/Crunch/terms-slims-al-$DOM.log dotnet crunch/CrunchTime.Tool.dll import terms all doit

# process new hires & existing employees
dotnet paycom2/Paycom2.Tool.dll cache export gl_code 15M02 active true > ../Mailbox/15m02-employees.json
cat ../Mailbox/15m02-employees.json | dotnet client/FourJays.Tool.dll import hires 15M02 | LOGFILE=../Logs/Crunch/hires-slims-al-$DOM.log dotnet crunch/CrunchTime.Tool.dll import hires all doit
cat ../Mailbox/15m02-employees.json | dotnet client/FourJays.Tool.dll import employees 15M02 | LOGFILE=../Logs/Crunch/sync-slims-al-$DOM.log dotnet crunch/CrunchTime.Tool.dll import sync all doit

# 15M03 is Simply Slims AR and 15M04 is Fourjay
dotnet paycom2/Paycom2.Tool.dll cache export gl_code 15M03 active false termdate 7d | dotnet client/FourJays.Tool.dll import employees 15M03 > ../Mailbox/15m03-terms.json
cat ../Mailbox/15m03-terms.json | LOGFILE=../Logs/Crunch/terms-slims-ar-$DOM.log dotnet crunch/CrunchTime.Tool.dll import terms all doit

# process new hires & existing employees
dotnet paycom2/Paycom2.Tool.dll cache export gl_code 15M03 active true > ../Mailbox/15m03-employees.json
cat ../Mailbox/15m03-employees.json | dotnet client/FourJays.Tool.dll import hires 15M03 | LOGFILE=../Logs/Crunch/hires-slims-ar-$DOM.log dotnet crunch/CrunchTime.Tool.dll import hires all doit
cat ../Mailbox/15m03-employees.json | dotnet client/FourJays.Tool.dll import employees 15M03 | LOGFILE=../Logs/Crunch/sync-slims-ar-$DOM.log dotnet crunch/CrunchTime.Tool.dll import sync all doit

