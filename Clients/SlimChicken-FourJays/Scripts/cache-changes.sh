#!/bin/sh

# This script refreshes the local cache by asking Paycom what has changed recently. This will pick up many employee changes, but not all.
# It depends on how HR makes the change and how Paycom handles the change.

DOM=`date +%d`
LOGFILE=../Logs/Paycom/paycom-$DOM.log dotnet paycom2/Paycom2.Tool.dll cache update
LOGFILE=../Logs/Crunch/cruch-cache-$DOM.log dotnet crunch/CrunchTime.Tool.dll cache init
