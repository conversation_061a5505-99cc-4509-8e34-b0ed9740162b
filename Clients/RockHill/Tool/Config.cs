﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Runtime.CompilerServices;
using Payroll.Shared;

namespace RockHill.Tool
{
    public static class Config
    {
        public readonly static string SettingSection = "rockhill";

        private static readonly string ImmedTermSection = "rockhill_immed_term_labels";

        public static readonly string EMPLOYEES_CACHE_COLLECTION = "employees";

        private const uint DefaultNextIdStart = 10020;

        public static uint NextIdStart()
        {
            var val = Setting.Get(SettingSection, "next_id_start");
            if (uint.TryParse(val, out var result))
                return result;
            return DefaultNextIdStart;
        }

        public static readonly List<string> ImmediateTermLabels = new List<string>();

        static Config()
        {
            Setting.Init();

            var termList = Setting.ListSection(ImmedTermSection);
            ImmediateTermLabels = termList.Values.ToList(); // assuming very few term labels (probably 2-3)
        }

        public static ExecutionMode ExecutionMode()
        {
            var mode = Setting.Get(SettingSection, "mode");
            if (string.IsNullOrEmpty(mode)) return Payroll.Shared.ExecutionMode.DryRun;
            return mode == "execute" ? Payroll.Shared.ExecutionMode.Execute : Payroll.Shared.ExecutionMode.DryRun;
        }

        public static bool IsImmediateTermLabel(string label)
        {
            return ImmediateTermLabels.Contains(label);
        }

        public static string OrgUnitMapPath()
        {
            return Setting.Get(SettingSection, "ou_map");
        }

        public static string TemplateDnMapPath()
        {
            return Setting.Get(SettingSection, "template_dn_map");
        }

        public static string MailboxListPath()
        {
            return Setting.Get(SettingSection, "mailbox_list");
        }

        public static string CreateMailboxScriptPath()
        {
            return Setting.Get(SettingSection, "create_mailbox");
        }

        public static string UpdateMailboxListScriptPath()
        {
            return Setting.Get(SettingSection, "update_mailbox_list");
        }

        public static string InitialPassword()
        {
            return Setting.Get(SettingSection, "initial_pwd");
        }
    }
}
