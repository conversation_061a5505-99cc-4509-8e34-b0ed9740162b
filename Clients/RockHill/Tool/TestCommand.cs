﻿using System;
using System.Collections.Generic;
using System.Globalization;
using System.Linq;
using System.Xml.Serialization;
using Newtonsoft.Json;
using Payroll.Shared;
using Serilog;

namespace RockHill.Tool
{
    internal class TestCommand
    {
        public TestCommand()
        {
        }

        public void DeptMap(List<string> args)
        {
            if (args == null || args.Count < 1)
            {
                Console.WriteLine("Usage: RockHill.Tool.exe test deptmap <filename>");
                return;
            }

            var filename = args[0];

            try
            {
                // Create dictionary to store department-OU mappings
                Dictionary<string, string> deptOUMap = new Dictionary<string, string>();

                // Open file and read lines
                using (StreamReader file = new StreamReader(filename))
                {
                    string line;
                    while ((line = file.ReadLine()) != null)
                    {
                        // Split line on tab delimiter
                        string[] parts = line.Split('\t');

                        // Add to dictionary
                        deptOUMap.Add(parts[0], parts[1]);
                    }
                }

                // Print dictionary contents
                foreach (KeyValuePair<string, string> kvp in deptOUMap)
                {
                    Console.WriteLine("Department: {0}, OU: {1}", kvp.Key, kvp.Value);
                }
            }
            catch (Exception e)
            {
                Log.Logger.Fatal(e.Message);
                Log.Logger.Fatal(e.StackTrace);
            }
        }
    }
}