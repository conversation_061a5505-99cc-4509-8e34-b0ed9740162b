using System;

namespace RockHill.Tool
{
    public static class DivisionOrgUnit
    {
        public static string Create<PERSON>ey(string divisionCode, string jobTitle)
        {
            // Null checks
            divisionCode = divisionCode ?? ""; // Use empty string if null
            jobTitle = jobTitle ?? ""; // Use empty string if null
            
            // Return the key
            return $"{divisionCode.ToLower()}/{jobTitle.ToLower()}";
        }
    }
}