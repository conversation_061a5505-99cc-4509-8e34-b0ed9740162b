using LiteDB;
using Payroll.Shared;
using Serilog;
using System.Text.Json;
using System.Text.Json.Serialization;

namespace RockHill.Tool
{
    internal class EmployeeTermConverter : JsonConverter<EmployeeDirectoryEntry>
    {
        public override EmployeeDirectoryEntry Read(ref Utf8JsonReader reader, Type typeToConvert, JsonSerializerOptions options)
        {
            throw new NotImplementedException();
        }

        public override void Write(Utf8JsonWriter writer, EmployeeDirectoryEntry value, JsonSerializerOptions options)
        {
            writer.WriteStartObject();
            writer.WriteString("SamAccountName", value.SamAccountName);
            writer.WriteString("EmployeeId", value.EmployeeId);
            writer.WriteString("PrimaryKey", value.PrimaryKey);
            writer.WriteString("ExtensionAttribute8", value.ExtensionAttribute8);
            writer.WriteString("ExtensionAttribute11", value.ExtensionAttribute11);
            writer.WriteString("ParentOU", value.ParentOU);
            writer.WriteString("TermDate", value.TermDate.ToString());
            writer.WriteString("Description", value.Description);
            writer.WriteString("Department", value.Department);
            writer.WriteString("Name", value.Name);
            writer.WriteEndObject();
        }
    }
}