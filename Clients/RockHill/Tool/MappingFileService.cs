using System.Globalization;
using CsvHelper;
using CsvHelper.Configuration.Attributes;
using Serilog;

namespace RockHill.Tool
{
public class DnMapEntry
{
    public string ParentOU { get; set; }
    public string PolicyDN { get; set; }
}

    public class GroupLookupTableEntry
    {
        [Name("Division Detail Code")]
        public string DivisionCode { get; set; }
        [Name("OU")]
        public string GroupOU { get; set; }
    }

    class PolicyLookupTableEntry
    {
        [Name("extensionattribute3")]
        public string GlCode { get; set; }
        [Name("samaccountname")]
        public string SamAccountName { get; set; }
        [Name("title")]
        public string JobTitle { get; set; }
        [Name("distinguishedname")]
        public string PolicyDN { get; set; }
        [Name("OU")]
        public string ParentOU { get; set; }
    }

    public static class MappingFileService
    {
        public static Dictionary<string, string> LoadParentOuMap()
        {
            var ouMapping = new Dictionary<string, string>();

            try
            {
                var path = Config.OrgUnitMapPath();

                using (FileStream fs = new FileStream(path, FileMode.Open, FileAccess.Read, FileShare.ReadWrite))
                using (var reader = new StreamReader(fs))
                using (var csv = new CsvReader(reader, CultureInfo.InvariantCulture))
                {
                    var records = csv.GetRecords<GroupLookupTableEntry>();
                    foreach (var record in records)
                    {
                        ouMapping[record.DivisionCode] = record.GroupOU;
                    }
                }
            }
            catch (Exception e)
            {
                Log.Logger.Fatal(e.Message);
                Log.Logger.Fatal(e.StackTrace);
            }

            return ouMapping;
        }

        public static Dictionary<string, DnMapEntry> LoadTemplateDnMap()
        {
            var departmentDnMapping = new Dictionary<string, DnMapEntry>();

            try
            {
                var path = Config.TemplateDnMapPath();

                using (FileStream fs = new FileStream(path, FileMode.Open, FileAccess.Read, FileShare.ReadWrite))
                using (var reader = new StreamReader(fs))
                using (var csv = new CsvReader(reader, CultureInfo.InvariantCulture))
                {
                    var records = csv.GetRecords<PolicyLookupTableEntry>();
                    foreach (var record in records)
                    {
                        var skey = DivisionOrgUnit.CreateKey(record.GlCode, record.JobTitle);
                        departmentDnMapping[skey] = new DnMapEntry() { 
                            ParentOU = record.ParentOU,
                            PolicyDN = record.PolicyDN 
                        };
                    }
                }
            }
            catch (Exception e)
            {
                Log.Logger.Fatal(e.Message);
                Log.Logger.Fatal(e.StackTrace);
            }

            return departmentDnMapping;
        }
    }
}