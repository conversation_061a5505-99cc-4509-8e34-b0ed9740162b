using Payroll.Shared;
using Serilog;
using System.Text.Json;

namespace RockHill.Tool
{
    internal class ExportCommand
    {
        public void Hires(List<string> args)
        {
            try
            {
                if (!ConsoleService.TryGetEmployeesFromInput(out var employees))
                {
                    Log.Logger.Error("Failed to parse employees list");
                    return;
                }

                if (employees == null || employees.Count == 0)
                {
                    Log.Logger.Error("Failed to load employees list");
                    return;
                }

                var dryRun = !(args != null && args.Count > 0 && args[0] == "doit");
                ProcessEmployees(employees, dryRun);
            }
            catch (Exception e)
            {
                Log.Logger.Fatal(e.Message);
                Log.Logger.Fatal(e.StackTrace);
            }
        }

        private void ProcessEmployees(IEnumerable<Payroll.Shared.Employee> employees, bool dryRun)
        {
            var hires = new List<Employee>();
            foreach (var employee in employees)
            {
                // new employees don't have a clockseq number
                if (!string.IsNullOrEmpty(employee.ClockSeq)) continue;

                hires.Add(employee);
            }

            ConsoleService.PrintFormattedJson(hires);
        }
    }
}