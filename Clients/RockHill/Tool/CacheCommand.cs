﻿using System;
using System.Collections.Generic;
using Serilog;
using System.Text.Json;
using Payroll.Shared;
using System.Linq;
using Location = Paycom2.Shared.Location;
using LiteDB;

namespace RockHill.Tool
{
    class CacheCommand
    {
        public void Prune(List<string> args)
        {
            if (args == null || args.Count < 1)
            {
                Console.WriteLine("Usage: RockHill.Tool.exe cache prune [doit] - clear out terminated employees with IDs less than <NEXTID>");
                return;
            }

            // Check if we should actually delete the records
            var dryRun = !(args != null && args.Count > 0 && args[0] == "doit");
            int deletedCount = 0;
            uint nextIdStart = Config.NextIdStart();

            try
            {
                using (var cache = CacheService.OpenCache(SystemConfig.CacheName()))
                {
                    var col = cache.GetCollection<Employee>(Config.EMPLOYEES_CACHE_COLLECTION);

                    // Find all terminated employees
                    var terminatedEmployees = col.Find(x => x.Active == false && x.TermDate != null).ToList();
                    Log.Logger.Information("Found {0} terminated employees in cache", terminatedEmployees.Count);

                    // Filter employees with term dates older than maxDaysOld
                    foreach (var employee in terminatedEmployees)
                    {
                        if (!uint.TryParse(employee.ClockSeq, out uint clockSeq))
                        {
                            Log.Logger.Error("Invalid ClockSeq: {0}", employee.ClockSeq);
                            continue;
                        }

                        if (clockSeq < nextIdStart)
                        {
                            if (!dryRun)
                            {
                                CacheService.RemoveRecord<Employee>(Config.EMPLOYEES_CACHE_COLLECTION, employee.Id);
                            }
                            else
                            {
                                Log.Logger.Information("Would have deleted {0}/{1} {2} {3}", employee.Id, employee.ClockSeq, employee.FirstName, employee.LastName);
                            }
                            deletedCount++;
                        }
                    }

                    string resultMessage = dryRun
                        ? $"Found {deletedCount} employees that would be deleted"
                        : $"Deleted {deletedCount} employees";

                    Log.Logger.Information(resultMessage);
                }
            }
            catch (Exception e)
            {
                Log.Logger.Fatal(e.Message);
                Log.Logger.Fatal(e.StackTrace);
            }
        }
    }
}
