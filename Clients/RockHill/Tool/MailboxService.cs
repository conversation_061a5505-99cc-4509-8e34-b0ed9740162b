using System.Runtime.InteropServices;
using System.Text.RegularExpressions;
using Serilog;

namespace RockHill.Tool
{
    public static class MailboxService
    {
        static Dictionary<string, string> MailboxList;

        private static bool IsValidEmailAddress(string emailAddress)
        {
            // Regex pattern for validating email addresses.
            string regexPattern = @"^[a-zA-Z0-9_.+-]+@[a-zA-Z0-9-]+\.[a-zA-Z]{2,}$";

            // Create a new Regex object.
            Regex regex = new Regex(regexPattern);

            // Match the email address against the regex pattern.
            Match match = regex.Match(emailAddress);

            // Return true if the email address matches the regex pattern, false otherwise.
            return match.Success;
        }

        public static string ExistingMailboxEmpId(string emailAddress)
        {
            var retId = MailboxList.TryGetValue(emailAddress, out string empId) ? empId : null;
            Log.Logger.Debug("ExistingMailboxEmpId check: {0} = {1}", emailAddress, retId);
            return retId;
        }

        public static bool IsUserMailbox(string emailAddress, string targetId)
        {
            // must match for emailAddress and targetId
            var mailboxFnd = MailboxList.TryGetValue(emailAddress, out string empId);
            if (!mailboxFnd) return false;
            return empId == targetId;
        }

        static MailboxService()
        {
            MailboxList = new Dictionary<string, string>();
            var path = Config.MailboxListPath();

            using (FileStream fs = new FileStream(path, FileMode.Open, FileAccess.Read, FileShare.ReadWrite))
            using (var reader = new StreamReader(fs))
            {
                string line;
                while ((line = reader.ReadLine()) != null)
                {
                    var cleanLine = line.Trim().Replace("\"", "").ToLower();
                    var words = cleanLine.Split("\t");
                    if (words == null || words.Length < 2) continue;

                    // Validate the email address.
                    if (!IsValidEmailAddress(words[0]))
                    {
                        Log.Logger.Warning("Skipping invalid email address {0}", words[0]);
                        continue;
                    }

                    // Add the email address to the HashSet.
                    MailboxList.Add(words[0], words[1]);
                }
            }

            var first = MailboxList.FirstOrDefault();
            Log.Logger.Information("Added {0} existing email addresses to hashset, including {1} {2}", MailboxList.Count, first.Key, first.Value);
        }
    }
}