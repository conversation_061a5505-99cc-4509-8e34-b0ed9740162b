﻿using System;
using System.Collections.Generic;
using CommandLine;
using Payroll.Shared;
using System.Linq;

namespace RockHill.Tool
{
    class Program : ProgramBase<SettingCommand>
    {
        private static readonly string AppVersion = "1.0";

        static int DoShowUsage()
        {
            Console.WriteLine("Usage: RockHill.Tool.exe <command> <command-args>");
            Console.WriteLine("  where <command> is one of...");
            Console.WriteLine("   - exchange [setup|refresh] - setup email boxes, refresh list of mailboxes, etc");
            Console.WriteLine("   - exec [refresh] - setup email boxes");
            Console.WriteLine("   - import [changes, employees, hires] = custom RockHill change import");
            Console.WriteLine("   - info - show version and tool information");

            return 1;
        }

        public override int ShowUsage()
        {
            return DoShowUsage();
        }

        public int Help(List<string> args)
        {
            return DoShowUsage();
        }

        private int ExecCommand<T>(List<string> args) where T : new()
        {
            var command = new CommandArguments(args);

            return Command<T>.Invoke(command);
        }

        public int Import(List<string> args)
        {
            return ExecCommand<ImportCommand>(args);
        }

        public int Exchange(List<string> args)
        {
            return ExecCommand<ExchangeCommand>(args);
        }

        public int Exec(List<string> args)
        {
            return ExecCommand<ExecCommand>(args);
        }

        public int Export(List<string> args)
        {
            return ExecCommand<ExportCommand>(args);
        }

        public int Info(List<string> args)
        {
            try
            {
                Console.WriteLine($"RockHill.Tool.exe version {AppVersion}");
                Console.WriteLine($"  Branch: {BuildInfo.Branch}, Built: {BuildInfo.Built}, Host: {BuildInfo.Host}, Hash: {BuildInfo.Commit}");
                Console.WriteLine();

                var dnMapping = MappingFileService.LoadTemplateDnMap();
                if (dnMapping != null) Console.WriteLine("DN Mappings: {0}", dnMapping.Count);

                var ouMapping = MappingFileService.LoadParentOuMap();
                if (ouMapping != null) Console.WriteLine("OU Mappings: {0}", ouMapping.Count);

                Console.WriteLine();

                Console.WriteLine("Immediate Term Labels:");
                foreach (var key in Config.ImmediateTermLabels)
                {
                    Console.WriteLine(" -> '{0}'", key);
                }

                Console.WriteLine();
                Console.WriteLine("Next ID Start: {0}", Config.NextIdStart());
                Console.WriteLine("Mailbox List: {0}", Config.MailboxListPath());
                Console.WriteLine("Create Mailbox Script: {0}", Config.CreateMailboxScriptPath());
                Console.WriteLine("Update Mailbox List Script: {0}", Config.UpdateMailboxListScriptPath());
            }
            catch (Exception e)
            {
                Console.WriteLine(e.Message);
                Console.WriteLine(e.StackTrace);
                return ExitCode.Exception;
            }

            return 0;
        }

        public int Test(List<string> args)
        {
            return ExecCommand<TestCommand>(args);
        }

        static int Main(string[] args)
        {
            if (args.Length == 0)
            {
                DoShowUsage();
                return ExitCode.BadUsage;
            }

            // setup logging services...
            var command = string.Join(" ", args);
            Payroll.Shared.Logger.Setup($"RockHill.Tool.Exe - Command: '{command}', Version: {AppVersion}");

            int rc = 0;
            try
            {
                Parser.Default.ParseArguments<ProgramArguments>(args)
                    .MapResult((ProgramArguments opts) => rc = ProgramDriver<Program>.Run(opts), errs => 1);
            }
            catch (Exception e)
            {
                Console.WriteLine(e.Message);
                Console.WriteLine(e.StackTrace);
                return ExitCode.Exception;
            }

            return rc;
        }
    }
}
