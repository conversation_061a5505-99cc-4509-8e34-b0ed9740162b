<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <OutputType>Exe</OutputType>
    <TargetFrameworks>net8.0</TargetFrameworks>
    <Configurations>Release;CoreOnly</Configurations>
    <Platforms>AnyCPU</Platforms>
    <LangVersion>10.0</LangVersion>
    <ImplicitUsings>enable</ImplicitUsings>
    <SatelliteResourceLanguages>en-US</SatelliteResourceLanguages>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="CommandLineParser" Version="2.9.1" />
    <PackageReference Include="CsvHelper" Version="33.0.1" />
    <PackageReference Include="Microsoft.PowerShell.SDK" Version="7.5.1" />
    <PackageReference Include="System.Management.Automation" Version="7.4.*" />
    <PackageReference Include="LiteDB" Version="5.0.21" />
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\..\..\Paycom2.Shared\Paycom2.Shared.csproj" />
    <ProjectReference Include="..\..\..\Payroll.Shared\Payroll.Shared.csproj" />
  </ItemGroup>

</Project>
