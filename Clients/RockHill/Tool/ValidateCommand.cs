﻿using LiteDB;
using Payroll.Shared;
using Serilog;
using System.Text.Json;

namespace RockHill.Tool
{
    internal class ValidateCommand
    {
        public int Employees(List<string> args)
        {
            (int exitCode, int recordCount) = ProcessImportCommand(args, (IEnumerable<EmployeeDirectoryEntry> employees) =>
            {
                var results = new List<Result>();
                foreach (var dEntry in employees)
                {
                    if (string.IsNullOrEmpty(dEntry.SurName) ||
                        string.IsNullOrEmpty(dEntry.GivenName))
                    {
                        Log.Logger.Debug("First or last name missing, skipping employee record (id={0})", dEntry.EmployeeId);
                        continue;
                    }

                    if (string.IsNullOrEmpty(dEntry.PolicyDN))
                    {
                        var result = new Result() { ResultType = ResultType.Error, Success = false };
                        result.Args.Add("id", dEntry.EmployeeId);
                        result.Args.Add("pkey", dEntry.PrimaryKey);
                        result.Args.Add("date", dEntry.HireDate?.ToShortDateString());
                        result.Args.Add("department", dEntry.Department);
                        result.Args.Add("description", dEntry.Description);
                        result.Args.Add("name", dEntry.Name);

                        if (!string.IsNullOrEmpty(dEntry.Title))
                            result.Args.Add("title", dEntry.Title);
                        if (!string.IsNullOrEmpty(dEntry.ExtensionAttribute3))
                            result.Args.Add("ext3", dEntry.ExtensionAttribute3);
                        results.Add(result);
                    }
                }

                ConsoleService.PrintFormattedJson(results);
                return results.Count;
            });


            return exitCode;
        }

        private (int exitCode, int recordCount) ProcessImportCommand(List<string> args, Func<IEnumerable<EmployeeDirectoryEntry>, int> func)
        {
            try
            {
                if (!ConsoleService.TryGetDirectoryEntriesFromInput(out List<EmployeeDirectoryEntry> employees))
                {
                    Log.Logger.Error("Failed to parse employee list");
                    return (ExitCode.BadInput, 0);
                }

                if (employees == null)
                {
                    Log.Logger.Error("Skipping null employees list");
                    return (ExitCode.BadInput, 0);
                }

                return (ExitCode.Success, func(employees));
            }
            catch (Exception e)
            {
                Log.Logger.Fatal(e.Message);
                Log.Logger.Fatal(e.StackTrace);
                return (ExitCode.Exception, 0);
            }
        }
   }
}