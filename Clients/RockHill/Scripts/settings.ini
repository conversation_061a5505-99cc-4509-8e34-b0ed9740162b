[system]
dbtype = ldb
dbname = rockhill
smtp_host = ***********
smtp_user = <EMAIL>
notify_addr = <EMAIL>
errors_notify_addr = <EMAIL>
errors_notify_addr2 = <EMAIL>
smtp_pwd = R0ck!P@y!123
notify_addr_dev = <EMAIL>
email_subject_errors = Verification Needed for Staff Title and GL Code for IT Account Creation/Update
email_body_errors = c:\OpenArc\Prod051528\Scripts\errors.mail.template

[rockhill]
ou_map = c:\Installers\OU_LookupTable.csv
template_dn_map = c:\Installers\TemplateLookup.csv
initial_pwd = R0ck!CRH!123
mailbox_list = C:\Installers\current-mailboxes.txt
update_mailbox_list = c:\OpenArc\Prod051528\Scripts\update-mailbox-list.ps1
create_mailbox = c:\OpenArc\Prod051528\Scripts\create-mailbox.ps1
next_id_start = 10300

[rockhill_immed_term_labels]
imt1=Terminated for Cause
imt2=Quit in Lieu of Termination (HR Use)

[ad]
endpoint=LDAP://***********
username=<EMAIL>
password=R0ck!P@y!123
hire_mode=execute
sync_mode=execute
term_mode=execute
pkey=extensionAttribute11
sync_limit = 2500
hire_limit = 25
term_limit = 25

[ad_skip_groups]
wh=Windows Hello for Business Users
mfa=MFAExempt_sec
du=Domain Users

[ad_notify_on_change]
title = true

[ad_sensitive_properties]
company = true
displayName = true
title = true
manager = true
department = true
division = true

[mail]
endpoint=crh-vmw-exhyb.cityofrockhillsc.gov

[paycom]
sid=45e1506d08e201d17053f4f4bc59ee4a3725b51110ee973c0592fa0c789fee61
token=6c1822447824a72b2e655078781ddad942435deacd0427ea9f6aa4a5c3b041aa
mode = execute
timezone = EST
sensitive = 1
pending = 1

[paycom_fields]
location = loccode
organization = cat3
fname = nickname
budget = cat1
division_code = cat1
division_name = cat1desc
org_code = new_hire

[paycom_custom_fields]
city_phone = CustomText29
city_mobile = CustomText30
sign_phone = CustomText31
agent_phone = CustomText32
sign_mobile = CustomText33
post_nominals = CustomText12
pref_lname = CustomText16
needs_email = CustomSelect06
its_ready = CustomSelect08
