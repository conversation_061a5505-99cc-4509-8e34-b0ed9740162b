#!/bin/sh

# This script asks the cache for who has been terminated recently, disables those records in AD,
# and then emails the results.

DOM=`date +%d`
HOUR=`date +%H`
TODAY=`date +day%d-hr%H`

dotnet paycom2/Paycom2.Tool.dll cache dump term 7 | LOGFILE=../Logs/Client/terms-$DOM-$HOUR.log dotnet client/RockHill.Tool.dll import terms  > ../terms.json
cat ../terms.json | LOGFILE=../Logs/AD/terms-$DOM-$HOUR.log dotnet.exe ad/AD.Tool.dll import terms doit > ../terms.log
cat ../terms.log | dotnet.exe payroll/Payroll.Tool.dll mail results
