# This script removes test mailboxes. It is not used in production.

Function Connect-Exchange {

    param(
        [Parameter( Mandatory=$false)]
        [string]$URL="crh-vmw-exhyb.cityofrockhillsc.gov"
    )

    $username = "<EMAIL>"
    $apiPassword = Get-Content "C:\OpenArc\ApiPasswordFile.txt" | ConvertTo-SecureString
    $credentials = New-Object System.Management.Automation.PSCredential ($username, $apiPassword)

    $ExOPSession = New-PSSession -ConfigurationName Microsoft.Exchange -ConnectionUri http://$URL/PowerShell/ -Authentication Kerberos -Credential $credentials

    Import-PSSession $ExOPSession
}

Connect-Exchange
Remove-RemoteMailbox -Identity <EMAIL>
Remove-RemoteMailbox -Identity <EMAIL>
