# Script to update the Start In folder for all tasks in the OpenArc folder
# Requires one argument: the new Start In folder path
# Prompts for credentials to handle tasks with specific user accounts

param (
    [Parameter(Mandatory=$true)]
    [ValidateScript({Test-Path $_ -PathType Container})]
    [string]$NewStartInFolder
)

try {
    # Normalize the path to remove trailing backslash
    $NewStartInFolder = $NewStartInFolder.TrimEnd('\')

    # Prompt for credentials
    $credential = Get-Credential -Message "Enter credentials for an account with permissions to modify tasks in OpenArc folder"

    # Get all tasks in the OpenArc folder
    $tasks = Get-ScheduledTask -TaskPath "\OpenArc\" -ErrorAction Stop

    # Check if any tasks exist
    if ($tasks.Count -eq 0) {
        Write-Host "No tasks found in the OpenArc folder."
        exit
    }

    # Loop through each task and update its Start In folder
    foreach ($task in $tasks) {
        $taskName = $task.TaskName
        $action = $task.Actions[0]

        # Check if the action is an executable action
        if ($action -is [Microsoft.Management.Infrastructure.CimInstance] -and $action.CimClass.CimClassName -eq "MSFT_TaskExecAction") {
            try {
                # Create a new action with the updated Start In folder
                $newAction = New-ScheduledTaskAction -Execute $action.Execute -Argument $action.Arguments -WorkingDirectory $NewStartInFolder

                # Update the task with the new action and provided credentials
                Set-ScheduledTask -TaskName $taskName -TaskPath "\OpenArc\" -Action $newAction -User $credential.UserName -Password $credential.GetNetworkCredential().Password -ErrorAction Stop

                Write-Host "Updated Start In folder for task: $taskName to $NewStartInFolder"
            } catch {
                Write-Error "Failed to update task $taskName : $_"
            }
        } else {
            Write-Warning "Task $taskName does not have an executable action or is not compatible."
        }
    }
} catch {
    Write-Error "Failed to retrieve tasks from OpenArc folder: $_"
}