#!/bin/sh

DOM=`date +%d`
HOUR=`date +%H`
TODAY=`date +day%d-hr%H`

# This script takes as input an employee ID. It asks Paycom for information on that employee, 
# applies Rock Hill specific customizations, and then creates an exchange emailbox and AD record with the information from Paycom.
# A "new employee" record that fails one of the validation criteria for "cache dump new" in the hires-all command would still run in this command.

#!/bin/bash

if [ $# -lt 1 ]; then
    echo "Usage: $0 <PaycomID>"
    exit 1
fi

# setup exchange accounts
if dotnet payroll/Payroll.Tool.dll cache view $1 | LOGFILE=../Logs/Client/hires-$DOM-$HOUR.log dotnet client/RockHill.Tool.dll exchange setup > ../mail.txt; then
    cat ../mail.txt >> ../Logs/Misc/mail-$DOM.txt
    powershell.exe -File "Scripts/create-mailboxes.ps1"
    sleep 15s
else
    echo "User is not ready for creation or already has a mailbox"
    exit
fi

# now set rockhill id & update paycom
dotnet payroll/Payroll.Tool.dll cache view $1 | LOGFILE=../Logs/Client/hires-$DOM-$HOUR.log dotnet client/RockHill.Tool.dll import hires  > ../hires.json
cat ../hires.json >> ../Logs/Misc/hires-$DOM.json

# create AD accounts
cat ../hires.json | LOGFILE=../Logs/AD/hires-$DOM-$HOUR.log dotnet ad/AD.Tool.dll import hires doit > ../hires.log

# update clockseq and badge numbers in Paycom
cat ../hires.json | dotnet ad/AD.Tool.dll export employees > ../created-employees.json
cat ../created-employees.json >> ../Logs/Misc/created-employees-$DOM.json

cat ../created-employees.json | LOGFILE=../Logs/Paycom/hires-$DOM-$HOUR.log dotnet paycom2/Paycom2.Tool.dll import clockseq doit
cat ../created-employees.json | LOGFILE=../Logs/Paycom/hires-$DOM-$HOUR.log dotnet paycom2/Paycom2.Tool.dll import badge doit
cat ../created-employees.json | LOGFILE=../Logs/Paycom/hires-$DOM-$HOUR.log dotnet paycom2/Paycom2.Tool.dll import email doit

cat ../hires.log | dotnet.exe payroll/Payroll.Tool.dll mail results

