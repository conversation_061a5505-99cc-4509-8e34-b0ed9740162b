# This script updates our local cache of exchange mailboxes which includes each employee's email address and empID

Function Connect-Exchange {

    param(
        [Parameter( Mandatory = $false)]
        [string]$URL = "crh-vmw-exhyb.cityofrockhillsc.gov"
    )

    $username = "<EMAIL>"
    $apiPassword = Get-Content "C:\OpenArc\ApiPasswordFile.txt" | ConvertTo-SecureString
    $credentials = New-Object System.Management.Automation.PSCredential ($username, $apiPassword)

    $ExOPSession = New-PSSession -ConfigurationName Microsoft.Exchange -ConnectionUri http://$URL/PowerShell/ -Authentication Kerberos -Credential $credentials

    Import-PSSession $ExOPSession
}

Connect-Exchange
Get-RemoteMailbox -ResultSize unlimited | Select-Object -Property PrimarySmtpAddress, CustomAttribute11 | Export-Csv -delimiter "`t" -Path C:\Installers\current-mailboxes.txt
