#!/bin/sh

DOM=`date +%d`
HOUR=`date +%H`
TODAY=`date +day%d-hr%H`

# This script reads all active employees with a rehire date in the future or with the Paycom "new hire" flag set from the local cache,
# applies Rock Hill customizations to the records, then creates exchange email boxes, adds the record to AD, and sends the clockseq, badge, and email
# back to Paycom. Note: the "doit" argument on commands below is how you exit "dry-run" mode and actually change AD, paycom, etc.

# first, setup exchange accounts
dotnet paycom2/Paycom2.Tool.dll cache dump new | dotnet ad/AD.Tool.dll filter exists | LOGFILE=../Logs/Client/hires-$DOM-$HOUR.log dotnet client/RockHill.Tool.dll exchange setup > ../mail.txt
cp -f ../mail.txt ../Logs/Misc/mail-$DOM-$HOUR.txt

if [ -s "../mail.txt" ]; then
    powershell.exe -File "Scripts/create-mailboxes.ps1"
    sleep 15m
fi

# now set rockhill id & update paycom
dotnet paycom2/Paycom2.Tool.dll cache dump new | LOGFILE=../Logs/Client/hires-$DOM-$HOUR.log dotnet client/RockHill.Tool.dll import hires  > ../hires.json
cp -f ../hires.json ../Logs/Misc/hires-$DOM-$HOUR.json

# now, create AD accounts
cat ../hires.json | LOGFILE=../Logs/AD/hires-$DOM-$HOUR.log dotnet ad/AD.Tool.dll import hires doit > ../hires.log
cp -f ../hires.log ../Logs/Misc/hires-$DOM-$HOUR.log

# finally, update clockseq and badge numbers in Paycom
cat ../hires.json | dotnet ad/AD.Tool.dll export employees > ../created-employees.json
cp -f ../created-employees.json ../Logs/Misc/created-employees-$DOM-$HOUR.json

cat ../created-employees.json | LOGFILE=../Logs/Paycom/hires-$DOM-$HOUR.log dotnet paycom2/Paycom2.Tool.dll import clockseq doit
cat ../created-employees.json | LOGFILE=../Logs/Paycom/hires-$DOM-$HOUR.log dotnet paycom2/Paycom2.Tool.dll import badge doit
cat ../created-employees.json | LOGFILE=../Logs/Paycom/hires-$DOM-$HOUR.log dotnet paycom2/Paycom2.Tool.dll import email doit

cat ../hires.log | dotnet.exe payroll/Payroll.Tool.dll mail results

