#!/bin/sh

# This script logs the latest stats on API function calls to a file and then clears those stats, typically it is best run at the end of the day.

DOM=`date +%d`
dotnet payroll/Payroll.Tool.dll cache stats > ../Logs/Paycom/stats-$DOM.log 
dotnet payroll/Payroll.Tool.dll cache clear stats

# email any validation issues to HR
cat ../existing.json | LOGLEVEL=debug dotnet client/RockHill.Tool.dll validate employees | dotnet payroll/Payroll.Tool.dll mail results
