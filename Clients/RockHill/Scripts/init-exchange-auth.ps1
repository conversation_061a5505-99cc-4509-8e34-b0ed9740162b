# This script allows us to securely store passwords on the server, but is not used when passwords are hard coded into the scripts.

$password1 = Read-Host -AsSecureString "Enter API Password"
Set-Content "C:\OpenArc\ApiPasswordFile.txt" -Value ($password1 | ConvertFrom-SecureString)

$password2 = ConvertTo-SecureString "R0ck!CRH!123" -AsPlainText -Force
Set-Content "C:\OpenArc\UserPasswordFile.txt" -Value ($password2 | ConvertFrom-SecureString)
