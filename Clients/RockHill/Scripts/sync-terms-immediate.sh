#!/bin/sh

# This script updates the cache with any Paycom changes from the last 15 minutes,
# Asks the cache for who has been terminated recently, filters out all "non-immediate" terminations,
# Terminates the immediate ones, and emails the results.

WEEKDAYPLUSTIME=`date +%u-%H%M`
DOM=`date +%d`

LOGFILE=../Logs/Paycom/paycom-$DOM.log dotnet paycom2/Paycom2.Tool.dll cache update 15m 0
dotnet paycom2/Paycom2.Tool.dll cache dump term 1 | LOGFILE=../Logs/Client/immed-terms-$DOM.log dotnet client/RockHill.Tool.dll import terms immed_only  > ../immed-terms.json
cat ../immed-terms.json | LOGFILE=../Logs/AD/immed-terms-$DOM.log dotnet.exe ad/AD.Tool.dll import terms doit > ../immed-terms.log
cat ../immed-terms.log | dotnet.exe payroll/Payroll.Tool.dll mail results
