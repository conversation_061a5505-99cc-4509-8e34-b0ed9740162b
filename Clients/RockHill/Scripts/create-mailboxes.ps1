# This script takes a table as an input and creates exchange mailboxes for each row of the table.
# Each row of the table has multiple columns with information like the employees name, eecode, email address, etc..

$userPassword = ConvertTo-SecureString "R0ck!CRH!123" -AsPlainText -Force
$day = (Get-Date).ToString("dd")
$logFile = "..\Logs\Misc\RemoteMailboxLog-$day.txt"

Function My-RemoteMailbox() {
}

Function My-Start-Process() {
}

Function Connect-Exchange {

    try
    {
        $username = "<EMAIL>"
        $apiPassword = ConvertTo-SecureString "R0ck!P@y!123" -AsPlainText -Force
        $credentials = New-Object System.Management.Automation.PSCredential ($username, $apiPassword)

        $ExOPSession = New-PSSession -ConfigurationName Microsoft.Exchange -ConnectionUri http://crh-vmw-exhyb.cityofrockhillsc.gov/PowerShell/ -Authentication Kerberos -Credential $credentials
        Import-PSSession $ExOPSession -AllowClobber
    }
    catch {
        Write-Error "Failed to connect to Exchange Server: $_"
        return $false
    }
}

Function CreateMailbox($line) {
    # Split the line into the 5 names
    $names = $line.Split("`t")

    # Skip the line if it does not have 5 tab delimited strings
    if ($names.Count -ne 5) {
        Write-Output $names.Count
        return
    }

    $fname = $names[0]
    $lname = $names[1]
    $eecode = $names[2]
    $uname = $names[3]
    $parent = $names[4]
    Write-Output "First: $fname, Last: $lname, Username: $uname"

    try {
        # Check if the user already exists in Active Directory
        $existingUser = Get-ADUser -Filter { SamAccountName -eq $uname } -ErrorAction SilentlyContinue

        if ($existingUser) {
            # User exists, use Enable-RemoteMailbox
            Enable-RemoteMailbox -Identity $uname -PrimarySmtpAddress "$<EMAIL>" -RemoteRoutingAddress "$<EMAIL>" *>&1 | Out-File $logFile -Append
        }
        else {
            # User doesn't exist, create new remote mailbox
            New-RemoteMailbox -FirstName $fname -LastName $lname -Password $userPassword -ResetPasswordOnNextLogon:$false -name "$lname, $fname" -UserPrincipalName "$<EMAIL>" -PrimarySmtpAddress "$<EMAIL>" -RemoteRoutingAddress "$<EMAIL>" -OnPremisesOrganizationalUnit "$parent" -DisplayName "$lname, $fname" *>&1 | Out-File $logFile -Append  
        }
    }  
    catch {  
        $errorMessage = "Failed to create or enable remote mailbox for ${uname}: $_"  
        Write-Error $errorMessage  
        # Log the error to a separate error log file  
        Add-Content -Path $logFile -Value $errorMessage  
    }

    # need this for users who already have a directory entry
    # Enable-RemoteMailbox -Identity $<EMAIL> -PrimarySmtpAddress $<EMAIL> -RemoteRoutingAddress $<EMAIL> -DisplayName "$lname, $fname"

    $arguments = @("paycom2/Paycom2.Tool.dll", "employee", "setmail", $eecode, "$<EMAIL>", "doit")
    Start-Process -FilePath "dotnet.exe" -ArgumentList $arguments -RedirectStandardError ../Logs/Exchange/$eecode.txt
}

Write-Output "Connecting to exchange server..."

if (-not (Connect-Exchange)) {
    $errorMessage = "Unable to connect to Exchange. Exiting script."
    Write-Error $errorMessage
    Add-Content -Path $logFile -Value $errorMessage
    exit
}

Write-Output "Processing each row in the mail.txt file..."

try {
    foreach ($line in [System.IO.File]::ReadLines("C:\openarc\mail.txt")) {
        # Skip the line if it is empty
        if ($line -eq "") {
            continue
        }

        # Split the line into the 5 cells
        $cleanedLine = $line.Trim().TrimEnd([char]10);
        CreateMailbox($cleanedLine)
        Start-Sleep -Seconds 1
    }
}
catch {
    $errorMessage = "Error reading mail.txt: $_"
    Write-Error $errorMessage
    Add-Content -Path $logFile -Value $errorMessage
}

Write-Output "Done."
