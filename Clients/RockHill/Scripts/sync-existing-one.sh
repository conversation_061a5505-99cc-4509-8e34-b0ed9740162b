#!/bin/sh

# This script takes as input an employee ID. It asks Paycom for information on that employee, 
# applies Rock Hill specific customizations, and then updates active directory with the information from Paycom.
# Note: This script is one of the few that directly talks to Paycom and skips the cache.

DOM=`date +%d`

if [ $# -lt 1 ]; then
    echo "Usage: $0 <PaycomID>"
    exit 1
fi

dotnet paycom2/Paycom2.Tool.dll employee view $1 | LOGFILE=../Logs/Client/existing-$DOM.log dotnet client/RockHill.Tool.dll import employees doit  > ../existing.json
cat ../existing.json  | LOGFILE=../Logs/AD/existing-$DOM.log dotnet.exe ad/AD.Tool.dll import employees doit > ../changelog.json

cat ../changelog.json | LOGFILE=../Logs/Client/changes-$DOM.log dotnet client/RockHill.Tool.dll import changes | LOGFILE=../Logs/AD/changes-$DOM.log dotnet.exe ad/AD.Tool.dll import changes doit > ../sync.log
cat ../sync.log | dotnet.exe payroll/Payroll.Tool.dll mail results