Function GetPrintableString($str) {
    $Ptr = [System.Runtime.InteropServices.Marshal]::SecureStringToBSTR($str)
    $result = [System.Runtime.InteropServices.Marshal]::PtrToStringAuto($Ptr)
    [System.Runtime.InteropServices.Marshal]::ZeroFreeBSTR($Ptr)

    return $result
}

$userPassword = Get-Content "C:\OpenArc\UserPasswordFile.txt" | ConvertTo-SecureString
$uPwdPrint = GetPrintableString($userPassword)
Write-Output "UserPwd From File: $uPwdPrint"

$apiPassword = Get-Content "C:\OpenArc\ApiPasswordFile.txt" | ConvertTo-SecureString
$apiPwdClear = GetPrintableString($apiPassword)
Write-Output "ApiPwd From File: $apiPwdClear"

