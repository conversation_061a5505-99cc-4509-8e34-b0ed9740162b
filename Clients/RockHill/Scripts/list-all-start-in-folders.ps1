# Script to output the Start In (working directory) of each task in the OpenArc folder

try {
    # Get all tasks in the OpenArc folder
    $tasks = Get-ScheduledTask -TaskPath "\OpenArc\" -ErrorAction Stop

    # Check if any tasks exist
    if ($tasks.Count -eq 0) {
        Write-Host "No tasks found in the OpenArc folder."
        exit
    }

    # Loop through each task and output its Start In folder
    foreach ($task in $tasks) {
        $taskName = $task.TaskName
        $action = $task.Actions[0]

        # Check if the action is an executable action
        if ($action -is [Microsoft.Management.Infrastructure.CimInstance] -and $action.CimClass.CimClassName -eq "MSFT_TaskExecAction") {
            $startInFolder = $action.WorkingDirectory
            if ($startInFolder) {
                Write-Host "Task: $taskName, Start In Folder: $startInFolder"
            } else {
                Write-Host "Task: $taskName, Start In Folder: Not specified"
            }
        } else {
            Write-Host "Task: $taskName, No executable action found or action is not compatible."
        }
    }
} catch {
    Write-Error "Failed to retrieve tasks from OpenArc folder: $_"
}