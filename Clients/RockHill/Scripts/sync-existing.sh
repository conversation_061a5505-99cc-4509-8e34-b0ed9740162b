#!/bin/sh

# This script reads all current active employees with a clock sequence number from the local cache,
# applies Rock Hill customizations to the records, and updates the employee records in AD.
# Note: the "doit" argument on the import employees command instructs the command to actually change AD. Without "doit" it would only show you what it would do.

DOM=`date +%d`
dotnet paycom2/Paycom2.Tool.dll cache dump cseq | LOGFILE=../Logs/Client/existing-$DOM.log dotnet client/RockHill.Tool.dll import employees  > ../existing.json
cat ../existing.json  | LOGFILE=../Logs/AD/existing-$DOM.log dotnet.exe ad/AD.Tool.dll import employees doit > ../changelog.json

# ad.tool import changes, updates group memberships
cat ../changelog.json | LOGFILE=../Logs/Client/changes-$DOM.log dotnet client/RockHill.Tool.dll import changes | LOGFILE=../Logs/AD/changes-$DOM.log dotnet.exe ad/AD.Tool.dll import changes doit > ../sync.log
cat ../sync.log | dotnet.exe payroll/Payroll.Tool.dll mail results
