﻿<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <OutputType>Exe</OutputType>
    <TargetFrameworks>net8.0</TargetFrameworks>
    <Configurations>Release;CoreOnly</Configurations>
    <SatelliteResourceLanguages>en-US</SatelliteResourceLanguages>
    <Platforms>AnyCPU</Platforms>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="CommandLineParser" Version="2.9.1" />
    <PackageReference Include="Newtonsoft.Json" Version="13.0.3" />
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\..\..\Payroll.Shared\Payroll.Shared.csproj" />
    <ProjectReference Include="..\..\..\Toast.Tool\Toast.Tool.csproj" />
  </ItemGroup>

</Project>
