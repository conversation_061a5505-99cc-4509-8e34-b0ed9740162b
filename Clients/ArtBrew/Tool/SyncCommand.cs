﻿using System;
using System.Collections.Generic;
using System.Linq;
using Newtonsoft.Json;
using Serilog;
using Payroll.Shared;
using System.Globalization;
using Toast.Tool;

namespace ArtBrew.Tool
{
    class SyncCommand
    {
        private EmployeeDirectoryByLocation EmployeeDirectoryByLocation { get; set; }
        private JobDirectory JobDirectory { get; set; }
        private SortedDictionary<string, Toast.Tool.RestaurantInfo> RestaurantDirectory { get; set; }

        void LoadDirectories(ToastService service)
        {
            EmployeeDirectoryByLocation = service.EmployeesByRestaurant().Result;
            JobDirectory = service.JobsByRestaurant().Result;
            RestaurantDirectory = Toast.Tool.Config.RestaurantsByCode();
        }

        public void Employees(List<string> args)
        {
            try
            {
                if (!ConsoleService.TryGetEmployeesFromInput(out var employees))
                {
                    Log.Logger.Error("Failed to parse employees list");
                    return;
                }

                if (employees == null || employees.Count == 0)
                {
                    Log.Logger.Error("Failed to load employees list");
                    return;
                }

                var dryRun = !(args != null && args.Count > 0 && args[0] == "doit");
                ProcessEmployees(employees, dryRun);
            }
            catch (Exception e)
            {
                Log.Logger.Fatal(e.Message);
                Log.Logger.Fatal(e.StackTrace);
            }
        }

        public void ProcessEmployees(IEnumerable<Payroll.Shared.Employee> employees, bool dryRun)
        {
            // basically another safety measure
            var executionMode = Toast.Tool.Config.ExecutionMode();
            if (executionMode != ExecutionMode.Execute)
            {
                dryRun = true;
                Log.Logger.Information("Mode={mode}, DryRun={dr}", executionMode, dryRun);
            }

            using (var service = new ToastService())
            {
                LoadDirectories(service);

                foreach (var employee in employees)
                {
                    if (string.IsNullOrEmpty(employee.ClockSeq))
                    {
                        Log.Logger.Information("  No clock sequence specified, skipping {fname} {lname} - {cseq}",
                            employee.FirstName, employee.LastName, employee.ClockSeq);
                        continue;
                    }

                    if (string.IsNullOrEmpty(employee.PrimaryWorkLocation))
                    {
                        // this employee may be terminated, so don't bark in that case
                        if (employee.Active)
                        {
                            Log.Logger.Warning(
                                "  No home location, skipping active employee record: {fname} {lname} - {cseq}",
                                employee.FirstName, employee.LastName, employee.ClockSeq);
                        }

                        continue;
                    }

                    if (!RestaurantDirectory.TryGetValue(employee.PrimaryWorkLocation, out Toast.Tool.RestaurantInfo restaurant))
                    {
                        Log.Logger.Debug(
                            "  Invalid or disabled toast restaurant code: {rid}, skipping {fname} {lname} - {cseq}",
                            employee.PrimaryWorkLocation, employee.FirstName, employee.LastName, employee.ClockSeq);
                        continue;
                    }

                    if (!EmployeeDirectoryByLocation.TryGetValue(employee.PrimaryWorkLocation,
                        out var employeeDirectory))
                    {
                        Log.Logger.Error(
                            "  Failed to locate employee directory {rid}, skipping {fname} {lname} - {cseq}",
                            employee.PrimaryWorkLocation, employee.FirstName, employee.LastName, employee.ClockSeq);
                        continue;
                    }

                    if (!employeeDirectory.TryGetValue(employee.ClockSeq, out Toast.Tool.Employee toastEmployee))
                    {
                        if (!employee.Active)
                        {
                            Log.Logger.Debug("  Skipping creation of an already inactive employee {cseq}",
                                employee.ClockSeq);
                            continue;
                        }

                        var duplicateEmailAddress = false;
                        foreach (var edItem in employeeDirectory)
                        {
                            if (edItem.Value.Email == employee.WorkEmail)
                            {
                                duplicateEmailAddress = true;
                                break;
                            }
                        }

                        if (duplicateEmailAddress)
                        {
                            Log.Logger.Warning(
                                "  Skipping creation of employee {cseq} with duplicative email address {email}",
                                employee.ClockSeq, employee.WorkEmail);
                            continue;
                        }

                        Log.Logger.Information("Creating new employee {fname} {lname} - {cseq} at location {loc}",
                            employee.FirstName, employee.LastName, employee.ClockSeq, employee.PrimaryWorkLocation);

                        // probably not a general issue, but when we imported manual RAS report
                        // the numbers had commas in them
                        if (!int.TryParse(employee.ClockSeq, NumberStyles.AllowThousands,
                            CultureInfo.InvariantCulture, out int clockseq))
                        {
                            Log.Logger.Error("  Failed to convert external id {cseq} to integer passcode",
                                clockseq);
                            continue;
                        }

                        // setup a new employee record
                        toastEmployee = new Toast.Tool.Employee()
                        {
                            Passcode = clockseq.ToString(),
                            ExternalEmployeeId = clockseq.ToString(),
                            Email = employee.WorkEmail
                        };
                    }

                    // update toast record as needed
                    var employeeUpdate = false;

                    if (toastEmployee.FirstName != employee.FirstName)
                    {
                        Log.Logger.Information("  Updating first name from '{old}' to '{new}' - {cseq}  at location {loc} ",
                            toastEmployee.FirstName, employee.FirstName, employee.ClockSeq, employee.PrimaryWorkLocation);

                        toastEmployee.FirstName = employee.FirstName;
                        employeeUpdate = true;
                    }

                    if (toastEmployee.LastName != employee.LastName)
                    {
                        Log.Logger.Information("  Updating last name from '{old}' to '{new}' - {cseq}  at location {loc}",
                            toastEmployee.LastName, employee.LastName, employee.ClockSeq, employee.PrimaryWorkLocation);

                        toastEmployee.LastName = employee.LastName;
                        employeeUpdate = true;
                    }

                    // employees HAVE to have an email address, so make up something 
                    // if we are here with an empty email address
                    if (string.IsNullOrEmpty(toastEmployee.Email))
                    {
                        var pseudoEmail = $"{employee.FirstName}.{employee.LastName}@thompson-toast-api.com";
                        toastEmployee.Email = pseudoEmail.Replace(" ", "");
                        employeeUpdate = true;
                    }

                    var jobAndWageStatus = JobDirectory.MapJobsToToastEmployee(employee, toastEmployee);

                    // new hire
                    if (toastEmployee.Guid == Guid.Empty)
                    {
                        if (dryRun)
                        {
                            Log.Logger.Information(
                                "Dry Run Mode: would have added employee {fname} {lname} - {cseq}",
                                toastEmployee.FirstName, toastEmployee.LastName, employee.ClockSeq);
                        }
                        else
                        {
                            Log.Logger.Information("Execution Mode: adding employee {fname} {lname} - {cseq}",
                                toastEmployee.FirstName, toastEmployee.LastName, employee.ClockSeq);

                            var addedEmployee = service.AddEmployee(restaurant.Id, toastEmployee).Result;
                        }

                        continue;
                    }

                    // Now handle deletions...
                    // If employee has be deactivated, make sure in toast deleted
                    if (!employee.Active)
                    {
                        if (!toastEmployee.Deleted)
                        {
                            if (dryRun)
                            {
                                Log.Logger.Information(
                                    "Dry Run Mode: would have terminated employee {fname} {lname} - {cseq}",
                                    toastEmployee.FirstName, toastEmployee.LastName, employee.Id);
                            }
                            else
                            {
                                Log.Logger.Information("Execution Mode: terminating employee {fname} {lname} - {cseq}",
                                    toastEmployee.FirstName, toastEmployee.LastName, employee.Id);

                                var deletedEmployee = service.DeleteEmployee(restaurant.Id, toastEmployee).Result;
                            }
                        }

                        continue;
                    }

                    // Now handle updates...
                    if (employeeUpdate)
                    {
                        if (dryRun)
                        {
                            Log.Logger.Information(
                                "Dry Run Mode: would have updated employee {fname} {lname} - {cseq}",
                                toastEmployee.FirstName, toastEmployee.LastName, employee.Id);
                        }
                        else
                        {
                            Log.Logger.Information("Execution Mode: updating employee {fname} {lname} - {cseq}",
                                toastEmployee.FirstName, toastEmployee.LastName, employee.Id);

                            var updatedEmployee = service.UpdateEmployee(restaurant.Id, toastEmployee).Result;
                        }
                    }

                    if (jobAndWageStatus.needToUpdateJobs)
                    {
                        if (dryRun)
                        {
                            Log.Logger.Information(
                                "Dry Run Mode: would have added jobs for employee {fname} {lname}",
                                toastEmployee.FirstName, toastEmployee.LastName);
                        }
                        else
                        {
                            Log.Logger.Information("Execution Mode: adding jobs for employee {fname} {lname}",
                                toastEmployee.FirstName, toastEmployee.LastName);

                            var updatedEmployee = service.UpdateJobs(restaurant.Id, toastEmployee).Result;
                        }
                    }

                    if (jobAndWageStatus.needToUpdateWages)
                    {
                        if (dryRun)
                        {
                            Log.Logger.Information(
                                "Dry Run Mode: would have modified wages for employee {fname} {lname}, jobs: {jcnt}, wages: {wcnt}",
                                toastEmployee.FirstName, toastEmployee.LastName, toastEmployee.JobReferences.Count,
                                toastEmployee.WageOverrides.Count);
                        }
                        else
                        {
                            Log.Logger.Information(
                                "Execution Mode: modifying wages for employee {fname} {lname}, jobs: {jcnt}, wages: {wcnt}",
                                toastEmployee.FirstName, toastEmployee.LastName, 
                                toastEmployee.JobReferences.Count,
                                toastEmployee.WageOverrides.Count);
                            Log.Logger.Debug("Jobs: {jobs}", JsonConvert.SerializeObject(toastEmployee.JobReferences));
                            Log.Logger.Debug("Wages: {wages}", JsonConvert.SerializeObject(toastEmployee.WageOverrides));
                            var updatedWagesEmployee =
                                service.UpdateWageOverrides(restaurant.Id, toastEmployee).Result;
                        }
                    }
                } // foreach employee
            } //using toast service
        }
    }
}