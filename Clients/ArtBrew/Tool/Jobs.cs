﻿using System.Collections.Generic;

namespace ArtBrew.Tool
{
    public enum UserType
    {
        None = 1, Training = 2, Server = 3, Bartender = 4, AsstMgr = 5, Manager = 6
    }

    public class PosJob
    {
        public string Id { get; set; }
        public string Code { get; set; }
        public string Name { get; set; }
        public UserType UserType { get; set; }
    }

    static public class Map
    {
        // this gets the actual database id. The enum values above set the hierarchy of privileges by the int value
        static public string UserTypeToPosCode(UserType userType)
        {
            switch (userType) {
                case UserType.None:
                    return "0";
                case UserType.Manager:
                    return "1";
                case UserType.Bartender:
                    return "2";
                case UserType.Server:
                    return "3";
                case UserType.Training:
                    return "4";
                case UserType.AsstMgr:
                    return "6";
            }

            return "0";
        }

        static public Dictionary<string, PosJob> Jobs()
        {
            var map = new Dictionary<string, PosJob>();
            map.Add("225", new PosJob() { Id = "22", Code = "225", Name = "TOGO", UserType = UserType.None });
            map.Add("226", new PosJob() { Id = "30", Code = "226", Name = "SERVER", UserType = UserType.Server });
            map.Add("228", new PosJob() { Id = "23", Code = "228", Name = "FOH NRO", UserType = UserType.AsstMgr });
            map.Add("231", new PosJob() { Id = "25", Code = "231", Name = "LRT SERVER", UserType = UserType.Bartender });
            map.Add("239", new PosJob() { Id = "3", Code = "239", Name = "DINING ROOM SUPERVI", UserType = UserType.Manager });
            map.Add("246", new PosJob() { Id = "14", Code = "246", Name = "PATIO BAR", UserType = UserType.Bartender });
            map.Add("247", new PosJob() { Id = "21", Code = "247", Name = "SERVICE BAR", UserType = UserType.Bartender });
            map.Add("248", new PosJob() { Id = "16", Code = "248", Name = "PATIO SERVER", UserType = UserType.Server });
            map.Add("254", new PosJob() { Id = "31", Code = "254", Name = "BARTENDER", UserType = UserType.Bartender });
            return map;
        }
    }
}
