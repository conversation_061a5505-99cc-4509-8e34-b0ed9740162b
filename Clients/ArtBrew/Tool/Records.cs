﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Xml;
using System.Xml.Serialization;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;
using Payroll.Shared;
using Serilog;

namespace ArtBrew.Tool
{
    [XmlRoot(ElementName="EMPLOYEES")]
    public class RECORDS { 

        [XmlElement(ElementName="DETAILS")] 
        public List<UltiProEmployee> Employees { get; set; } 
    }

    [XmlRoot(ElementName = "DETAILS")]
    public class UltiProEmployee
    {
        [XmlElement(ElementName = "COMPANYCD")]
        public string COMPANYCD { get; set; }

        [XmlElement(ElementName = "ULTIPROEENUMBER")]
        public string ULTIPROEENUMBER { get; set; }

        [XmlElement(ElementName = "ALTEMPLNUMBER")]
        public string ALTEMPLNUMBER { get; set; }

        [XmlElement(ElementName = "FIRSTNAME")]
        public string FIRSTNAME { get; set; }

        [XmlElement(ElementName = "LASTNAME")] public string LASTNAME { get; set; }

        [XmlElement(ElementName = "EMPLSTATUS")]
        public string EMPLSTATUS { get; set; }

        [XmlElement(ElementName = "DATEOFBIRTH")]
        public string DATEOFBIRTH { get; set; }

        [XmlElement(ElementName = "DATEHIRED")]
        public string DATEHIRED { get; set; }

        [XmlElement(ElementName = "PHONE")] public string PHONE { get; set; }

        [XmlElement(ElementName = "CLASSCD")] public string CLASSCD { get; set; }

        [XmlElement(ElementName = "ADDRESS1")] public string ADDRESS1 { get; set; }

        [XmlElement(ElementName = "ADDRESS2")] public string ADDRESS2 { get; set; }

        [XmlElement(ElementName = "CITY")] public string CITY { get; set; }

        [XmlElement(ElementName = "STATE")] public string STATE { get; set; }

        [XmlElement(ElementName = "ZIP")] public string ZIP { get; set; }

        [XmlElement(ElementName = "STORENUMBER1")]
        public string STORENUMBER1 { get; set; }

        [XmlElement(ElementName = "STORENUMBER2")]
        public string STORENUMBER2 { get; set; }

        [XmlElement(ElementName = "STORENUMBER3")]
        public string STORENUMBER3 { get; set; }

        [XmlElement(ElementName = "STORENUMBER4")]
        public string STORENUMBER4 { get; set; }

        [XmlElement(ElementName = "STORENUMBER5")]
        public string STORENUMBER5 { get; set; }

        [XmlElement(ElementName = "JOBNAME1")] public string JOBNAME1 { get; set; }

        [XmlElement(ElementName = "JOBNUMBER1")]
        public string JOBNUMBER1 { get; set; }

        [XmlElement(ElementName = "RATE1")] public string RATE1 { get; set; }

        [XmlElement(ElementName = "JOBNAME2")] public string JOBNAME2 { get; set; }

        [XmlElement(ElementName = "JOBNUMBER2")]
        public string JOBNUMBER2 { get; set; }

        [XmlElement(ElementName = "RATE2")] public string RATE2 { get; set; }

        [XmlElement(ElementName = "JOBNAME3")] public string JOBNAME3 { get; set; }

        [XmlElement(ElementName = "JOBNUMBER3")]
        public string JOBNUMBER3 { get; set; }

        [XmlElement(ElementName = "RATE3")] public string RATE3 { get; set; }

        [XmlElement(ElementName = "JOBNAME4")] public string JOBNAME4 { get; set; }

        [XmlElement(ElementName = "JOBNUMBER4")]
        public string JOBNUMBER4 { get; set; }

        [XmlElement(ElementName = "RATE4")] public string RATE4 { get; set; }

        [XmlElement(ElementName = "JOBNAME5")] public string JOBNAME5 { get; set; }

        [XmlElement(ElementName = "JOBNUMBER5")]
        public string JOBNUMBER5 { get; set; }

        [XmlElement(ElementName = "RATE5")] public string RATE5 { get; set; }

        [XmlElement(ElementName = "JOBNAME6")] public string JOBNAME6 { get; set; }

        [XmlElement(ElementName = "JOBNUMBER6")]
        public string JOBNUMBER6 { get; set; }

        [XmlElement(ElementName = "RATE6")] public string RATE6 { get; set; }

        [XmlElement(ElementName = "JOBNAME7")] public string JOBNAME7 { get; set; }

        [XmlElement(ElementName = "JOBNUMBER7")]
        public string JOBNUMBER7 { get; set; }

        [XmlElement(ElementName = "RATE7")] public string RATE7 { get; set; }

        [XmlElement(ElementName = "JOBNAME8")] public string JOBNAME8 { get; set; }

        [XmlElement(ElementName = "JOBNUMBER8")]
        public string JOBNUMBER8 { get; set; }

        [XmlElement(ElementName = "RATE8")] public string RATE8 { get; set; }

        [XmlElement(ElementName = "JOBNAME9")] public string JOBNAME9 { get; set; }

        [XmlElement(ElementName = "JOBNUMBER9")]
        public string JOBNUMBER9 { get; set; }

        [XmlElement(ElementName = "RATE9")] public string RATE9 { get; set; }

        [XmlElement(ElementName = "JOBNAME10")]
        public string JOBNAME10 { get; set; }

        [XmlElement(ElementName = "JOBNUMBER10")]
        public string JOBNUMBER10 { get; set; }

        [XmlElement(ElementName = "RATE10")] public string RATE10 { get; set; }

        [XmlElement(ElementName = "MAXACTIONDATETIME")]
        public int MAXACTIONDATETIME { get; set; }

        [XmlElement(ElementName = "TERMINATIONDATE")]
        public string TERMINATIONDATE { get; set; }

        [XmlElement(ElementName = "PRIMARYEMAILADDRESS")]
        public string PRIMARYEMAILADDRESS { get; set; }
    }
}