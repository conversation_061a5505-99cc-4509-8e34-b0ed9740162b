﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Runtime.CompilerServices;
using Payroll.Shared;

namespace ArtBrew.Tool
{
    public static class Config
    {
        public readonly static string SettingSection = "artbrew";
        private const string LocationSection = "ultipro_restaurants";
        private const string GlCodesSection = "artbrew_glcodes";
        private static readonly string TipScheme = "toast_tip_scheme";

        public static string DefaultTipCode { get; }
        public static Dictionary<string, string> TipCodes { get; }
        public static Dictionary<string, string> TipSchemeByRestaurantCode { get; }

        static Config()
        {
            Setting.Init();
            TipCodes = Setting.ListSection("artbrew_tip_codes");
            DefaultTipCode = Setting.Get(SettingSection, "default_tips_code");
            
            var section = Setting.ListSection(TipScheme);
            TipSchemeByRestaurantCode = section.ToDictionary(item => item.Key, item => item.Value);
        }

        public static Dictionary<string, string> RestaurantsById()
        {
            var map = new Dictionary<string, string>();
            var section = Setting.ListSection(LocationSection);

            foreach (var item in section)
            {
                map.Add(item.Key, item.Value);
            }

            return map;
        }
        
        public static string TipSchemeForRestaurant(string restaurantAbbrev)
        {
            var tipScheme = "total";
            if (TipSchemeByRestaurantCode.ContainsKey(restaurantAbbrev))
                tipScheme = TipSchemeByRestaurantCode[restaurantAbbrev];

            return tipScheme;
        }

        public static Dictionary<string, string> LocationCodes()
        {
            return Setting.ListSectionReversed(LocationSection);
        }

        public static Dictionary<string, string> GlCodes()
        {
            return Setting.ListSection(GlCodesSection);
        }

        public static string TipCodeForLocation(string location)
        {
            return TipCodes.ContainsKey(location) ? TipCodes[location] : DefaultTipCode;
        }
    }
}
