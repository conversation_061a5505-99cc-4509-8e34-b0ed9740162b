﻿using System;
using System.Collections.Generic;
using CommandLine;
using Payroll.Shared;
using ArtBrew.Tool;
using System.Linq;

namespace ArtBrew.Tool
{
    class Program : ProgramBase<SettingCommand>
    {
        private static readonly string AppVersion = "1.3 (tip cleanup)";

        static void DoShowUsage()
        {
            Console.WriteLine("Usage: ArtBrew.Tool.exe <command> <command-args>");
            Console.WriteLine("  where <command> is one of...");
            Console.WriteLine("   - import [punches, changes] = custom ArtBrew time import");
            Console.WriteLine("   - info - show version and tool information");
            Console.WriteLine("   - sync [employees]");
        }

        public override int ShowUsage()
        {
            DoShowUsage();
            return 0;
        }

        public void Help(List<string> args)
        {
            ShowUsage();
        }

        public int Import(List<string> args)
        {
            var command = new CommandArguments(args);
            return Command<ImportCommand>.Invoke(command);
        }

        public int Sync(List<string> args)
        {
            var command = new CommandArguments(args);
            return Command<SyncCommand>.Invoke(command);
        }

        public void Info(List<string> args)
        {
            Console.WriteLine($"ArtBrew.Tool.exe version {AppVersion}");
            Console.WriteLine($"  default tips code = {Config.DefaultTipCode}");

            var glCodes = Config.GlCodes();
            Console.WriteLine();
            Console.WriteLine("GL Codes");
            Console.WriteLine("====================================================");
            foreach (var (key, value) in glCodes)
            {
                Console.WriteLine($"{key} = {value}");
            }

            var locationCodes = Config.LocationCodes();
            Console.WriteLine();
            Console.WriteLine("Location Codes");
            Console.WriteLine("====================================================");
            foreach (var (key, value) in locationCodes)
            {
                var tcode = Config.TipCodeForLocation(key);
                Console.WriteLine($"{key} = {value}, tip code = {tcode}");
            }

            Console.WriteLine();
            Console.WriteLine("Tip Schemes");
            Console.WriteLine("====================================================");
            foreach (var (key, value) in Config.TipSchemeByRestaurantCode.OrderBy(x=>x.Key))
            {
                Console.WriteLine($"{key} = {value}");
            }
        }

        static int Main(string[] args)
        {
            if (args.Length == 0)
            {
                DoShowUsage();
                return -1;
            }

            // setup logging services...
            var command = string.Join(" ", args);
            Payroll.Shared.Logger.Setup($"ArtBrew.Tool.Exe - Command: '{command}', Version: {AppVersion}");

            try
            {
                Parser.Default.ParseArguments<ProgramArguments>(args)
                    .MapResult((ProgramArguments opts) => ProgramDriver<Program>.Run(opts), errs => 1);
            }
            catch (Exception e)
            {
                Console.WriteLine(e.Message);
                Console.WriteLine(e.StackTrace);
                return -1;
            }

            return 0;
        }
    }
}
