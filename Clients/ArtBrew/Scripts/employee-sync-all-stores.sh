#!/bin/bash
# this script gets new employees and updated employees via the UltiPro CSV exports

TODAY=`date +day%d-hr%H`

for filename in /home/<USER>/inbox/*.txt; do
    [ -e "$filename" ] || continue
    F=`basename $filename .txt`
    echo Processing $filename
    cat $filename | dotnet ./client/ArtBrew.Tool.dll import changes > /opt/PayrollTools/Mailbox/changes/$F.json
    cat /opt/PayrollTools/Mailbox/changes/$F.json | LOGFILE=/opt/PayrollTools/Logs/$F.log dotnet ./client/ArtBrew.Tool.dll sync employees doit
    mv $filename /opt/PayrollTools/Archive/changes/
    fnamebase=`basename $filename`

    # EMAIL THE TIME REPORT
    echo "Please find attached the changes imported from $F" > /opt/PayrollTools/Mailbox/change.email
    
    cat /opt/PayrollTools/Mailbox/time.email | mail -A /opt/PayrollTools/Logs/$F.log -s "ArtBrew Change Report for $F - $TODAY"  <EMAIL>
    cat /opt/PayrollTools/Logs/$F.log | grep ERR | while read line; do gcloud logging write "empsync" "$line"; done
    
    echo mail $filename to  <EMAIL>
    gzip /opt/PayrollTools/Archive/changes/$fnamebase
done

# only keep 40 days worth of files (4 files per day)
find /opt/PayrollTools/Logs/ -type f -mtime +40 -delete
find /opt/PayrollTools/Mailbox/ -type f -mtime +40 -delete
find /opt/PayrollTools/Archive/ -type f -mtime +40 -delete
