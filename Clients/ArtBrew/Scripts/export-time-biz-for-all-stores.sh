#!/bin/sh
mkdir -p ../Mailbox/time/
mkdir -p ../Archive/time
mkdir -p ../Archive/punches

TODAY=`date +%Y%m%d`
WEEKNUM=`date +%V`
TIME_FNAME="time"
PUNCH_FNAME="punches"

for i in `dotnet toast/Toast.Tool.dll restaurant list brief`; do
  LOGFILE="../Mailbox/bus-time.log" dotnet toast/Toast.Tool.dll export time2 $i 7 1 > ../Mailbox/time/bus-$i.json
  cat ../Mailbox/time/bus-$i.json | dotnet ./client/ArtBrew.Tool.dll import punches $i > ../Mailbox/punches/bus-ABV-$TODAY-$i.csv

  cp ../Mailbox/punches/bus-ABV-$TODAY-$i.csv ../Archive/punches/
  mv ../Mailbox/time/bus-$i.json ../Archive/time/bus-$WEEKNUM-$i.json

  # EMAIL THE TIME REPORT
  echo "Please find attached the BUSINESS TIME based punches for $i for week number $WEEKNUM" > ../Mailbox/bus-time.email
  echo "" >> ../Mailbox/bus-time.email
  echo "Software: `dotnet toast/Toast.Tool.dll version`" >> ../Mailbox/bus-time.email
  echo "" >> ../Mailbox/bus-time.email
  echo "Generated: `date`" >> ../Mailbox/bus-time.email

  cat ../Mailbox/bus-time.email | mail -A ../Archive/punches/bus-ABV-$TODAY-$i.csv -s "ArtBrew Business Time Punch Report for $i - $TODAY" <EMAIL>
  #cat ../Mailbox/bus-time.email | mail -A ../Archive/punches/bus-ABV-$TODAY-$i.csv -s "ArtBrew Business Time Punch Report for $i - $TODAY" <EMAIL>

  gzip -f ../Archive/punches/bus-ABV-$TODAY-$i.csv
  gzip -f ../Archive/time/bus-$WEEKNUM-$i.json

done
