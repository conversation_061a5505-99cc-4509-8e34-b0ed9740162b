#!/bin/sh
mkdir -p ../Mailbox/time/
mkdir -p ../Archive/time
mkdir -p ../Archive/punches

TODAY=`date +%Y%m%d`
WEEKNUM=`date +%V`
TIME_FNAME="time"
PUNCH_FNAME="punches"

for i in `dotnet toast/Toast.Tool.dll restaurant list brief`; do
  LOGFILE="../Mailbox/time.log" dotnet toast/Toast.Tool.dll export time $i 7 1 > ../Mailbox/time/$i.json
  cat ../Mailbox/time/$i.json | dotnet ./client/ArtBrew.Tool.dll import punches $i > ../Mailbox/punches/ABV-$TODAY-$i.csv
  
  cp ../Mailbox/punches/ABV-$TODAY-$i.csv ../Archive/punches/
  mv ../Mailbox/time/$i.json ../Archive/time/$WEEKNUM-$i.json
  
  # EMAIL THE TIME REPORT
  echo "<html><body>" > ../Mailbox/time.email
  echo "Please find attached the time punches for $i for week number $WEEKNUM" >> ../Mailbox/time.email
  echo "</body></html>" >> ../Mailbox/time.email

  #cat ../Mailbox/time.email | mail -A ../Archive/punches/ABV-$TODAY-$i.csv -a "Content-type: text/html" -s "ArtBrew Punch Report for $i - $TODAY" <EMAIL>
  cat ../Mailbox/time.email | mail -A ../Archive/punches/ABV-$TODAY-$i.csv -a "Content-type: text/html" -s "ArtBrew Punch Report for $i - $TODAY" <EMAIL>

  gzip -f ../Archive/punches/ABV-$TODAY-$i.csv
  gzip -f ../Archive/time/$WEEKNUM-$i.json
  
done
