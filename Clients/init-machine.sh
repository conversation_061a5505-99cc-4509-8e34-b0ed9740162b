#!/bin/sh
# This script makes sure that we have all the directories and software set up that we need

DOM=`date +%d`
HOUR=`date +%H`
TODAY=`date +day%d-hr%H`

apt-get update && apt-get upgrade
adduser --disabled-password oadmin
mkdir /usr/share/PayrollTools/
chown oadmin /usr/share/PayrollTools/
mkdir -p /opt/openarc/Production
chown oadmin /opt/openarc/Production
cd /opt/openarc/Production
mkdir -p ../Logs ../Logs/Paycom ../Logs/Client ../Logs/Crunch/
touch /etc/profile.d/ptools.sh && chown oadmin /etc/profile.d/ptools.sh

apt-get update && apt-get install gpg coreutils xdg-utils dotnet-sdk-8.0 diffstat zsh emacs-nox lftp
curl -fsSL https://pkgs.netbird.io/install.sh | sh
curl -fsSL https://apt.releases.hashicorp.com/gpg | gpg --dearmor -o /usr/share/keyrings/hashicorp-archive-keyring.gpg
echo "deb [signed-by=/usr/share/keyrings/hashicorp-archive-keyring.gpg] https://apt.releases.hashicorp.com $(lsb_release -cs) main" | tee /etc/apt/sources.list.d/hashicorp.list
apt update && apt install netbird boundary hcp
netbird up