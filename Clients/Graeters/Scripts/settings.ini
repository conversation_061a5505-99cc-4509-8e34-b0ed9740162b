[system]
dbtype = ldb
dbname = graeters

[paycom]
sid=036c02a550b45824a0546d464ac0a599d401c5936c7ee056d3f3cd2f16aa44da
token=a8aac1fa8fc2b24c0685d2d3dcf07db62bd58816d6aab4cb366df2a1a3439105
mode = dryrun
timezone = GMT
sensitive = 1
pending   = 1
breakmode = lunches
startbreak = OL
endbreak = IL

[paycom_fields]
location = loccode
organization = cat3
fname = nickname
budget = cat1
division_code = cat1
division_name = cat1desc
jobcode = cat2
jobloc = alloccode

[paycom_timeadj]
EST = 4
CST = 5

[dc]
endpoint=https://dc01.rmdatacentral.com/Portal1248/api
dc_key = v3cAIO8i1HaANe09csHNEFBBeVdY1K25t7T-wuV51sVTQzYhp4rNjVL1924D7GZTLb0
timecard_report = c1481531-bea1-4b4d-8ea0-9e0a76618c3c
payment_report = ebe98c9d-dd0a-459b-b621-275169f8c4bc
pto_memo_type = bd37dec7-bb75-4bda-a012-a7a2c67eebd9
hire_mode=dryrun
sync_mode=dryrun
term_mode=dryrun
pto_mode=dryrun
sync_limit = 3
hire_limit = 3
term_limit = 3
adjust_for_timezone = 1

[dc_fields]
email = work

[graeters_skip_store_numbers]
70 = true
74 = true

[jobs_map]
dde39731-4140-4479-8a34-08cbca199f6d = Trainer
f62ac983-61fb-4c5f-80fe-0b56852f5b37 = Kitchen
f9e05380-346b-42e1-8d9b-0f00b4cbc5eb = 16 - 17 Team Member
fab779b6-380f-4177-86dc-10c319397e4d = ...SSM
b410534f-a499-4204-bb32-17d731d4eafb = Car Side/To Go
ff55b8b6-92b9-4739-8cfd-1a4f1434bf4f = Server
e49f60e0-d3a3-4869-9848-2efbfdae0059 = Bus
1d1070e9-c2e5-48b0-82db-32b856f09b4c = Tea Maker
a6a453d4-9616-4404-a003-3ab174b266bb = Host
8097097e-416f-4823-b074-4033dae11a23 = US Bank Arena
56373e13-ec0b-4a66-993f-4095138d7e6d = Cook
9f094670-38b6-49f0-924c-5122acdb8ed4 = Senior Store Manager
beec803d-85ab-4ae6-bdc3-57c39ae2f222 = Team Member
bfd5e253-8ccb-46ba-894f-5b2aa5efb0ca = Runner
f246818c-afd0-4259-a323-63952a9f592d = Factory
d31413b1-2c07-4f6f-a4b1-63babec495d5 = Dish
296f2613-48e9-477d-9020-76f0530ce241 = Minor
2c0ce917-51a0-4891-8585-8317d6ec4ec1 = Salaried Tippable
0e2802a5-ebc5-41d5-a7a4-87dc98e1a002 = Key
ccac24af-dc48-4c0e-829f-8996d6eae225 = Senior Team Leader
789ec0ce-0534-4be0-a31f-8af6c60b1ed1 = Assistant
64ad1632-a153-42b8-9225-c5781118345f = _Discard
74fd18b3-98c4-4d0c-bc63-c5dab2f7025b = Cocktail
********-eb55-4ab4-852b-c9a0e252ce4a = MIT
56f7c852-2623-41ab-9664-cae1e0d136ff = Counter
5ea099d6-4be0-49a0-9375-d3d122441fee = Driver
11cbf506-6d54-4d4d-8a97-e5c254a727a8 = Assistant Mgr
49ecd69e-d33e-4e5f-a389-e70df083bfd1 = RLT
dfc220c1-6dd4-4fd6-ad82-f282e8d19db5 = Manager
87e67a2d-cbb2-421d-974c-f68157808bdc = Team Leader
2c4b7673-79a2-4053-90bc-fcdb4836ddf6 = ..ASSISTANT MANAGER

[job_name_map]
Team Member = Team Member
Team Member 2 = Team Member
Team Member 3 = Team Member
Team Leader = Team Leader
Assistant = Assistant
Assistant Manager = Assistant
Manager = Manager
Manager In Training = Manager
Store Manager In Training = Manager
Store Manager = Manager
Store Manager II = Manager
Training Store Manager = Manager
Multi-Store Manager = Manager
Multi Store Manager = Manager
Food Truck Team Member = Team Member
Food Truck Team Leader = Team Leader
Senior Team Leader = Senior Team Leader
Food Truck Driver = Senior Team Leader
Food Truck Admin Assistant = Senior Team Leader
Catering and Food Truck Manager = Manager
Salaried Tippable = Salaried Tippable
Chicago Team Member = Team Member
Chicago Team Member 2 = Team Member
Chicago Senior Team Leader = Senior Team Leader
Retail Custodian = Retail Custodian
Factory = Retail Custodian

[job_status_change_reasons]
A = dfeff3cb-90ab-403c-8c70-0131c7135198
T = 0e74b62c-09d9-41d2-a07f-3542c4240913
L = 482f9eac-595c-48ca-8303-530d8ecf5a4c