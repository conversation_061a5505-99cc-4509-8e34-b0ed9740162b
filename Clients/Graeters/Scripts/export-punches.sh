#!/bin/bash

# HOUSEKEEPING
DOY=`date +%j`
WEEKNUM=`date +%V`

# Calculate if today is the right day to run (every 14 days from start date)
start_date="2024-09-03"  # Replace with your actual start date
today=$(date +%Y-%m-%d)

start_seconds=$(date -d "$start_date" +%s)
today_seconds=$(date -d "$today" +%s)
days_diff=$(( (today_seconds - start_seconds) / 86400 ))

target_hour=${1:-12}

if [ $((days_diff % 14)) -eq 0 ]; then

    # is it target_hour in Cincinnati?
    dotnet payroll/Payroll.Tool.dll time when $target_hour 39.11076189565238 -84.5152691083674

    # Capture the return code
    return_code=$?

    # Check the return code
    if [ $return_code -ne 0 ]; then
        echo "It is not ${target_hour} in Cincinnati"*-+
        exit $return_code
    fi

    # Run your command here
    echo "Running biweekly job on $today"
    # Add your actual command here

    # Export from DataCentral (14 day rolling window - expected to run on Tuesday. For Monday, use "15 2"
    LOGFILE=../Logs/DataCentral/time-$DOY.log dotnet ./dc/DataCentral.Tool.dll export time all 15 2 > ../Mailbox/time.json

    cat ../Mailbox/time.json | dotnet client/Graeters.Tool.dll import punches | dotnet paycom2/Paycom2.Tool.dll import punches doit

    cp ../Mailbox/time.json ../Archive/time-$WEEKNUM.json

    # only keep X days worth of files
    find /opt/openarc/Logs/ -type f -mtime +60 -delete
    find /opt/openarc/Mailbox/ -type f -mtime +180 -delete

fi
