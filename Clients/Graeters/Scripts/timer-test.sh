#!/bin/bash

# HOUSEKEEPING
DOY=`date +%j`
WEEKNUM=`date +%V`

# Calculate if today is the right day to run (every 14 days from start date)
start_date="2024-09-03"  # Replace with your actual start date
today=$(date +%Y-%m-%d)

start_seconds=$(date -d "$start_date" +%s)
today_seconds=$(date -d "$today" +%s)
days_diff=$(( (today_seconds - start_seconds) / 86400 ))

if [ $((days_diff % 14)) -eq 0 ]; then

    # is it 12:00 in Cincinnati?
    dotnet payroll/Payroll.Tool.dll time when 12 39.11076189565238 -84.5152691083674

    # Capture the return code
    return_code=$?

    # Check the return code
    if [ $return_code -ne 0 ]; then
        echo "It is not 12:00pm in Cincinnati"
        exit $return_code
    fi

    # Run your command here
    echo "Running biweekly job on $today, week:$WEEKNUM, doy: $DOY" >> ../Mailbox/timer-test.log

fi
