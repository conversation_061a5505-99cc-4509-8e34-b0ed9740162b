#!/bin/sh

DOM=`date +%d`
dotnet paycom2/Paycom2.Tool.dll cache dump term 60 | dotnet client/Graeters.Tool.dll import employees > ../terms.json
cat ../terms.json | LOGFILE=../Logs/DataCentral/terms-$DOM.log dotnet dc/DataCentral.Tool.dll import terms all doit

dotnet paycom2/Paycom2.Tool.dll cache dump cseq| dotnet client/Graeters.Tool.dll import employees > ../employees.json
cat ../employees.json | LOGFILE=../Logs/DataCentral/hires-$DOM.log dotnet dc/DataCentral.Tool.dll import hires all doit
cat ../employees.json | LOGFILE=../Logs/DataCentral/sync-$DOM.log dotnet dc/DataCentral.Tool.dll import sync all doit
