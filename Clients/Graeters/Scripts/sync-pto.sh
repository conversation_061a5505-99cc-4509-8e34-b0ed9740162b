#!/bin/sh

DOM=`date +%d`
DOY=`date +%j`

LOGFILE=../Logs/Paycom/pto-$DOM.log dotnet paycom2/Paycom2.Tool.dll export pto 1 0 | dotnet client/Graeters.Tool.dll import pto > ../Mailbox/pto.json
cat ../Mailbox/pto.json | LOGFILE=../Logs/DataCentral/pto-$DOM.log dotnet dc/DataCentral.Tool.dll import pto all

# keep 60 days of PTO files
cp ../Mailbox/pto.json ../Archive/pto-$DOY.json
find /opt/openarc/Archive/ -type f -mtime +60 -delete
