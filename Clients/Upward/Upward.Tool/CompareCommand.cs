﻿using System;
using System.Collections;
using System.Collections.Generic;
using System.Text.Json;
using Payroll.Shared;
using Serilog;

namespace Upward.Tool
{
    class CompareCommand
    {
        public void Employees(List<string> args)
        {
            try
            {
                if (!ConsoleService.TryGetEmployeesFromInput(out var employees))
                {
                    Log.Logger.Error("Failed to parse employees list");
                    return;
                }

                if (employees == null || employees.Count == 0)
                {
                    Log.Logger.Error("Failed to load employees list");
                    return;
                }

                Log.Logger.Information("Exception Type, Location, Employee Id, Employee Name, Description");
                using (var cache = CacheService.OpenCache(SystemConfig.CacheName()))
                {
                    var empCache = cache.GetCollection<Employee>("employees");

                    foreach (var employee in employees)
                    {
                        var findEmp = empCache.FindOne(x => x.Id == employee.Id);
                        if (findEmp == null)
                        {
                            Log.Logger.Information($"MissingEmployee, {employee.PrimaryWorkLocation}, {employee.Id}, {employee.FirstName} {employee.LastName}, Not found in Paylocity");
                            continue;
                        }

                        if (employee.Active == false && findEmp.Active == false)
                        {
                            Log.Logger.Debug($"Skipping inactive employee, {employee.PrimaryWorkLocation}, {employee.Id}, {employee.FirstName} {employee.LastName}");
                            continue;
                        }

                        if (Paylocity.Tool.Config.SkipEmployee(employee))
                        {
                            Log.Logger.Debug($"Skipping employee, location blacklisted, {employee.PrimaryWorkLocation}, {employee.Id}, {employee.FirstName} {employee.LastName}");
                            continue;
                        }

                        foreach (var job in employee.Jobs)
                        {
                            var cjob = findEmp.Jobs.FirstOrDefault(x => x.Code == job.Code);
                            if (cjob == null)
                            {
                                Log.Logger.Warning($"Missing Job, {employee.PrimaryWorkLocation}, {employee.Id}, {employee.FirstName} {employee.LastName}, The job {job.Code}/{job.Name} is not assigned in Paylocity");
                                continue;
                            }

                            if (job.Rate != cjob.Rate)
                            {
                                Log.Logger.Error($"Rate Mismatch, {employee.PrimaryWorkLocation}, {employee.Id}, {employee.FirstName} {employee.LastName}, For job {job.Code}/{job.Name} - Paylocity: {cjob.Rate} vs Toast: {job.Rate}");
                            }
                        }
                    }
                }
            }
            catch (Exception e)
            {
                Log.Logger.Fatal(e.Message);
                Log.Logger.Fatal(e.StackTrace ?? "");
            }
        }
    }
}
