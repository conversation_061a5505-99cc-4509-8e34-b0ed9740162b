﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Runtime.CompilerServices;
using Payroll.Shared;

namespace Upward.Tool
{
    public static class Config
    {
        public readonly static string SettingSection = "upward";

        static Config()
        {
            Setting.Init();
        }

        public static ExecutionMode ExecutionMode()
        {
            var mode = Setting.Get(SettingSection, "mode");
            if (string.IsNullOrEmpty(mode)) return Payroll.Shared.ExecutionMode.DryRun;
            return mode == "execute" ? Payroll.Shared.ExecutionMode.Execute : Payroll.Shared.ExecutionMode.DryRun;
        }
    }
}
