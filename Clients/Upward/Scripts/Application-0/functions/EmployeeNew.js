// This function is the endpoint's request handler.
exports = function ({ query, headers, body }, response) {
    // Data can be extracted from the request as follows:

    // Query params, e.g. '?arg1=hello&arg2=world' => {arg1: "hello", arg2: "world"}
    //const {arg1, arg2} = query;

    // Headers, e.g. {"Content-Type": ["application/json"]}
    const contentTypes = headers["Content-Type"];

    // Raw request body (if the client sent one).
    // This is a binary object that can be accessed as a string using .text()
    const pChange = EJSON.parse(body.text());
    console.log("Request body:", pChange);

    var change = {
        _id: pChange.employeeId,
        ChangedBy: context.request.remoteIPAddress,
        ChangeTime: Date.now(),
        ChangeDesc: context.request.httpUserAgent,
        Employee: {
            Id: pChange.employeeId,
            FirstName: pChange.employeeFirstName,
            LastName: pChange.employeeLastName,
            StreetAddress: pChange.employeeAddressLine1,
            City: pChange.employeeCity,
            State: pChange.employeeState,
            Zip: pChange.employeeZip,
            WorkEmail: pChange.employeeWorkEMailAddress,
            Phone: pChange.employeeWorkPhone,
            HomeLocation: pChange.employeeCostCenter1,
            DeptCode: pChange.employeeCostCenter2,
            Jobs: [{
                Code: pChange.employeeCostCenter3,
                Rate: pChange.employeePrimaryPayRate,
                IsPrimary: true
            }]
        }
    }

    //console.log("arg1, arg2: ", arg1, arg2);
    //console.log("Content-Type:", JSON.stringify(contentTypes));

    const mongodb = context.services.get("mongodb-atlas");
    const changesCollection = mongodb.db("ptools").collection("changes");

    changesCollection.insertOne(change)
        .then(result => console.log(`Successfully inserted change with _id: ${result.insertedId}`))
        .catch(err => console.error(`Failed to insert change: ${err}`))

    // when the "Respond with Result" setting is set.
    return JSON.stringify(change);
}
