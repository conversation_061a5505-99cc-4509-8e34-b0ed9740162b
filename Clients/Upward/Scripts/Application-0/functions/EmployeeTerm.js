// This function is the endpoint's request handler.
exports = function ({ query, headers, body }, response) {

    // Headers, e.g. {"Content-Type": ["application/json"]}
    const contentTypes = headers["Content-Type"];

    // Raw request body (if the client sent one).
    // This is a binary object that can be accessed as a string using .text()
    const pChange = EJSON.parse(body.text());
    console.log("Request body:", pChange);

    var change = {
        _id: pChange.employeeId,
        ChangedBy: context.request.remoteIPAddress,
        ChangeTime: Date.now(),
        ChangeDesc: context.request.httpUserAgent,
        Employee: {
            Id: pChange.employeeId,
            Active: false,
            TermDate: pChange.employeeTerminationDate
        }
    }

    const mongodb = context.services.get("mongodb-atlas");
    const changesCollection = mongodb.db("ptools").collection("changes");

    changesCollection.insertOne(change)
        .then(result => console.log(`Successfully inserted change with _id: ${result.insertedId}`))
        .catch(err => console.error(`Failed to insert change: ${err}`))

    // when the "Respond with Result" setting is set.
    return JSON.stringify(change);
};