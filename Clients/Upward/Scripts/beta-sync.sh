#!/bin/sh

DOM=`date +%d`
HOUR=`date +%H`
TODAY=`date +day%d-hr%H`

dotnet payroll/Payroll.Tool.dll export employees location 102 > ../Mailbox/central.json
dotnet payroll/Payroll.Tool.dll export employees location 108 > ../Mailbox/annex.json
cat ../Mailbox/central.json| LOGFILE=../Logs/Toast/toast-$DOM-central.log dotnet toast/Toast.Tool.dll --config beta import employees
cat ../Mailbox/annex.json| LOGFILE=../Logs/Toast/toast-$DOM-annex.log dotnet toast/Toast.Tool.dll --config beta import employees
