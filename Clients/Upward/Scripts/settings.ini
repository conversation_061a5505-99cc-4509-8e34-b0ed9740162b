[system]
dsn = mongodb+srv://upwards:<EMAIL>
dbname = ptools
smtp_host = smtp.sendgrid.com
smtp_user = apikey
smtp_pwd = *********************************************************************
notify_addr = <EMAIL>
notify_addr2 = <EMAIL>
from_addr = <EMAIL>

[radar]
username=
password=
baseUrl=

[7shifts]
apiUrl=https://api.7shifts.com/v2/
accessToken=30666334653539642d663832622d346234662d623930332d306336356338633064393539
companyId=220422

[7shifts_locations]
279289 = PostinoParkPlace

[radar_job_codes]
No role = 11
Dishwasher = 22
Line Cook = 33
Bartender = 44
Server = 55
SA = 66
SIT = 77
FOH Meet = 88
FOH Key = 99
Prep Cook = 111
Sous Chef = 222
BOH Meet = 333
Exec Chef = 444
BOH Key = 555
Host = 666
CT Server = 777
RM = 888
GM = 999
Delivery = 1111
CT Host = 2222
CT SA = 3333
NS TRN FOH = 4444
SAIT = 5555
HIT = 6666
CT BOH = 7777
BIT = 8888
BOH in Trn = 9999
CT BAR = 1234
NS TRN BOH = 112233
MIT = 4321

[radar_7shifts_location_map]
279289 = 28

[paylocity]
username = paylocity-upward
password = ju$t3n0ughtoBeStrong86
host = api.paylocity.com
companyid = 83831
clientid = 5J/1qVznfkWg1LE/Z1Qsoi04NTg1MjIwMjYwMTIwOTc2MzIw
secret = pFeO/EbyfobTzUKayigCWqA4Gc+mFe3qGCKiBl4uEUjpof2eHCaZXOmUF/QLkhRqo5gidDKjjHQ5tolkEP2wvQ==
sync_start_year = 2024

[paylocity_fields]
job_code = job

[paylocity_skip_locations]
100 = corporate office
118 = permenantly closed

[toast]
clientid = 9Tdt7q588X7y2BH7XTOaUPzAYvxAm6Kc
secret = OYUG9qFeNHx9_ldQPIyM3jWYX9_VV5aKb_LLsq4T5gZtcA3uMsibSs6ZazvkD44C
groupid = 801aea3a-4c07-42f6-aca7-1d8cabac35c6
host = ws-api.toasttab.com
defaultid = 6d2c8b3b-e330-4b85-9441-34e17bc191f9
delay = 1
sandhost = ws-sandbox-api.eng.toasttab.com
mode = execute
rm_jobs_mode=dryrun
hire_mode=dryrun
sync_mode=dryrun
term_mode=execute
sync_limit = 25
hire_limit = 5
term_limit = 5

[toast_restaurants]
#Postino Arcadia
6d2c8b3b-e330-4b85-9441-34e17bc191f9=101
#Postino Central
d66fe635-2bd2-4899-b9b0-8cdcea0644f1=102
#Windsor Churn
d0d02e40-3c52-4ab6-ab9a-57ca0b7e44d7=103
#Postino East
866a24c6-8e7c-488d-8327-9a73082e35e3=104
#Federal Pizza
bbbb9bc1-71c8-46a1-b5ff-c12e87c02ff7=105
#Joyride East
b954eb7a-92c9-43c4-95ec-b12113fd226b=106
#Joyride Central
2677725d-fdf8-4f86-8450-65291b212556=107
#Postino Annex
ad9f7c50-4694-46bf-b814-08591dc2cff5=108
#Postino LoHi
517f7067-3e97-406b-9b7b-0cfc0234eff8=109
#Postino Highland
cb3e7ac4-b45d-4576-9dfa-290a3f720ede=111
#Postino Heights
90cf19dd-7f18-4237-b675-102a1a2f9e44=112
#Postino Montrose
7f5d7f60-a7b2-40df-9386-242820d0938f=113
#Postino Kierland
f095feaf-7f94-4dab-a93a-909d12cfa9f9=110
#Postino Grant
d7cacdff-1ecf-4bd8-9986-0f33c9e2ff4a=116
#Postino South Tempe
2b81ad59-db1f-4980-9031-a39946118b09=117
#Postino Town & Country
626e0f72-4e00-40d6-ab4e-b3e7a971fa59=119
#Postino Uptown Park
e414314d-e79f-4c03-85bb-d0f912f03270=120
#Postino Cinco Ranch
d304a5ff-09a1-4a07-96f8-68d7f4b8eefc=123
#Postino Addison
a8043d2c-bb25-433c-8d61-61fff6ee4438=124
#Postino The Woodlands
dc707203-2ba6-4bbb-a520-46a64ab521f4=129
#Postino Rim
69ff2a03-c691-42d3-a23c-eb4055397d48=130
#Postino South Broadway
11e1852c-2812-4f77-8c7e-7d114f55df8f=131
#Postino South Lamar
753b446b-79ad-4e4e-8aea-4ce4b71a83f9=132
#Postino Little Italy
bffec8f1-bc65-4203-92b0-b4e96cd14244=135
#Postino Del Mar
1cbb5315-7e89-4947-91b0-4e775a4125c8=136
#Postino Ballantyne
879dbfa4-fc4b-4556-8968-2b4c09e08ee2=137
#Postino 12 South
965c42fd-cc0f-4637-afd2-576c4e710eea=138
#Postino Peoria
47ec668a-c04f-4226-8aaf-8c599eed365a=140

[toast_skip_jobs]
10200 = Not found 1
10021 = Not found 2

[toast_skip_wage_overrides]
c278480e-cffe-4842-9cc7-041d57b01db5 = Old BOH in TRN
dcfc957d-142f-48d7-b352-3a1d810d6322 = Another missing job
00860584-b876-40a1-9136-3f2ef8431171 = Another bad one

# this is currently UNUSED
[toast_skip_employees]
<EMAIL> = Ashlyn Poly
<EMAIL> = Monica Menchaca
<EMAIL> = Trish Dalrymple
<EMAIL> = Carlos Buscaglia
<EMAIL> = Claudia Galas
<EMAIL> = Brent Renner
<EMAIL> = Ari Mangual
<EMAIL> = Halley McCarthy
<EMAIL> = IT Admin
<EMAIL> = Felix Kaminski
<EMAIL> = Tanni Rednor
<EMAIL> = DoorDash
<EMAIL> = Cory Lattuca
<EMAIL> = Brent Karlicek
<EMAIL> = Colleen Power
<EMAIL> = QA Ingest
<EMAIL> = Phillip Bock
<EMAIL> = Cathryn Kerkman
<EMAIL> = Blake Deffenbacher
<EMAIL> = Luke Christopherson
<EMAIL> = Sarah Hart
<EMAIL> = Rosana DeMar
<EMAIL> = Dro Gonzalez
<EMAIL> = Nina Camay
<EMAIL> = Juba Malou
<EMAIL> = Sarah Pak
<EMAIL> = Kenneth Hoffman
<EMAIL> = Marissa Travis
<EMAIL> = Michael Magdaleno
<EMAIL> = Michael Wang
<EMAIL> = Brian Anderson

[toast_jobs]
10001	=	23c09521-a616-4dae-bb9d-a5cbe8df1a26	#	FOH Training	---	FOH Training
10016	=	92e719d4-5914-4c75-ba3a-374341fca8ed	#	CT Bar	---	Certified Trainer Bar
10017	=	6fc65e70-3359-445e-a1f3-d6b9fd58793f	#	CT HOST	---	Certified Trainer Host
10018	=	e7905c80-dbe5-4835-83af-5118d57edac0	#	CT SA	---	Certified Trainer SA
10019	=	121f1ad7-4414-4088-9e01-4e354208f52c	#	CT Server	---	Certified Trainer Server
10002	=	f0522ee6-1596-4436-b127-db116e880fc2	#	FOH CT	---	FOH Certified Trainer
10003	=	1c65b90a-d3d0-4bba-ac59-63528e46e854	#	Server	---	Server
10004	=	57c39c3b-72e2-4d39-8aaa-db921f4f25bb	#	SA	---	Service Assistant
10005	=	216d1686-52b8-4e31-ac9b-2c6c874fc51d	#	Bartender	---	Bartender
10006	=	becec26c-7a52-4d9c-947a-a1804042358b	#	Bar Assist	---	Bar Assistant
10007	=	7fe5d809-1ec8-4056-a496-45370a45869d	#	Host	---	Host
10010	=	5701be5d-f6fd-42da-b692-833f2f27d171	#	Delivery	---	Catering Delivery
10014   =   c0c6e3c8-1ae5-466b-ad87-ac05377d2c80    #   Takeout FOH	---	Takeout Hospitality
20001	=	526c3f31-b58e-4a70-8b78-86b25dab5bec	#	BOH Training	---	BOH Training
20002	=	f361b178-0455-4fa0-9e90-d0a99532170f	#	CT BOH	---	BOH Certified Trainer
20003	=	ae46c08f-2bd5-4c68-880c-a1f9db870fb6	#	Dishwasher	---	Dishwasher
20004	=	8384f6a6-ce2a-44cd-a504-01ac11c72311	#	Prep Cook	---	Prep Cook
20005	=	60c2caf9-0406-4975-a10b-eff6ba1eb14c	#	Line Cook	---	Line Cook
20007	=	d3fcbf65-261c-4643-b126-3be47b18ef3e	#	Takeout BOH	---	Takeout Culinary
30001	=	97cd39d6-1493-4be7-a1dc-81656469e824	#	FOH Key	---	FOH Key Hourly Supervisor
30003	=	5047980d-6ee6-4115-ada2-8fb6ef1b1816	#	BOH Key	---	BOH Key Hourly Supervisor
40015	=	43e70337-6a26-48d7-ae0c-7841d50d75eb	#	MIT Hourly	---	MIT Hourly


[toast_fields]
email = personal
passcode = clockseq

[toast_manager_jobs]
ce2b21d5-dfc2-4b55-8bde-9cad8aba9d16 = GM
b4d6062b-81da-4c1a-ac11-86c2b75861e2 = AGM
f1643651-2152-4066-b1f4-b7c4b8b8f526 = RM
e324f64e-b89c-43ba-bb3b-811b2d3f9d75 = EC
9054ca88-0280-44b2-878d-b351bf53a63f = SC

[toast_administrative_jobs]
f075544d-bdce-4165-bc07-391e4e18d5cb = Report Access
