#!/bin/sh

#One other bit of information needed is the file naming convention for the Radar SFTP site.
#The naming convention ensures that the polling job that is initiated after the file is ingested applies the data to the correct location.
#The correct naming convention of Location ID followed by date e.g. 28_20221118. This would be the file for location 28 date 11/18/2022.
#Postino Park Place is location 28 in Radar, the file you send each day should be named accordingly

# HOUSEKEEPING
DOY=`date +%j`
RADAR_FID=`dotnet ./payroll/Payroll.Tool.dll date from 1 0`

# Export from 7shifts (21 day rolling window)
LOGFILE=../Logs/7shifts-$DOY.log dotnet ./7shifts/7shifts.Tool.dll export time 21 0 > ../Mailbox/7shifts/punches_$DOY.json

# Tranform for Radar
cat ../Mailbox/7shifts/punches_$DOY.json | dotnet ./radar/Radar.Tool.dll convert punches > ../Mailbox/radar/28_$RADAR_FID.csv

# production server
LFTP_PASSWORD='Lr}h8ARUuyEz,n^g' lftp --env-password sftp://<EMAIL> -e "put ../Mailbox/radar/28_$RADAR_FID.csv; exit"

# only keep X days worth of files
find /opt/openarc/Logs/ -type f -mtime +60 -delete
find /opt/openarc/Mailbox/ -type f -mtime +180 -delete
find /opt/openarc/Mailbox/cookies/ -type f -mtime +7 -delete
