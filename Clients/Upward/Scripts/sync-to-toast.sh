#!/bin/sh
DOM=`date +%d`
HOUR=`date +%H`
TODAY=`date +day%d-hr%H`

# Use tail to keep the last 5000 lines and overwrite the original file
tail -n 5000 ../Logs/Toast/terms-$DOM.log > ../Logs/Toast/terms-$DOM.log.tmp && mv ../Logs/Toast/terms-$DOM.log.tmp ../Logs/Toast/terms-$DOM.log
tail -n 5000 ../Logs/Toast/hires-$DOM.log > ../Logs/Toast/hires-$DOM.log.tmp && mv ../Logs/Toast/hires-$DOM.log.tmp ../Logs/Toast/hires-$DOM.log
tail -n 5000 ../Logs/Toast/sync-$DOM.log > ../Logs/Toast/sync-$DOM.log.tmp && mv ../Logs/Toast/sync-$DOM.log.tmp ../Logs/Toast/sync-$DOM.log

echo Updating MongoDB with Paylocity Data
LOGFILE=../Logs/Paylocity/payloc-$DOM-$HOUR.log dotnet paylocity/Paylocity.Tool.dll export employees > ../Mailbox/fulldump.json
cat ../Mailbox/fulldump.json | dotnet payroll/Payroll.Tool.dll cache import

echo Processing Terminations...
dotnet payroll/Payroll.Tool.dll cache dump term | LOGFILE=../Logs/Toast/terms-$DOM.log dotnet toast/Toast.Tool.dll import terms all doit > ../terms.log
cat ../terms.log | dotnet payroll/Payroll.Tool.dll mail results

echo Processing New Hires...
dotnet payroll/Payroll.Tool.dll cache dump active | LOGFILE=../Logs/Toast/hires-$DOM.log dotnet toast/Toast.Tool.dll import hires all doit > ../hires.log
cat ../hires.log | dotnet payroll/Payroll.Tool.dll mail results

echo Processing	Existing Employees...
dotnet payroll/Payroll.Tool.dll cache dump active | LOGFILE=../Logs/Toast/sync-$DOM.log dotnet toast/Toast.Tool.dll import sync all doit > ../sync.log
cat ../sync.log | dotnet payroll/Payroll.Tool.dll mail results

mv -f ../Mailbox/fulldump.json ../Logs/fulldump-$DOM.json
gzip ../Logs/fulldump-$DOM.json

