#!/bin/sh

DOM=`date +%d`
HOUR=`date +%H`
TODAY=`date +day%d-hr%H`

echo Updating MongoDB with Paylocity Data
LOGFILE=../Logs/Paylocity/payloc-$DOM-$HOUR.log dotnet paylocity/Paylocity.Tool.dll export employees > ../Mailbox/fulldump.json
cat ../Mailbox/fulldump.json | dotnet payroll/Payroll.Tool.dll import employees

echo Processing stores...
dotnet payroll/Payroll.Tool.dll export employees location 101 > ../Mailbox/arcadia.json
dotnet payroll/Payroll.Tool.dll export employees location 102 > ../Mailbox/central.json
dotnet payroll/Payroll.Tool.dll export employees location 108 > ../Mailbox/annex.json
dotnet payroll/Payroll.Tool.dll export employees location 109 > ../Mailbox/lohi.json

cat ../Mailbox/arcadia.json| LOGFILE=../Logs/Toast/toast-$DOM-arcadia.log dotnet toast/Toast.Tool.dll import employees doit
cat ../Mailbox/central.json| LOGFILE=../Logs/Toast/toast-$DOM-central.log dotnet toast/Toast.Tool.dll import employees doit
cat ../Mailbox/annex.json| LOGFILE=../Logs/Toast/toast-$DOM-annex.log dotnet toast/Toast.Tool.dll import employees doit
cat ../Mailbox/lohi.json| LOGFILE=../Logs/Toast/toast-$DOM-lohi.log dotnet toast/Toast.Tool.dll import employees doit
