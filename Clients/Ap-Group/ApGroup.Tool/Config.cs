﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Runtime.CompilerServices;
using Payroll.Shared;

namespace ApGroup.Tool
{
    public static class Config
    {
        public readonly static string SettingSection = "slim";
        private static readonly string LocationsToSkipSection = "slim_skip_store_numbers";
        public static readonly string POS_EMPLOYEES_CACHE_COLLECTION = "pos_employees";
        private static readonly HashSet<string> LocationsToSkip = new HashSet<string>();

        static Config()
        {
            Setting.Init();

            {
                var skiplist = Setting.ListSection(LocationsToSkipSection);
                LocationsToSkip = skiplist.Keys.ToHashSet();
            }
        }

        public static bool SkipLocation(string locationId)
        {
            if (string.IsNullOrEmpty(locationId)) return false;

            return LocationsToSkip.Contains(locationId);
        }

        public static ExecutionMode ExecutionMode()
        {
            var mode = Setting.Get(SettingSection, "mode");
            if (string.IsNullOrEmpty(mode)) return Payroll.Shared.ExecutionMode.DryRun;
            return mode == "execute" ? Payroll.Shared.ExecutionMode.Execute : Payroll.Shared.ExecutionMode.DryRun;
        }
    }
}
