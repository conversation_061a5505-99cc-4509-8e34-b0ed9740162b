#!/bin/sh

DOM=`date +%d`
dotnet paycom2/Paycom2.Tool.dll cache dump term 7 > ../Mailbox/terms.json
cat ../Mailbox/terms.json | LOGFILE=../Logs/Crunch/terms-$DOM.log dotnet crunch/CrunchTime.Tool.dll import terms all doit

dotnet paycom2/Paycom2.Tool.dll cache dump cseq | dotnet client/ApGroup.Tool.dll import employees > ../Mailbox/employees.json
cat ../Mailbox/employees.json | LOGFILE=../Logs/Crunch/hires-$DOM.log dotnet crunch/CrunchTime.Tool.dll import hires all doit
cat ../Mailbox/employees.json | LOGFILE=../Logs/Crunch/sync-$DOM.log dotnet crunch/CrunchTime.Tool.dll import sync all doit
