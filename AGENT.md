# AGENT.md - Payroll Tools Development Guide

## Build Commands
- `dotnet build` - Build solution
- `dotnet publish -c Release -f net8.0 --runtime linux-x64` - Publish for Linux
- `make clean` - Clean build artifacts (removes bin/, obj/, and ../Releases/*/)
- `make setup` - Generate BuildInfo.cs with git commit/branch info
- `make test` - Test build (builds Brink.Tool)
- Individual project build: `cd ProjectName.Tool && make linux8` or `make win8`

## Testing
- No unit tests - testing done via TestCommand classes in each Tool project
- Run tests: `dotnet run test <command>` (e.g., `dotnet run test employee 123`)
- Test commands include: employee, timezone, timezones, locations

## Code Style
- **Namespaces**: Follow project structure (e.g., `Brink.Tool`, `Payroll.Shared`)
- **Classes**: PascalCase, Commands end with "Command", Services end with "Service"
- **Methods**: PascalCase for public, camelCase for private
- **Variables**: camelCase for locals, PascalCase for properties, UPPER_CASE for constants
- **Using statements**: System first, then third-party, then project namespaces
- **Error handling**: Use `catch (Exception e)` pattern, log with `Log.Logger.Fatal/Error`
- **Logging**: Use Serilog throughout (`Log.Logger.Information/Debug/Error/Fatal`)
- **JSON**: Use System.Text.Json for serialization
- **No code comments** unless complex logic requires explanation
