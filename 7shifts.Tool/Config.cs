﻿using Payroll.Shared;

namespace _7shifts.Tool;

public static class Config
{
    public static readonly string ENV_KEY_ACCESS_TOKEN="7SHIFTS_ACCESS_TOKEN";
    public static readonly string ENV_KEY_COMPANY_ID="7SHIFTS_COMPANY_ID";
    public static readonly string SettingSection = "7shifts";
    public static readonly string LocationSection = "7shifts_locations";
    public readonly static string AccessToken;
    public readonly static string ApiUrl;
    public readonly static string CompanyId;

    static Config()
    {
        Setting.Init();

        ApiUrl = Setting.Get(SettingSection, "apiUrl");
        AccessToken = Setting.GetFromConfigOrEnv(SettingSection, "accessToken", ENV_KEY_ACCESS_TOKEN); 
        CompanyId = Setting.GetFromConfigOrEnv(SettingSection, "companyId", ENV_KEY_COMPANY_ID); 
    }

    public static List<string> LocationIds
    {
        get
        {
            var rtn = new List<string>();
            var list = Setting.ListSection(LocationSection);
            return list.Select(kvp => kvp.Key).ToList();
        }
    }
}
