using CommandLine;
using Payroll.Shared;
using Serilog;

namespace _7shifts.Tool;

public class Program : ProgramBase<SettingCommand>
{
    static readonly string AppVersion = "1.0";

    public override int ShowUsage()
    {
        Console.WriteLine("Usage: 7shifts.Tool.exe <command> <command-args>");
        Console.WriteLine("  where <command> is one of...");
        Console.WriteLine("   - export [punches][from:'02/06/2023' span:1]");
        return 0;
    }

    public void Help(List<string> args)
    {
        ShowUsage();
    }

    public void Info(List<string> args)
    {
        Payroll.Shared.Setting.Init();
        Console.WriteLine($"7shifts.Tool.exe version {AppVersion} ({Payroll.Shared.Setting.IniFilePath})");
        Console.WriteLine();
        WriteSettingsSection(Config.SettingSection);
        WriteSettingsSection(Config.LocationSection);
    }

    private void WriteSettingsSection(string section)
    {
        var list = Payroll.Shared.Setting.ListSection(section);
        Console.WriteLine();
        Console.WriteLine($"[{section}]");
        foreach (var e in list)
        {
            Console.WriteLine($"{e.Key}={e.Value}");
        }
    }

    private int ExecCommand<T>(List<string> args) where T : new()
    {
        var command = new CommandArguments(args);

        return Command<T>.Invoke(command);
    }

    public void Export(List<string> args)
    {
        ExecCommand<ExportCommand>(args);
    }

    static int Main(string[] args)
    {
        // setup logging services...
        var command = string.Join(" ", args);
        Logger.Setup($"7shifts.Tool.Exe - Command: '{command}', Version: {AppVersion}");

        // Log version information
        Log.Logger.Information($"7shifts.Tool.exe version {AppVersion} ({Payroll.Shared.Setting.IniFilePath})");
        Log.Logger.Information($"  Branch: {BuildInfo.Branch}, Built: {BuildInfo.Built}, Host: {BuildInfo.Host}, Hash: {BuildInfo.Commit}");

        try
        {
            Parser.Default.ParseArguments<ProgramArguments>(args)
                .MapResult((ProgramArguments opts) => ProgramDriver<Program>.Run(opts), errs => 1);
        }
        catch (Exception e)
        {
            Log.Logger.Error(e.Message);
            if (!string.IsNullOrEmpty(e.StackTrace)) Log.Logger.Error(e.StackTrace);
            return -1;
        }

        return 0;
    }
}