﻿using Serilog;
using System.Net.Http.Headers;
using System.Text;

namespace _7shifts.Tool;

public class _7shiftsService
{
    public async Task<string> GetAsync(string endpoint, Dictionary<string,string> paramMap)
    {
        using (var api = ApiClient())
        {
            var query = await BuildGetRequestQuery(endpoint, paramMap);
            try
            {
                var response = await api.GetAsync(query);
                return await response.Content.ReadAsStringAsync();
            }
            catch (Exception ex)
            {
                Log.Logger.Error(ex, "bad api call");
                return string.Empty;
            }
        }

    }

    private HttpClient ApiClient()
    {
        var token = new AuthenticationHeaderValue("Bearer", Config.AccessToken);

        var client = new HttpClient();
        client.DefaultRequestHeaders.Authorization = token;
        client.BaseAddress = new Uri(Config.ApiUrl);
        return client;
    }

    private async Task<string> BuildGetRequestQuery(string endpoint, Dictionary<string, string> paramMap)
    {
        var sb = new StringBuilder();
        sb.Append(Config.ApiUrl);
        sb.Append(endpoint);
        sb.Append("?");
        using(var encoded = new FormUrlEncodedContent(paramMap))
        {
            sb.Append(await encoded.ReadAsStringAsync());
        }
        var query = sb.ToString();
        Log.Logger.Debug(query);
        return query;
    }
}
