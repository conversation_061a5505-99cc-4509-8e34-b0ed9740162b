﻿using System.Diagnostics;
using MongoDB.Driver.Linq;
using Newtonsoft.Json.Linq;
using Payroll.Shared;
using Serilog;

namespace _7shifts.Tool;

public class ExportCommand
{
	private readonly _7shiftsService _service;

	public ExportCommand()
	{
		_service = new _7shiftsService();
	}

    public void Settings(List<string> args)
    {
        string? atoken = Environment.GetEnvironmentVariable(Config.ENV_KEY_ACCESS_TOKEN);
        Console.WriteLine($"export {Config.ENV_KEY_ACCESS_TOKEN}={atoken}");
        var companyId = Environment.GetEnvironmentVariable(variable: Config.ENV_KEY_COMPANY_ID);
        Console.WriteLine($"export {Config.ENV_KEY_COMPANY_ID}={companyId}");
    }

    public static bool ParseDateWindow(List<string> args, out DateTime startDate, out DateTime endDate)
    {
        // NOTE: you cannot use the ConsoleService.GetDateWindowFromArgs function with 7Shifts!
        startDate = DateTime.Now.AddDays(-1);
        endDate = DateTime.Now.AddDays(-1); // different for 7shifts

        if (args == null) return true;

        if (args.Count > 1)
        {
            int sinceWhenStartInDays = Convert.ToInt32(args[0]);
            int sinceWhenEndInDays = Convert.ToInt32(args[1]);

            startDate = DateTime.Now.AddDays(-sinceWhenStartInDays).Date;
            endDate = startDate.AddDays(sinceWhenStartInDays-sinceWhenEndInDays-1).Date; // different for 7shifts
        }
        else if (args.Count > 0)
        {
            int sinceWhenStartInDays = Convert.ToInt32(args[0]);
            startDate = DateTime.Now.AddDays(-sinceWhenStartInDays).Date;
            endDate = DateTime.Now.AddDays(-sinceWhenStartInDays).Date; // different for 7shifts
        }

        Log.Logger.Information("Date window of {start} to {end}", startDate, endDate);

        return true;
    }

    public void Punches(List<string> args)
    {
        var p = ParseArgs(args);

        var fromDate = DateTime.Today.AddDays(-1);
        if (p.ContainsKey("from"))
        {
            fromDate = DateTime.Parse(p["from"]);
        }

        var spanOfDays = 1;
        if (p.ContainsKey("span"))
        {
            spanOfDays = Convert.ToInt32(p["span"]);
        }

        var toDate = fromDate.AddDays(spanOfDays - 1);
        if (p.ContainsKey("to"))
        {
            toDate = DateTime.Parse(p["to"]);
        }

        Log.Logger.Information("Date window of {start} to {end}", fromDate, toDate);
        PrintHoursAndWagesBetween(fromDate, toDate);
    }
    public void Time(List<string> args)
    {
        ParseDateWindow(args, out DateTime fromDate, out DateTime toDate);
        PrintHoursAndWagesBetween(fromDate, toDate);
    }

	private void PrintHoursAndWagesBetween(DateTime fromDate, DateTime toDate)
	{
    	var paramMap = new Dictionary<string, string>();
		paramMap.Add("punches", "true");
		paramMap.Add("company_id", Config.CompanyId);
        paramMap.Add("from", fromDate.ToString("yyyy-MM-dd"));
        paramMap.Add("to", toDate.ToString("yyyy-MM-dd"));
        paramMap.Add("show_exception_costs", "true");
        foreach (var locationId in Config.LocationIds)
		{
			paramMap.Add("location_id", locationId);
		}

		var json = _service.GetAsync("reports/hours_and_wages", paramMap).Result;
		Log.Logger.Debug(json);

        // for upward projects, we need to know if 7shifts isn't giving us any data, so using this for now
        var punches = BuildPunchList(json);
        ConsoleService.PrintFormattedJson(punches);
        Environment.Exit(punches.Count);
    }

	private Dictionary<string, string> ParseArgs(List<string> args)
	{
		var rtn = new Dictionary<string, string>();
		foreach (var arg in args)
		{
			if (arg.Contains(":"))
			{
				var pair = arg.Split(':');
				rtn.Add(pair[0], pair[1]);
			}
		}
		return rtn;
	}

	private List<PunchPair> BuildPunchList(string json)
	{
		var rtn = new List<PunchPair>();
		var whw = JObject.Parse(json);
		JArray? users = whw["users"] as JArray;
		if (users == null)
		{
			Log.Logger.Warning("report retrieved successfully from 7shifts, but contains no users for the day.  No further processing will be performed.");
			return rtn;
		}

		foreach (var user in users)
		{
			var employeeId = user["user"]?["employee_id"]?.Value<string>() ?? string.Empty;
			var firstName = user["user"]?["first_name"]?.Value<string>() ?? string.Empty;
			var lastName = user["user"]?["last_name"]?.Value<string>() ?? string.Empty;

			//user has weeks[].. week has shifts[].. 
			JArray? weeks = user["weeks"] as JArray;
			if (weeks != null)
			{
				foreach (var week in weeks)
				{
					JArray? shifts = week["shifts"] as JArray;
					if (shifts != null)
					{
						foreach (var shift in shifts)
						{
							var locationId = shift["location_id"]?.Value<string>() ?? string.Empty;
							var role = shift["role_label"]?.Value<string>() ?? string.Empty;
							var wage = shift["wage"]?.Value<string>() ?? "0.00";
                            var exceptionPay = shift["total"]?["compliance_exceptions_pay"]?.Value<string>() ?? "0.00";
                            var totalPay = shift["total"]?["total_pay"]?.Value<string>() ?? "0.00";
                            var shiftStart = shift["date"]?.Value<DateTime>() ?? DateTime.MinValue;
							//var shiftTimeSpanLabel = shift["label"]?.Value<string>() ?? string.Empty;
							//var shiftTimeSplit = shiftTimeSpanLabel.Replace(" ", string.Empty).Split('-');
							//var shiftEndTime = shiftTimeSplit.Length > 1 ? shiftTimeSplit[1].Replace("AM", ":00AM").Replace("PM", ":00PM") : string.Empty;

							//var shiftDate = shiftStart.ToString("MM/dd/yyyy") ?? string.Empty;
							//var shiftStart = startDate.ToString("MM/dd/yyyy hh:mm:sstt") ?? string.Empty;
                            var shiftEnd = shiftStart.AddHours(shift["total"]?["total_hours"]?.Value<double>() ?? 0);
                            //var shiftEnd = $"{shiftDate} {shiftEndTime}".TrimEnd();

                            var punch = new PunchPair
                            {
                                EECode = employeeId,
                                FirstName = firstName,
                                LastName = lastName,
                                Location = locationId,
                                Date = shiftStart.Date,
                                TimeIn = shiftStart,
                                TimeOut = shiftEnd,
                                JobCode = role,
                                Rate = Decimal.Parse(wage),
                                TotalPay = Decimal.Parse(totalPay),
                               	ExceptionPay = Decimal.Parse(s: exceptionPay)
                        	};

                            rtn.Add(punch);
						}
					}
				}
			}
		}

		return rtn;
	}
}
