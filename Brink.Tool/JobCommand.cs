﻿using System;
using System.Collections.Generic;
using System.Linq;
using Newtonsoft.Json;
using Brink.Api.Settings2;
using Payroll.Shared;
using Brink.Shared;

namespace Brink.Tool
{
    class JobCommand
    {
        public void List(List<string> args)
        {
            var managerJobsById = BrinkConfig.ManagerJobIds();
            var locations = Setting.ListSection(BrinkConfig.LocationSection);

            foreach (var location in locations)
            {
                var service = new BrinkService(location.Key);
                var jobs = service.AllBrinkJobs();

                Console.WriteLine("Loc \tExport Code    \tName");
                var sortedJobs = jobs.Values.OrderBy(x => x.Name);
                
                foreach (var job in sortedJobs)
                {
                    var exportCode = job.ExportCode ?? "";

                    var title = job.Name;
                    if (managerJobsById.Contains(job.ExportCode)) title += "*";

                    Console.WriteLine($"{location.Key}\t{exportCode.PadRight(15)}\t{title}");
                }
            }
        }

        public void Exceptions(List<string> args)
        {
            var locations = Setting.ListSection(BrinkConfig.LocationSection);
            Console.WriteLine($"Job Exception Report, Generated {DateTime.Now}");
            Console.WriteLine();

            foreach (var location in locations)
            {
                Console.WriteLine($"Location {location.Key}");
                Console.WriteLine("--------------------------------------------------");

                var service = new BrinkService(location.Key);
                var jobs = service.AllBrinkJobs();
                foreach (var job in jobs.Values)
                {
                    if (!string.IsNullOrEmpty(job.ExportCode)) continue;
                    Console.WriteLine($"{job.Id}\t{job.Name}");
                }

                Console.WriteLine();
            }
        }
    }
}
