﻿using System;
using System.Collections;
using System.Collections.Generic;
using System.Linq;
using Newtonsoft.Json;
using Payroll.Shared;
using Brink.Shared;
using Serilog;

namespace Brink.Tool
{
    class ImportCommand
    {
        //readonly string ConfigLocation = BrinkConfig.LocationCode();
        readonly Random randomNumberGenerator = new Random();

        private delegate bool SyncOperation(BrinkService service, string location, ref Api.Settings2.Employee brinkEmployee, Employee employee);

        private void EmployeeSyncIteration(ICollection args, SyncOperation processingFunc)
        {
            /*
            try
            {
                Log.Logger.Debug("Parsing json from <stdin>");
                if (!ConsoleService.TryGetChangesFromInput(out List<EmployeeChange> changes))
                {
                    Log.Logger.Error("Failed to parse changes list");
                    return;
                }

                Log.Logger.Debug("Processing {0} changes", changes.Count);
                var betaMode = BrinkConfig.BetaMode();
                var empMap = new EmployeesByLocation();
                var employeesInJson = changes.Select(x => x.Employee);

                // we map employees by every available location identifier for changes
                empMap.LoadByHomeLocation(employeesInJson);
                empMap.LoadByJobLocation(employeesInJson);

                foreach (var location in empMap.Keys)
                {
                    var employees = empMap[location];
                    var locationToken = BrinkConfig.TokenForLocation(location);

                    // should we process this location or skip it?
                    if (!BrinkConfig.IsLocationEnabled(location))
                    {
                        Log.Logger.Debug("Skipping location {0}", location);
                        continue;
                    }

                    // pull brink records and map by external keys
                    var service = new BrinkService(location);
                    service.InitializeDirectories();

                    bool warnOnSkips = false;
                    var brinkEmployees = service.SyncableBrinkEmployees(warnOnSkips).Values.ToDictionary(x => x.PayrollId);
                    Log.Logger.Debug("Processing location={location}, token={locationToken}, employee count {count}",
                        location, locationToken, brinkEmployees.Count);

                    var processedEmployees = new List<Api.Settings2.Employee>();

                    foreach (var kvp in employees)
                    {
                        var employee = kvp.Value;
                        Log.Logger.Debug($"Processing employee {kvp.Key}: {employee.FirstName} {employee.LastName}");

                        if (!service.IsEmployeeSyncable(employee))
                        {
                            Log.Logger.Debug($"-> Employee {kvp.Key} is not syncable.");
                            continue;
                        }

                        var fnd = brinkEmployees.TryGetValue(employee.Id, out Api.Settings2.Employee brinkEmployee);
                        if (!fnd) brinkEmployee = null;

                        if (processingFunc(service, location, ref brinkEmployee, employee))
                            processedEmployees.Add(brinkEmployee);
                    }

                    var dryRun = (args == null || args.Count == 0);
                    UpdateRecordsInBrink(service, location, dryRun, processedEmployees);
                }
            }
            catch (Exception e)
            {
                Log.Logger.Fatal(e.Message);
                Log.Logger.Fatal(e.StackTrace);
            }
            */
        }

        public void Changes(List<string> args)
        {
            Console.WriteLine("WARNING: NON-OPERATIONAL METHOD");
            bool SyncOperation(BrinkService service, string location, ref Api.Settings2.Employee brinkEmployee, Employee employee)
            {
                if (brinkEmployee != null) // must be present record to be updated
                {
                    Log.Logger.Information($"Updating existing employee {employee.Id}/{employee.ClockSeq} - {employee.FirstName} {employee.LastName} - {employee.HireDate}");
                    service.UpdateBrinkEmployeeRecord(brinkEmployee, employee);
                    return true;
                }

                return false;
            }

            //EmployeeSyncIteration(args, SyncOperation);
        }

        public void Jobs(List<string> args)
        {
            Console.WriteLine("WARNING: NON-OPERATIONAL METHOD");
            bool SyncOperation(BrinkService service, string location, ref Api.Settings2.Employee brinkEmployee, Employee employee)
            {
                if (brinkEmployee != null) // must be present record to be updated
                {
                    Log.Logger.Information($"Updating existing employee {employee.Id}/{employee.ClockSeq} - {employee.FirstName} {employee.LastName} - {employee.HireDate}");
                    service.UpdateBrinkEmployeeJobs(brinkEmployee, employee);
                    return true;
                }

                return false;
            };

            //EmployeeSyncIteration(args, SyncOperation);
        }

        public void Hires(List<string> args)
        {
            Console.WriteLine("WARNING: NON-OPERATIONAL METHOD");
            bool SyncOperation(BrinkService service, string location, ref Api.Settings2.Employee brinkEmployee, Employee employee)
            {
                // must NOT be present record to be added
                if (brinkEmployee != null)
                {
                    Log.Logger.Debug("Skipping {employeeId} as brinkEmployee {bid} exists", employee.Id, brinkEmployee.Id);
                    return false;
                }

                // don't add employees who are inactive...
                if (!employee.Active)
                {
                    Log.Logger.Information("Skipping inactive employee {employeeId}/{employeeClockSeq} - {employeeFirstName} {employeeLastName}",
                        employee.Id, employee.ClockSeq, employee.FirstName, employee.LastName);
                    return false;
                }

                Log.Logger.Information("Adding new employee {employeeId}/{employeeClockSeq} - {employeeFirstName} {employeeLastName} to {loc}",
                    employee.Id, employee.ClockSeq, employee.FirstName, employee.LastName, location);

                brinkEmployee = CreateNewBrinkEmployee(employee);

                // finally set home location if needed (only doing this for new employees)
                if (employee.PrimaryWorkLocation == location)
                {
                    var homeLocationId = BrinkConfig.GuidForLocation(employee.PrimaryWorkLocation);
                    Log.Logger.Information("  Setting {field} = {loc} ({homeLocationId})",
                        "Home Location", employee.PrimaryWorkLocation, homeLocationId);
                    brinkEmployee.HomeLocationId = homeLocationId;
                }

                service.UpdateBrinkEmployeeRecord(brinkEmployee, employee);
                SetEmployeePermissions(brinkEmployee, employee);

                if (employee.Jobs != null && employee.Jobs.Count > 0)
                    Log.Logger.Debug($"  {employee.Jobs[0].Name} {employee.Jobs[0].Code} {employee.Jobs[0].Rate}");

                return true;
            };

            //EmployeeSyncIteration(args, SyncOperation);
        }

        private void UpdateRecordsInBrink(BrinkService service, string location, bool dryRun,
            List<Api.Settings2.Employee> employees)
        {
            if (employees.Count == 0)
            {
                Log.Logger.Debug("  no syncable employee records for {location}", location);
                return;
            }

            if (dryRun)
            {
                Log.Logger.Information("Dry Run Mode: would have synced {records} employee records at {location}",
                    employees.Count, location);
            }
            else
            {
                Log.Logger.Information("Execution Mode: updating {hires} employee records at {location}",
                    employees.Count, location);
                service.UpdateBrinkEmployees(employees);
            }
        }

        private Api.Settings2.Employee CreateNewBrinkEmployee(Payroll.Shared.Employee employee)
        {
            var brinkEmployee = new Api.Settings2.Employee
            {
                // The Brink docs say:
                // When adding a new employee, specify a unique negative number for the employee’s Id.
                // This will be used to reference that employee for errors and when returning
                // the system assigned id for the new employee.
                Id = -randomNumberGenerator.Next(),
                PayrollId = employee.Id
            };

            // Pin and payroll id setup for new people
            // Brink pin entry length is configurable
            // So take the last X digits, if the clockseq is longer than that
            if (!string.IsNullOrEmpty(employee.ClockSeq))
            {
                var plen = BrinkConfig.PinLength();
                brinkEmployee.Pin = employee.ClockSeq.Tail(plen);
                Log.Logger.Information("  Setting {pin} = {pinval} (pl={pl})", "Pin", brinkEmployee.Pin, plen);
            }

            brinkEmployee.CanLoginWithPin = true;
            return brinkEmployee;
        }

        public Api.Settings2.Employee SetEmployeePermissions(Api.Settings2.Employee brinkEmployee, Payroll.Shared.Employee employee)
        {
            // add permission settings
            // Id Name
            // 1  Manager Functions
            // 2  Open Cash Drawer
            JobLevel level = JobLevel.None;
            foreach (var job in employee.Jobs)
            {
                if (job.JobLevel > level)
                    level = job.JobLevel;
            }

            // initialize array if needed
            if (brinkEmployee.Permissions == null)
                brinkEmployee.Permissions = new List<Api.Settings2.EmployeePermission>();

            if (level == JobLevel.Manager)
            {
                brinkEmployee.Permissions.Add(new Api.Settings2.EmployeePermission()
                {
                    Id = -randomNumberGenerator.Next(),
                    PermissionId = 1 // Manager Functions
                });
            }
            else if (level == JobLevel.Cashier) // they cannot be both & manager takes priority
            {
                brinkEmployee.Permissions.Add(new Api.Settings2.EmployeePermission()
                {
                    Id = -randomNumberGenerator.Next(),
                    PermissionId = 2 // Open Cash Drawer
                });
            }

            return brinkEmployee;
        }
    }
}
