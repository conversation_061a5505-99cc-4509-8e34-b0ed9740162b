using System;
using System.Collections.Generic;
using System.Globalization;
using System.IO;
using System.Reflection;
using Serilog;
using Newtonsoft.Json;
using Brink.Shared;
using CommandLine;
using Payroll.Shared;

namespace Brink.Tool
{
    [AttributeUsage(AttributeTargets.Assembly)]
    internal class BuildDateAttribute : Attribute
    {
        public BuildDateAttribute(string value)
        {
            DateTime = DateTime.ParseExact(value, "yyyyMMddHHmmss", CultureInfo.CurrentCulture, DateTimeStyles.None);
        }

        public DateTime DateTime { get; }
    }

    class Program : ProgramBase<SettingCommand>
    {
        private const string AppVersion = "1.2.5";

        private static DateTime GetBuildDate()
        {
            var assembly = Assembly.GetExecutingAssembly();
            var attribute = assembly.GetCustomAttribute<BuildDateAttribute>();
            return attribute?.DateTime ?? default(DateTime);
        }

        static void DoShowUsage()
        {
            Console.WriteLine("Usage: Brink.Tool.exe <command> <command-args>");
            Console.WriteLine("  where <command> is one of...");
            Console.WriteLine("   - employee [list, view, exceptions, connections] = employee management");
            Console.WriteLine("   - info - show version and tool information");
            Console.WriteLine("   - job [list, exceptions] = job management");
            Console.WriteLine("   - test [timezone, timezones, levels, permissions] = misc management");
            Console.WriteLine("   - export [time] = export utilities");
            Console.WriteLine("   - import [hires, changes, jobs, employees] = import utilities");
            Console.WriteLine("   - restaurant [list, employees, terms] = restaurant management");
            Console.WriteLine("   - setting = settings management");

            Console.WriteLine();
            Console.WriteLine($"  Beta Mode Enabled = {BrinkConfig.BetaMode()}");
            Console.WriteLine($"  Pin Length = {BrinkConfig.PinLength()}");
            var excludePunches = BrinkConfig.ExcludeManagerPunches() ? "exclude" : "include";
            Console.WriteLine($"  Manager Punches = {excludePunches}");
        }

        public override int ShowUsage()
        {
            DoShowUsage();
            return 0;
        }

        public void Help(List<string> args)
        {
            ShowUsage();
        }

        public void Info(List<string> args)
        {
            try
            {
                Console.WriteLine($"Brink.Tool.exe - Version: {AppVersion}, Built: {GetBuildDate()}");
                Console.WriteLine($"  Branch: {BuildInfo.Branch}, Built: {BuildInfo.Built}, Host: {BuildInfo.Host}, Hash: {BuildInfo.Commit}");
                Console.WriteLine($"  Settings Path: {Payroll.Shared.Setting.IniFilePath}");

                Console.WriteLine($"  Repair Mode: {BrinkConfig.RepairMode()}");
                Console.WriteLine($"  Beta Mode: {BrinkConfig.BetaMode()}");

                var excludePunches = BrinkConfig.ExcludeManagerPunches() ? "exclude" : "include";
                Console.WriteLine($"  Manager Punches = {excludePunches}");
                Console.WriteLine();

                var addr = BrinkConfig.Host();
                if (string.IsNullOrEmpty(addr))
                {
                    Console.WriteLine("  Warning: BRINK_HOST not set. Example below.");
                    addr = "api2.brinkpos.net";
                }

                Console.WriteLine("  BRINK_HOST=" + addr);

                var atoken = BrinkConfig.AccessToken();
                if (string.IsNullOrEmpty(atoken))
                {
                    Console.WriteLine("  Warning: BRINK_ACCESS_TOKEN not set. Example below.");
                    atoken = "A7/3YwnYgk+B80JH/FZF5"; // not a real token
                }

                Console.WriteLine("  BRINK_ACCESS_TOKEN=" + atoken);

                var locCode = BrinkConfig.LocationCodeFromEnv();
                if (string.IsNullOrEmpty(locCode))
                {
                    Console.WriteLine("  Warning: BRINK_LOCATION_CODE not set. Example below.");
                    locCode = "'MM1'";
                }

                Console.WriteLine("  BRINK_LOCATION_CODE=" + locCode);

                if (!string.IsNullOrEmpty(locCode))
                {
                    var token = BrinkConfig.TokenForLocation(locCode);
                    Console.WriteLine("  BRINK_LOCATION_TOKEN=" + token);
                }
            }
            catch (Exception e)
            {
                Console.WriteLine(e.Message);
            }
        }

        private int ExecCommand<T>(List<string> args) where T : new()
        {
            var command = new CommandArguments(args);

            return Command<T>.Invoke(command);
        }

        public void Test(List<string> args)
        {
            ExecCommand<TestCommand>(args);
        }

        public void Employee(List<string> args)
        {
            ExecCommand<EmployeeCommand>(args);
        }

        public void Job(List<string> args)
        {
            ExecCommand<JobCommand>(args);
        }

        public void Export(List<string> args)
        {
            ExecCommand<ExportCommand>(args);
        }

        public void Import(List<string> args)
        {
            ExecCommand<ImportCommand>(args);
        }

        public void Restaurant(List<string> args)
        {
            ExecCommand<RestaurantCommand>(args);
        }

        static int Main(string[] args)
        {
            if (args.Length == 0)
            {
                DoShowUsage();
                return -1;
            }

            // setup logging services...
            string command = String.Join(" ", args);
            Payroll.Shared.Logger.Setup($"Brink.Tool.Exe - Command: '{command}', Version: {AppVersion}");
            
            // Log version information
            Log.Logger.Information($"Brink.Tool.exe - Version: {AppVersion}, Built: {GetBuildDate()}");
            Log.Logger.Information($"  Branch: {BuildInfo.Branch}, Built: {BuildInfo.Built}, Host: {BuildInfo.Host}, Hash: {BuildInfo.Commit}");

            try
            {
                Parser.Default.ParseArguments<ProgramArguments>(args)
                    .MapResult((ProgramArguments opts) => ProgramDriver<Program>.Run(opts), errs => 1);
            }
            catch (Exception e)
            {
                Console.WriteLine(e.Message);
                Console.WriteLine(e.StackTrace);
                return -1;
            }

            return 0;
        }
    }
}
