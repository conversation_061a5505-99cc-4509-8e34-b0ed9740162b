﻿using System;
using System.Collections.Generic;
using System.Linq;
using Newtonsoft.Json;
using Payroll.Shared;
using TimeZoneConverter;
using Serilog.Core;
using Serilog;
using Brink.Api.Settings2;
using Brink.Shared;

namespace Brink.Tool
{
    class TestCommand
    {
        public void Timezone(List<string> args)
        {
            var service = new BrinkService();
            Log.Logger.Information($"  Timezone: {service.IanaNameForLocation()}");

            if (args != null && args.Count > 0)
            {
                Console.WriteLine(TZConvert.WindowsToIana(args[0]));
            }
        }

        public void Timezones(List<string> args)
        {
            foreach (TimeZoneInfo z in TimeZoneInfo.GetSystemTimeZones())
            {
                string tz = TZConvert.IanaToWindows(z.Id);
                Console.WriteLine($"{z.Id}\t{z.StandardName}\t{tz}");
            }
        }

        public void Employee(List<string> args)
        {
            if (args == null || args.Count < 1)
            {
                Console.WriteLine("Usage: Brink.Tool.exe test employee <id>");
                return;
            }

            var id = Convert.ToInt32(args[0]);
            var service = new BrinkService();
            var employees = service.EmployeesTakeWhile(x => x.Id == id);

            foreach (var employee in employees)
            {
                Console.WriteLine(JsonConvert.SerializeObject(employee, Formatting.Indented));
            }
        }

        public void Levels(List<string> args)
        {
            var service = new BrinkService();
            var levels = service.AllLevels();

            Console.WriteLine("Id\tName          \tManageCashDrawers");
            foreach (var level in levels)
            {
                var lname = level.Name ?? " ";
                Console.WriteLine($"{level.Id}\t{lname.PadRight(15)}\t{level.ManageCashDrawers}");
            }
        }

        public void Permissions(List<string> args)
        {
            var service = new BrinkService();
            var permissions = service.AllPermissions();

            Console.WriteLine("Id\tActive\tName");
            foreach (var permission in permissions)
            {
                Console.WriteLine($"{permission.Id}\t{permission.IsActive}\t{permission.Name}");
            }
        }
    }
}
