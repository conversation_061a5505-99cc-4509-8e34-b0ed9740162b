﻿using System;
using System.Collections.Generic;
using System.Linq;
using Newtonsoft.Json;
using Payroll.Shared;
using Brink.Shared;

namespace Brink.Tool
{
    class EmployeeCommand
    {
        public void List(List<string> args)
        {
            var service = new BrinkService();
            var employees = service.AllActiveEmployees();

            Console.WriteLine("Id\tPayrollId       \tName");
            foreach (var employee in employees)
            {
                var name = $"{employee.FirstName} {employee.LastName}";
                Console.WriteLine($"{employee.PrimaryWorkLocation}\t{employee.Id}\t{employee.ClockSeq?.PadRight(15)}\t{name.PadRight(18)}\t{employee.PrimaryWorkLocation}");
            }
        }

        public void Exceptions(List<string> args)
        {
            var locations = Setting.ListSection(BrinkConfig.LocationSection);
            Console.WriteLine($"Exception Report, Generated {DateTime.Now}");
            Console.WriteLine();

            foreach (var location in locations)
            {
                Console.WriteLine($"Location {location.Key}");
                Console.WriteLine("--------------------------------------------------");

                var service = new BrinkService(location.Key);
                var employees = service.AllActiveEmployees(); ;
                foreach (var employee in employees)
                {
                    if (!string.IsNullOrEmpty(employee.ClockSeq)) continue;
                    Console.WriteLine($"{employee.Id}\t{employee.FirstName} {employee.LastName}");
                }

                Console.WriteLine();
            }
        }

        public void Connections(List<string> args)
        {
            var service = new BrinkService();
            var employees = service.SyncableEmployees();

            Console.WriteLine("Id\tClockSeq       \tName");
            foreach (var employee in employees)
            {
                Console.WriteLine($"{employee.Id}\t{employee.ClockSeq?.PadRight(15)}\t{employee.FirstName} {employee.LastName}");
            }
        }

        public void View(List<string> args)
        {
            if (args == null || args.Count < 1)
            {
                Console.WriteLine("Usage: Brink.Tool.exe employee view <payroll-id>");
                return;
            }

            var clockSeq = args[0];
            var service = new BrinkService();
            var employees = service.SyncableEmployees();

            foreach (var employee in employees)
            {
                if (employee.ClockSeq == clockSeq)
                {
                    Console.WriteLine(JsonConvert.SerializeObject(employee, Formatting.Indented));
                    return;
                }
            }
        }
    }
}
