﻿using Newtonsoft.Json;
using Payroll.Shared;
using Serilog;
using System;
using System.Collections.Generic;
using System.Linq;
using Brink.Shared;

namespace Brink.Tool
{
    class RestaurantCommand
    {
        public void List(List<string> args)
        {
            var restaurants = BrinkConfig.RestaurantsByGuid();
            foreach (var restaurant in restaurants)
            {
                var status = BrinkConfig.IsLocationEnabled(restaurant.Value) ? "Enabled" : "";
                Console.WriteLine($"{restaurant.Value}\t{status}");
            }
        }

        public void Jobs(List<string> args)
        {
            if (args == null || args.Count < 1)
            {
                Console.WriteLine("Usage: Brink.Tool.exe restaurant jobs <restaurant-code>");
                return;
            }

            var service = new BrinkService(args[0]);
            var jobs = service.AllBrinkJobs();

            Console.WriteLine("Loc \tExport Code    \tName");
            var sortedJobs = jobs.Values.OrderBy(x => x.Name);

            foreach (var job in sortedJobs)
            {
                var exportCode = job.ExportCode ?? "";
                var title = job.Name;
                Console.WriteLine($"{job.Id}\t{exportCode.PadRight(15)}\t{title}");
            }
        }

        public void Employees(List<string> args)
        {
            if (args == null || args.Count < 1)
            {
                Console.WriteLine("Usage: Brink.Tool.exe restaurant employees <restaurant-code>");
                return;
            }

            try
            {
                var service = new BrinkService(args[0]);
                var employees = service.AllActiveEmployees();
                var guidMap = BrinkConfig.RestaurantsByGuid();

                Console.WriteLine("Id\tPayrollId       \tName");
                foreach (var employee in employees)
                {
                    var name = $"{employee.FirstName} {employee.LastName}";
                    var location = string.IsNullOrEmpty(employee.PrimaryWorkLocation) ? "" : guidMap.GetValueOrDefault(employee.PrimaryWorkLocation);
                    Console.WriteLine($"{location}\t{employee.Id}\t{employee.ClockSeq?.PadRight(15)}\t{name?.PadRight(18)}");
                }
            }
            catch (Exception e)
            {
                Log.Logger.Error(e.Message);
                Log.Logger.Error(e.StackTrace);
            }
        }

        public void Terms(List<string> args)
        {
            if (args == null || args.Count < 1)
            {
                Console.WriteLine("Usage: Brink.Tool.exe restaurant terms <restaurant-code>");
                return;
            }

            try
            {
                var service = new BrinkService(args[0]);
                var employees = service.AllTerminatedEmployees();
                var guidMap = BrinkConfig.RestaurantsByGuid();

                Console.WriteLine("Id\tPayrollId       \tName");
                foreach (var employee in employees)
                {
                    var name = $"{employee.FirstName} {employee.LastName}";
                    var location = string.IsNullOrEmpty(employee.PrimaryWorkLocation) ? "" : guidMap.GetValueOrDefault(employee.PrimaryWorkLocation);
                    Console.WriteLine($"{location}\t{employee.Id}\t{employee.ClockSeq?.PadRight(15)}\t{name.PadRight(18)}");
                }
            }
            catch (Exception e)
            {
                Log.Logger.Error(e.Message);
                Log.Logger.Error(e.StackTrace);
            }
        }
    }
}
