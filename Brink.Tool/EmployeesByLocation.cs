﻿using System;
using System.Collections.Generic;
using System.Text;
using Serilog;

namespace Brink.Tool
{
    class EmployeesByLocation : SortedDictionary<string, SortedDictionary<string, Payroll.Shared.Employee>>
    {
        public int LoadByJobLocation(IEnumerable<Payroll.Shared.Employee> employees)
        {
            int cnt = 0;

            foreach (var employee in employees)
            {
                Log.Logger.Debug("LBJ: Processing employee {eid} @ location {lid}", employee.Id, employee.PrimaryWorkLocation);
                SortedDictionary<string, Payroll.Shared.Employee> empDict;

                foreach (var job in employee.Jobs)
                {
                    if (string.IsNullOrEmpty(job.Location)) continue;

                    // does location map employee dictionary already exist?
                    if (TryGetValue(job.Location, out empDict))
                    {
                        // have we already added this employee? if so, skip...
                        if (!empDict.ContainsKey(employee.Id))
                        {
                            empDict.Add(employee.Id, employee);
                            cnt++;
                        }
                    }
                    else
                    {
                        empDict = new SortedDictionary<string, Payroll.Shared.Employee>();
                        empDict.Add(employee.Id, employee);
                        Add(job.Location, empDict);
                        cnt++;
                    }
                }
            }

            return cnt;
        }

        public int LoadByHomeLocation(IEnumerable<Payroll.Shared.Employee> employees)
        {
            int cnt = 0;

            foreach (var employee in employees)
            {
                Log.Logger.Debug("LBH: Processing employee {eid} @ location {lid}", employee.Id, employee.PrimaryWorkLocation);
                SortedDictionary<string, Payroll.Shared.Employee> empDict;

                if (TryGetValue(employee.PrimaryWorkLocation, out empDict))
                {
                    // have we already added this employee? if so, skip...
                    if (!empDict.ContainsKey(employee.Id))
                    {
                        empDict.Add(employee.Id, employee);
                        cnt++;
                    }
                }
                else
                {
                    empDict = new SortedDictionary<string, Payroll.Shared.Employee>();
                    empDict.Add(employee.Id, employee);
                    Add(employee.PrimaryWorkLocation, empDict);
                    cnt++;
                }
            }

            return cnt;
        }
    };
}
