﻿using System;
using System.Collections.Generic;
using System.Linq;
using Newtonsoft.Json;
using Payroll.Shared;
using Brink.Shared;
using Serilog;

namespace Brink.Tool
{
    class ExportCommand
    {
        public void Time(List<string> args)
        {
            if (args == null || args.Count < 2)
            {
                Console.WriteLine("Usage: Brink.Tool.exe export time <start-day> <end-day>");
                Console.WriteLine("  where: <start-day> and <end-day> are integers representing how many days prior to today");
                Console.WriteLine("  e.g. 'Brink.Tool.exe export time 7 1' would export time from 7 days ago till yesterday.");
                return;
            }

            try
            {
                var start_days_back = Convert.ToInt32(args[0]);
                var end_days_back = Convert.ToInt32(args[1]);

                // initialize directories to reduce API calls
                var service = new BrinkService();
                service.InitializeDirectories();

                var punches = new List<PunchPair>();
                for (var x = start_days_back; x >= end_days_back; x--)
                {
                    var businessDate = DateTime.UtcNow.Date.AddDays(-x);
                    var punchesForDay = service.TimePunches(businessDate);
                    punches.AddRange(punchesForDay);
                }

                var json = JsonConvert.SerializeObject(punches, Formatting.Indented);
                Console.WriteLine(json);
            }
            catch (Exception e)
            {
                Log.Logger.Fatal(e.Message);
                Log.Logger.Fatal(e.StackTrace);
            }
        }

        public void Time2(List<string> args)
        {
            if (args == null || args.Count < 3)
            {
                Console.WriteLine("Usage: Brink.Tool.exe export time2 <location> <start-day> <end-day>");
                Console.WriteLine("  where:");
                Console.WriteLine("    <location> is a location code, like mm4");
                Console.WriteLine("    <start-day> and <end-day> are integers representing how many days prior to today");
                Console.WriteLine("  e.g. 'Brink.Tool.exe export time LM5 7 1' would export time from location 'LM5', 7 days ago till yesterday.");
                return;
            }

            try
            {
                var location = args[0];
                var start_days_back = Convert.ToInt32(args[1]);
                var end_days_back = Convert.ToInt32(args[2]);

                // initialize directories to reduce API calls
                var service = new BrinkService();
                service.InitializeDirectories();

                var punches = new List<PunchPair>();
                for (var x = start_days_back; x >= end_days_back; x--)
                {
                    var businessDate = DateTime.UtcNow.Date.AddDays(-x);
                    var punchesForDay = service.TimePunches(businessDate);
                    punches.AddRange(punchesForDay);
                }

                var json = JsonConvert.SerializeObject(punches, Formatting.Indented);
                Console.WriteLine(json);
            }
            catch (Exception e)
            {
                Log.Logger.Fatal(e.Message);
                Log.Logger.Fatal(e.StackTrace);
            }
        }
    }
}
