﻿using Newtonsoft.Json;
using System;
using System.Collections.Generic;

namespace Paylocity.Tool.Domain;

public class Employee
{
    public Employee()
    {
        BirthDate = DateTime.MinValue;
        EmployeeId = string.Empty;
        CoEmpCode = string.Empty;
        Ethnicity = string.Empty;
        FirstName = string.Empty;
        LastName = string.Empty;
        MaritalStatus = string.Empty;
        MiddleName = string.Empty;
        PreferredName = string.Empty;
        Gender = string.Empty;
        Ssn = string.Empty;
        Currency = string.Empty;
        TaxSetup = new TaxSetup();
        BenefitSetup = new BenefitSetup();
        DepartmentPosition = new DepartmentPosition();
        FederalTax = new FederalTax();
        PrimaryPayRate = new PrimaryPayRate();
        PrimaryStateTax = new PrimaryStateTax();
        Status = new Status();
        WorkEligibility = new WorkEligibility();
        WorkAddress = new WorkAddress();
        HomeAddress = new HomeAddress();
        WebTime = new WebTime();
        CompanyName = string.Empty;
        CompanyFEIN = string.Empty;
        EmergencyContacts = new List<EmergencyContact>();
        AdditionalRate = new List<AdditionalRate>();
    }

    public DateTime BirthDate { get; set; }
    public string EmployeeId { get; set; }
    public string CoEmpCode { get; set; }
    public string Ethnicity { get; set; }
    public string FirstName { get; set; }
    public string LastName { get; set; }
    public string MaritalStatus { get; set; }
    public string MiddleName { get; set; }
    public string PreferredName { get; set; }
    public string Gender { get; set; }
    public string Ssn { get; set; }
    public string Currency { get; set; }
    public TaxSetup TaxSetup { get; set; }
    public BenefitSetup BenefitSetup { get; set; }
    public DepartmentPosition DepartmentPosition { get; set; }
    public FederalTax FederalTax { get; set; }
    public PrimaryPayRate PrimaryPayRate { get; set; }
    public PrimaryStateTax PrimaryStateTax { get; set; }
    public Status Status { get; set; }
    public WorkEligibility WorkEligibility { get; set; }
    public WorkAddress WorkAddress { get; set; }
    public HomeAddress HomeAddress { get; set; }
    public WebTime WebTime { get; set; }
    public string CompanyName { get; set; }
    public string CompanyFEIN { get; set; }
    public List<EmergencyContact> EmergencyContacts { get; set; }
    
    public List<AdditionalRate> AdditionalRate { get; set; }
}

public class AdditionalRate
{
    public string ChangeReason { get; set; }
    public string CostCenter1 { get; set; }
    public string CostCenter2 { get; set; }
    public string CostCenter3 { get; set; }
    public DateTime EffectiveDate { get; set; }
    public string EndCheckDate { get; set; }
    public string Job { get; set; }
    public string Rate { get; set; }
    public string RateCode { get; set; }
    public string RateNotes { get; set; }
    public string RatePer { get; set; }
    public string Shift { get; set; }
}

public class TaxSetup
{
    public TaxSetup()
    {
        SuiState = string.Empty;
        TaxForm = string.Empty;
    }

    public string SuiState { get; set; }
    public string TaxForm { get; set; }
}

public class BenefitSetup
{
    public BenefitSetup()
    {
        BenefitClass = string.Empty;
        BenefitClassEffectiveDate = DateTime.MinValue;
        BenefitSalary = 0.0f;
        DoNotApplyAdministrativePeriod = false;
        IsMeasureAcaEligibility = false;
    }

    public string BenefitClass { get; set; }
    public DateTime BenefitClassEffectiveDate { get; set; }
    public float BenefitSalary { get; set; }
    public bool DoNotApplyAdministrativePeriod { get; set; }
    public bool IsMeasureAcaEligibility { get; set; }
}

public class DepartmentPosition
{
    public DepartmentPosition()
    {
        ChangeReason = string.Empty;
        CostCenter1 = string.Empty;
        CostCenter2 = string.Empty;
        CostCenter3 = string.Empty;
        EffectiveDate = DateTime.MinValue;
        EmployeeType = string.Empty;
        EqualEmploymentOpportunityClass = string.Empty;
        IsMinimumWageExempt = false;
        IsOvertimeExempt = false;
        IsSupervisorReviewer = false;
        IsUnionDuesCollected = false;
        IsUnionInitiationCollected = false;
        JobTitle = string.Empty;
        PayGroup = string.Empty;
        PositionCode = string.Empty;
        ReviewerCompanyNumber = string.Empty;
        ReviewerEmployeeId = string.Empty;
        SupervisorCompanyNumber = string.Empty;
        SupervisorEmployeeId = string.Empty;
        WorkersCompensation = string.Empty;
    }

    public string ChangeReason { get; set; }
    public string CostCenter1 { get; set; }
    public string CostCenter2 { get; set; }
    public string CostCenter3 { get; set; }
    public DateTime EffectiveDate { get; set; }
    public string EmployeeType { get; set; }
    public string EqualEmploymentOpportunityClass { get; set; }
    public bool IsMinimumWageExempt { get; set; }
    public bool IsOvertimeExempt { get; set; }
    public bool IsSupervisorReviewer { get; set; }
    public bool IsUnionDuesCollected { get; set; }
    public bool IsUnionInitiationCollected { get; set; }
    public string JobTitle { get; set; }
    public string PayGroup { get; set; }
    public string PositionCode { get; set; }
    public string ReviewerCompanyNumber { get; set; }
    public string ReviewerEmployeeId { get; set; }
    public string SupervisorCompanyNumber { get; set; }
    public string SupervisorEmployeeId { get; set; }
    public string WorkersCompensation { get; set; }
}

public class FederalTax
{
    public FederalTax()
    {
        Amount = 0f;
        FilingStatus = string.Empty;
        Percentage = 0f;
        TaxCalculationCode = string.Empty;
        W4FormYear = 0;
        HigherRate = false;
    }

    public float Amount { get; set; }
    public string FilingStatus { get; set; }
    public float Percentage { get; set; }
    public string TaxCalculationCode { get; set; }
    public int W4FormYear { get; set; }
    public bool HigherRate { get; set; }
}

public class PrimaryPayRate
{
    public PrimaryPayRate()
    {
        AnnualSalary = 0f;
        BaseRate = 0f;
        ChangeReason = string.Empty;
        DefaultHours = 0f;
        EffectiveDate = DateTime.MinValue;
        BeginCheckDate = DateTime.MinValue;
        IsAutoPay = false;
        PayFrequency = string.Empty;
        PayType = string.Empty;
        RatePer = string.Empty;
        Salary = 0f;
    }

    public float AnnualSalary { get; set; }
    public float BaseRate { get; set; }
    public string ChangeReason { get; set; }
    public float DefaultHours { get; set; }
    public DateTime EffectiveDate { get; set; }
    public DateTime BeginCheckDate { get; set; }
    public bool IsAutoPay { get; set; }
    public string PayFrequency { get; set; }
    public string PayType { get; set; }
    public string RatePer { get; set; }
    public float Salary { get; set; }
}

public class PrimaryStateTax
{
    public PrimaryStateTax()
    {
        Amount = 0f;
        Exemptions = 0f;
        Exemptions2 = 0f;
        FilingStatus = string.Empty;
        Percentage = 0f;
        SpecialCheckCalc = string.Empty;
        TaxCalculationCode = string.Empty;
        TaxCode = string.Empty;
        W4FormYear = 0;
    }

    public float Amount { get; set; }
    public float Exemptions { get; set; }
    public float Exemptions2 { get; set; }
    public string FilingStatus { get; set; }
    public float Percentage { get; set; }
    public string SpecialCheckCalc { get; set; }
    public string TaxCalculationCode { get; set; }
    public string TaxCode { get; set; }
    public int W4FormYear { get; set; }
}

public class Status
{
    public Status()
    {
        ChangeReason = string.Empty;
        EffectiveDate = DateTime.MinValue;
        EmployeeStatus = string.Empty;
        HireDate = DateTime.MinValue;
        IsEligibleForRehire = false;
        StatusType = string.Empty;
    }

    public string ChangeReason { get; set; }
    public DateTime EffectiveDate { get; set; }
    public string EmployeeStatus { get; set; }
    public DateTime HireDate { get; set; }
    public bool IsEligibleForRehire { get; set; }
    public string StatusType { get; set; }
}

public class WorkEligibility
{
    public WorkEligibility()
    {
        IsI9Verified = false;
        IsSsnVerified = false;
    }

    public bool IsI9Verified { get; set; }
    public bool IsSsnVerified { get; set; }
}

public class WorkAddress
{
    public WorkAddress()
    {
        Location = string.Empty;
        Address1 = string.Empty;
        Address2 = string.Empty;
        City = string.Empty;
        Country = string.Empty;
        EmailAddress = string.Empty;
        State = string.Empty;
        PostalCode = string.Empty;
    }

    public string Location { get; set; }
    public string Address1 { get; set; }
    public string Address2 { get; set; }
    public string City { get; set; }
    public string Country { get; set; }
    public string EmailAddress { get; set; }
    public string State { get; set; }
    public string PostalCode { get; set; }
}

public class HomeAddress
{
    public HomeAddress()
    {
        Address1 = string.Empty;
        City = string.Empty;
        Country = string.Empty;
        EmailAddress = string.Empty;
        MobilePhone = string.Empty;
        Phone = string.Empty;
        State = string.Empty;
        PostalCode = string.Empty;
    }

    public string Address1 { get; set; }
    public string City { get; set; }
    public string Country { get; set; }
    public string EmailAddress { get; set; }
    public string MobilePhone { get; set; }
    public string Phone { get; set; }
    public string State { get; set; }
    public string PostalCode { get; set; }
}

public class WebTime
{
    public WebTime()
    {
        IsTimeLaborEnabled = false;
    }

    public bool IsTimeLaborEnabled { get; set; }
}

public class EmergencyContact
{
    public EmergencyContact()
    {
        FirstName = string.Empty;
        LastName = string.Empty;
        Relationship = string.Empty;
        PrimaryPhone = string.Empty;
        MobilePhone = string.Empty;
        Priority = string.Empty;
    }

    public string FirstName { get; set; }
    public string LastName { get; set; }
    public string Relationship { get; set; }
    public string PrimaryPhone { get; set; }
    public string MobilePhone { get; set; }
    public string Priority { get; set; }
}
