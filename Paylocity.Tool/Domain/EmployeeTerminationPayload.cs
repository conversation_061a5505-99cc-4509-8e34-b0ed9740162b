﻿using System;
using System.Collections.Generic;

namespace Paylocity.Tool.Domain;

public class EmployeeTerminationPayload : Change
{
    public EmployeeTerminationPayload()
        : base()
    {
        EmployeeCostCenter1 = string.Empty;
        EmployeeCostCenter2 = string.Empty;
        EmployeeCostCenter3 = string.Empty;
        EmployeeFirstName = string.Empty;
        EmployeeLastName = string.Empty;
        EmployeeMiddleInitial = string.Empty;
        EmployeeWorkEMailAddress = string.Empty;
    }

    public string EmployeeCostCenter1 { get; set; }
    public string EmployeeCostCenter2 { get; set; }
    public string EmployeeCostCenter3 { get; set; }
    public string EmployeeFirstName { get; set; }
    public string EmployeeLastName { get; set; }
    public string EmployeeMiddleInitial { get; set; }
    public string EmployeeWorkEMailAddress { get; set; }
    public DateTime? EmployeeTerminationDate { get; set; }
}

