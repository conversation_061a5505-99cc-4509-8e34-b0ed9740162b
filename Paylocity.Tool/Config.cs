﻿using Payroll.Shared;
using System.Linq;
using System.Collections.Generic;
using Newtonsoft.Json;
using Serilog;
using System;

namespace Paylocity.Tool
{
    public static class Config
    {
        public static readonly string ENV_KEY_SECRET = "PAYLOCITY_SECRET";
        public static readonly string ENV_KEY_CLIENT_ID = "PAYLOCITY_CLIENT_ID";
        public static readonly string SettingSection = "paylocity";
        private static readonly string LocationSection = "paylocity_restaurants";
        private static readonly string ManagerSection = "paylocity_manager_jobs";
        public static readonly string SkipSection = "paylocity_skip_locations";

        private static readonly HashSet<string> LocationsToSkip = new HashSet<string>();

        static Config()
        {
            Setting.Init();

            var skiplist = Setting.ListSection(SkipSection);
            LocationsToSkip = skiplist.Keys.ToHashSet();
        }

        public static int ApiDelayInSeconds()
        {
            var delay = Setting.Get(SettingSection, "delay");
            if (string.IsNullOrEmpty(delay)) return 0;
            return Convert.ToInt32(delay);
        }

        public static bool SkipEmployee(Employee employee)
        {
            return LocationsToSkip.Contains(employee.PrimaryWorkLocation);
        }

        public static string SystemDsn()
        {
            return Setting.Get("system", "dsn");
        }

        public static string Host()
        {
            return Setting.Get(SettingSection, "host");
        }

        public static string ClientId()
        {
            return Setting.GetFromConfigOrEnv(SettingSection, "clientid", ENV_KEY_CLIENT_ID);
        }

        public static string Secret()
        {
            return Setting.GetFromConfigOrEnv(SettingSection, "secret", ENV_KEY_SECRET);
        }

        public static string Username()
        {
            return Setting.Get(SettingSection, "username");
        }

        public static string Password()
        {
            return Setting.Get(SettingSection, "password");
        }

        public static string CompanyId()
        {
            return Setting.Get(SettingSection, "companyid");
        }

        public static Dictionary<string, RestaurantInfo> RestaurantsById()
        {
            var map = new Dictionary<string, RestaurantInfo>();
            var section = Setting.ListSection(LocationSection);

            foreach (var item in section)
            {
                var ri = new RestaurantInfo()
                {
                    Id = item.Key,
                    Code = item.Value
                };
                map.Add(item.Key, ri);
            }

            return map;
        }

        public static Dictionary<string, RestaurantInfo> RestaurantsByCode()
        {
            var map = new Dictionary<string, RestaurantInfo>();
            var section = Setting.ListSection(LocationSection);

            foreach (var item in section)
            {
                var ri = new RestaurantInfo()
                {
                    Id = item.Key,
                    Code = item.Value
                };
                map.Add(item.Value, ri);
            }

            return map;
        }

        public static List<string> ManagerJobsById()
        {
            var managerJobs = new List<string>();
            var section = Setting.ListSection(ManagerSection);
            if (section == null) return managerJobs;
            return section.Keys.ToList();
        }

        public static string ExecutionMode()
        {
            var mode = Setting.Get(SettingSection, "mode");
            if (string.IsNullOrEmpty(mode)) return "none";
            return mode;
        }
   }
}