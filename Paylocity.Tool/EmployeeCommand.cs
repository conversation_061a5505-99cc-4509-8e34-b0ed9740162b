﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using Newtonsoft.Json;
using Payroll.Shared;
using Serilog;
using Paylocity.Tool.Domain;

namespace Paylocity.Tool
{
    class EmployeeCommand : BaseCommand
    {
        void PrintSelectedEmployees(SortedDictionary<string, EmployeeListInfo> empInfoList, Func<EmployeeListInfo, bool> match)
        {

            foreach (var empId in empInfoList.Keys)
            {
                var empInfo = empInfoList[empId];
                if (!match(empInfo)) continue;
                Console.WriteLine("${empInfo.EmployeeId}");
            }
        }

        SortedDictionary<string, EmployeeListInfo> GetAllEmployees()
        {
            var empInfoList = new SortedDictionary<string, EmployeeListInfo>();
            try
            {
                using (var service = new PaylocityService())
                {
                    empInfoList = service.GetAllEmployeesAsync().Result;
                }
            }
            catch (Exception e)
            {
                Log.Logger.Error(e.Message);
                Log.Logger.Error(e.StackTrace);
            }

            return empInfoList;
        }

        public void List(List<string> args)
        {
            var empInfoList = GetAllEmployees();

            if (args != null && args.Count > 0)
            {
                if (args[0] == "active") PrintSelectedEmployees(empInfoList, x => x.StatusTypeCode == "A");
                else if (args[0] == "terms") PrintSelectedEmployees(empInfoList, x => x.StatusTypeCode == "T");
                else if (args[0] == "leave") PrintSelectedEmployees(empInfoList, x => x.StatusTypeCode == "L");

            }
            else
            {
                Console.WriteLine("Id   \tStatus");
                foreach (var empId in empInfoList.Keys)
                {
                    var empInfo = empInfoList[empId];
                    Console.WriteLine($"{empInfo.EmployeeId}\t{empInfo.StatusCode}\t{empInfo.StatusTypeCode}");
                }
            }
        }

        public void Export(List<string> args)
        {
            if (args == null || args.Count < 1)
            {
                Console.WriteLine("Usage: Paylocity.Tool.exe employee export <employee id>");
                return;
            }

            try
            {
                using (var service = new PaylocityService())
                {
                    var employee = service.GetEmployee(args[0]).Result;
                    var exportableEmployee = PaylocityService.BuildExportableEmployee(employee);
                    Console.WriteLine("[");
                    Console.WriteLine(JsonConvert.SerializeObject(exportableEmployee, Formatting.Indented));
                    Console.WriteLine("]");
                }
            }
            catch (Exception e)
            {
                Log.Logger.Fatal(e.Message);
                Log.Logger.Fatal(e.StackTrace);
            }
        }

        public void View(List<string> args)
        {
            if (args == null || args.Count < 1)
            {
                Console.WriteLine("Usage: Paylocity.Tool.exe employee view <employee id>");
                return;
            }

            try
            {
                using (var service = new PaylocityService())
                {
                    var employee = service.GetEmployee(args[0]).Result;
                    Console.WriteLine(JsonConvert.SerializeObject(employee, Formatting.Indented));
                }
            }
            catch (Exception e)
            {
                Log.Logger.Fatal(e.Message);
                Log.Logger.Fatal(e.StackTrace);
            }
        }
    }
}
