﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using Newtonsoft.Json;
using Payroll.Shared;
using Serilog;
using Paylocity.Tool.Domain;
using System.Security.Cryptography.Xml;
using System.ComponentModel.DataAnnotations;
using DnsClient.Internal;

namespace Paylocity.Tool
{
    class ReportCommand : BaseCommand
    {
        public void Fedtax(List<string> args)
        {
            int delayInMilliseconds = Config.ApiDelayInSeconds() * 500;

            try
            {
                using (var service = new PaylocityService())
                {
                    var empInfoList = service.GetAllEmployeesAsync().Result;
                    Console.WriteLine("Id\tLastName\tBaseRate\tFedTaxRate");
                    foreach (var empId in empInfoList.Keys)
                    {
                        try
                        {
                            var employee = service.GetEmployee(empId).Result;
                            if (employee.FederalTax.Amount > 0 && employee.PrimaryPayRate.BaseRate != employee.FederalTax.Amount)
                                Console.WriteLine($"{employee.EmployeeId}\t{employee.LastName}\t{employee.PrimaryPayRate.BaseRate}\t{employee.FederalTax.Amount}");
                        }
                        catch (Exception e)
                        {
                            Log.Logger.Error(e.Message);
                            Log.Logger.Error(e.StackTrace);
                        }

                        Thread.Sleep(delayInMilliseconds);
                   }
                }
            }
            catch (Exception e)
            {
                Log.Logger.Error(e.Message);
                Log.Logger.Error(e.StackTrace);
            }
        }
    }
}
