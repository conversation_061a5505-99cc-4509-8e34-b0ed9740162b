﻿using MongoDB.Bson;
using MongoDB.Bson.Serialization.Attributes;

namespace Paylocity.Tool;

public class Change
{
    public string Id { get; set; }

    [BsonElement("companyId")]
    public string CompanyId { get; set; }

    [BsonElement("description")]
    public string Description { get; set; }

    [BsonElement("when")]
    [BsonDateTimeOptions(Kind = DateTimeKind.Local, Representation = BsonType.Document)]
    public DateTime When { get; set; }
}
