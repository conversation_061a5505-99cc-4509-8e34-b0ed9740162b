<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <OutputType>Exe</OutputType>
    <TargetFrameworks>net8.0</TargetFrameworks>
    <Configurations>Debug;Release;CoreOnly</Configurations>
    <Platforms>AnyCPU</Platforms>
    <LangVersion>10.0</LangVersion>
    <ImplicitUsings>enable</ImplicitUsings>
  </PropertyGroup>

  <PropertyGroup Condition=" '$(Configuration)' == 'Release' ">
    <DebugType>none</DebugType>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="CommandLineParser" Version="2.9.1" />
    <PackageReference Include="LiteDB" Version="5.0.21" />
    <PackageReference Include="Microsoft.Extensions.Caching.Memory" Version="9.0.4" />
    <PackageReference Include="MongoDB.Driver" Version="3.4.0" />
    <PackageReference Include="Newtonsoft.Json" Version="13.0.3" />
    <PackageReference Include="Serilog" Version="4.2.0" />
    <PackageReference Include="Serilog.Sinks.Console" Version="6.0.0" />
    <PackageReference Include="Serilog.Sinks.File" Version="7.0.0" />
    <PackageReference Include="System.ServiceModel.Duplex" Version="6.0.0" />
    <PackageReference Include="System.ServiceModel.Http" Version="8.1.2" />
    <PackageReference Include="System.ServiceModel.NetTcp" Version="8.1.2" />
    <PackageReference Include="System.ServiceModel.Security" Version="6.0.0" />
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\Payroll.Shared\Payroll.Shared.csproj" />
  </ItemGroup>

</Project>
