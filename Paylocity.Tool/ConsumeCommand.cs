﻿using Payroll.Shared;
using Serilog;
using System;
using System.Collections.Generic;
using System.Linq;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;
using MongoDB.Bson;
using MongoDB.Driver;
using MongoDB.Driver.Linq;

namespace Paylocity.Tool
{
    public class ConsumeCommand
    {
        public void Changes(List<string> args)
        {
            var dryRun = !(args != null && args.Count > 0 && args[0] == "doit");
            Log.Logger.Debug("Dry Run Mode: {0}", dryRun);

            try
            {
                var database = Db.Connect();
                var collection = database.GetCollection<Change>("changes");
                var docs = collection.Find(new BsonDocument()).ToList();
                Log.Logger.Debug(JsonConvert.SerializeObject(docs, Formatting.Indented));

                var employees = new List<Employee>();
                using (var service = new PaylocityService())
                {
                    foreach (var doc in docs)
                    {
                        var employee = service.GetEmployee(doc.Id).Result;
                        var exportableEmployee = PaylocityService.BuildExportableEmployee(employee);
                        employees.Add(exportableEmployee);

                    }

                    Console.WriteLine(JsonConvert.SerializeObject(employees, Formatting.Indented));

                    // delete after printing out, if not a dryrun
                    if (!dryRun)
                    {
                        var filter = Builders<Change>.Filter.In("_id", employees.Select(x => x.Id));
                        collection.DeleteMany(filter);
                    }
                }
            }
            catch (Exception e)
            {
                Log.Logger.Error(e.Message);
                Log.Logger.Error(e.StackTrace);
            }
        }
    }
}