﻿using Payroll.Shared;
using Serilog;
using System;
using System.Collections.Generic;
using System.Linq;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;
using MongoDB.Bson;
using MongoDB.Driver;
using MongoDB.Driver.Linq;

namespace Paylocity.Tool
{
    public class ChangeCommand
    {
        public void List(List<string> args)
        {
            try
            {
                var database = Db.Connect();
                var collection = database.GetCollection<Change>("changes");
                var docs = collection.Find(new BsonDocument()).ToList();
                foreach (var doc in docs)
                {
                    Console.WriteLine($"{doc.Id}\t{doc.CompanyId}\t{doc.When}");
                }
            }
            catch (Exception e)
            {
                Log.Logger.Error(e.Message);
                Log.Logger.Error(e.StackTrace);
            }
        }
    }
}