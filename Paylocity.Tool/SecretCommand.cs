﻿using Payroll.Shared;
using Serilog;
using System;
using System.Collections.Generic;
using System.Linq;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;

namespace Paylocity.Tool
{
    public class SecretCommand
    {
        public void Renew(List<string> args)
        {
            if (args.Count < 1)
            {
                Console.WriteLine("Usage: secret renew <code>");
                Console.WriteLine("  - use this command to renew the client secret");
                Console.WriteLine("  - required once a year");
                return;
            }

            var code = args[0];

            using (var service = new PaylocityService())
            {
                var response = service.RenewCredentials(code).Result;
                Console.WriteLine(response);
            }
        }
    }
}
