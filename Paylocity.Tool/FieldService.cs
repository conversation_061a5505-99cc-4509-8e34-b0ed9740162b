﻿using Payroll.Shared;
using Paylocity.Tool.Domain;
using System;
using System.Collections.Generic;
using System.Net;
using System.Text;

namespace Paylocity.Tool
{
    public static class FieldService
    {
        public readonly static string FieldSection = "paylocity_fields";
        public static string HomeLocationField = "costcenter1";
        public static string DeptCodeField = "costcenter2";
        public static string JobCodeField = "job";
        public static string FirstNameField = "firstname"; // firstname, preferred, nickname, or none (for firstname)

        static FieldService()
        {
            Setting.Init();

            {
                var x = Setting.Get(FieldSection, "home_location").ToLower();
                if (!string.IsNullOrEmpty(x)) HomeLocationField = x;
            }

            {
                var x = Setting.Get(FieldSection, "dept_code").ToLower();
                if (!string.IsNullOrEmpty(x)) DeptCodeField = x;
            }

            {
                var x = Setting.Get(FieldSection, "job_code").ToLower();
                if (!string.IsNullOrEmpty(x)) JobCodeField = x;
            }

            FirstNameField = Setting.Get(FieldSection, "fname").ToLower();
        }

        public static string GetFirstName(Domain.Employee employee)
        {
            if (string.IsNullOrEmpty(employee.PreferredName) || FirstNameField != "preferred")
                return employee.FirstName.ToProperCase();

            return employee.PreferredName.ToProperCase();
        }

        public static string GetHomeLocation(Domain.Employee employee)
        {
            switch (HomeLocationField)
            {
                case "costcenter1":
                default:
                    if (!string.IsNullOrEmpty(employee.DepartmentPosition.CostCenter1))
                        return employee.DepartmentPosition.CostCenter1;
                    if (employee.AdditionalRate != null && employee.AdditionalRate.Count() > 0)
                        return employee.AdditionalRate[0].CostCenter1;
                    return string.Empty;
            }
        }

        public static string GetJobCode(Domain.AdditionalRate rate)
        {
            switch (JobCodeField)
            {
                case "costcenter3":
                    return rate.CostCenter3;
                case "job":
                default:
                    return rate.Job;
            }
        }

        public static string GetDeptCode(Domain.Employee employee)
        {
            switch (DeptCodeField)
            {
                case "costcenter2":
                default:
                    if (!string.IsNullOrEmpty(employee.DepartmentPosition.CostCenter2))
                        return employee.DepartmentPosition.CostCenter2;
                    if (employee.AdditionalRate != null && employee.AdditionalRate.Count() > 0)
                        return employee.AdditionalRate[0].CostCenter2;
                    return string.Empty;
            }
        }
    }
}
