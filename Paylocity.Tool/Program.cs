using CommandLine;
using Payroll.Shared;
using Serilog;
using System;
using System.Collections.Generic;

namespace Paylocity.Tool
{
    class Program : ProgramBase<SettingCommand>
    {
        static readonly string AppVersion = "0.3";

        static void DoShowUsage()
        {
            Console.WriteLine("Usage: Paylocity.Tool.exe <command> <command-args>");
            Console.WriteLine("  where <command> is one of...");
            Console.WriteLine("   - cache [init, clear]");
            Console.WriteLine("   - change [list] ");
            Console.WriteLine("   - consume [changes] ");
            Console.WriteLine("   - employee [list, view] = employee management");
            Console.WriteLine("   - export [changes, employee, employees] ");
            Console.WriteLine("   - report [fedtax] ");
            Console.WriteLine("   - secret [renew] <code>");
            Console.WriteLine();
        }

        public override int ShowUsage()
        {
            DoShowUsage();
            return 0;
        }

        public void Help(List<string> args)
        {
            ShowUsage();
        }

        public void Info(List<string> args)
        {
            Console.WriteLine($"Paylocity.Tool.exe version {AppVersion} ({Payroll.Shared.Setting.IniFilePath})");
            Console.WriteLine();
            Console.WriteLine($"  Settings:");
            Console.WriteLine($"    CompanyId      = {Config.CompanyId()}");
            Console.WriteLine($"    Username       = {Config.Username()}");
            Console.WriteLine($"    Host           = {Config.Host()}");
            Console.WriteLine($"    ClientId       = {Config.ClientId()}");
            Console.WriteLine($"    Secret         = {Config.Secret()}");

            Console.WriteLine();
            Console.WriteLine("  Fields:");
            Console.WriteLine($"    JobCode: using '{FieldService.JobCodeField}'");
            Console.WriteLine($"    DeptCode: using '{FieldService.DeptCodeField}'");
            Console.WriteLine($"    HomeLoc: using '{FieldService.HomeLocationField}'");
        }

        private int ExecCommand<T>(List<string> args) where T : new()
        {
            var command = new CommandArguments(args);

            return Command<T>.Invoke(command);
        }

        public void Change(List<string> args)
        {
            ExecCommand<ChangeCommand>(args);
        }

        public void Consume(List<string> args)
        {
            ExecCommand<ConsumeCommand>(args);
        }

        public void Employee(List<string> args)
        {
            ExecCommand<EmployeeCommand>(args);
        }

        public void Export(List<string> args)
        {
            ExecCommand<ExportCommand>(args);
        }

        public void Secret(List<string> args)
        {
            ExecCommand<SecretCommand>(args);
        }

        public void Report(List<string> args)
        {
            ExecCommand<ReportCommand>(args);
        }

        static int Main(string[] args)
        {
            if (args.Length == 0)
            {
                DoShowUsage();
                return -1;
            }

            // setup logging services...
            var command = string.Join(" ", args);
            Logger.Setup($"Paylocity.Tool, LocFld: {FieldService.HomeLocationField}, Command: '{command}', Version: {AppVersion}");

            // Log version information
            Log.Logger.Information($"Paylocity.Tool.exe version {AppVersion} ({Payroll.Shared.Setting.IniFilePath})");
            Log.Logger.Information($"  Branch: {BuildInfo.Branch}, Built: {BuildInfo.Built}, Host: {BuildInfo.Host}, Hash: {BuildInfo.Commit}");

            try
            {
                Parser.Default.ParseArguments<ProgramArguments>(args)
                    .MapResult((ProgramArguments opts) => ProgramDriver<Program>.Run(opts), errs => 1);
            }
            catch (Exception e)
            {
                Log.Logger.Error(e.Message);
                Log.Logger.Error(e.StackTrace);
                return -1;
            }

            return 0;
        }
    }
}
