NOW=$(shell date "+%y%m%d%H%M")
COMMIT_HASH=$(shell git rev-parse HEAD)
CURRENT_BRANCH=$(shell git branch --show-current)
HOSTNAME=$(shell hostname)
MACHINE_OS?=linux8
COMMIT_HASH_FILE=Payroll.Shared/BuildInfo.cs

setup:
	rm -rf dist/*
	echo "public static class BuildInfo { " > $(COMMIT_HASH_FILE);
	echo "  public const string Branch = \"$(CURRENT_BRANCH)\";" >> $(COMMIT_HASH_FILE);
	echo "  public const string Built = \"$(shell date "+%m-%d-%Y @ %H:%M%p")\";" >> $(COMMIT_HASH_FILE);
	echo "  public const string Host=\"$(HOSTNAME)\";" >> $(COMMIT_HASH_FILE)
	echo "  public const string Commit=\"$(COMMIT_HASH)\";" >> $(COMMIT_HASH_FILE)
	echo "}" >> $(COMMIT_HASH_FILE)

test:
	$(MAKE) setup
	(cd Brink.Tool && make clean $(MACHINE_OS))

slim:
	$(MAKE) setup
	(cd Payroll.Tool && make clean $(MACHINE_OS))
	(cd CrunchTime.Tool && make clean $(MACHINE_OS))
	(cd Paycom2.Tool && make clean $(MACHINE_OS))

slim-corp:
	$(MAKE) slim
	(cd Clients/SlimChicken-Corp/SlimChicken.Tool && make $(MACHINE_OS))
	mkdir dist/configs
	cp -r Clients/Common/Scripts/*.timer dist/configs
	cp -r Clients/Common/Scripts/*.service dist/configs
	cp -r Clients/SlimChicken-Corp/Scripts/*.ini dist/configs
	cp -r Clients/SlimChicken-Corp/Scripts/*.sh dist/
	find ../Releases/SlimChicken-Corp/ -mindepth 1 -type d -not -path '*/\.*' -prune | xargs rm -rf
	cp -vr dist/* ../Releases/SlimChicken-Corp/

ap-group:
	$(MAKE) MACHINE_OS=linux9 slim
	(cd Clients/Ap-Group/ApGroup.Tool && make $(MACHINE_OS))
	mkdir dist/configs
	cp -r Clients/Common/Scripts/*.timer dist/configs
	cp -r Clients/Common/Scripts/*.service dist/configs
	cp -r Clients/Ap-Group/Scripts/*.ini dist/configs
	cp -r Clients/Ap-Group/Scripts/*.sh dist/
	find ../Releases/Ap-Group/ -mindepth 1 -type d -not -path '*/\.*' -prune | xargs rm -rf
	cp -vr dist/* ../Releases/Ap-Group/

slim-4j:
	$(MAKE) slim
	(cd Aloha.Tool && make clean $(MACHINE_OS))
	(cd Clients/SlimChicken-FourJays/FourJays.Tool && make $(MACHINE_OS))
	mkdir dist/configs
	cp -r Clients/Common/Scripts/*.timer dist/configs
	cp -r Clients/Common/Scripts/*.service dist/configs
	cp -r Clients/SlimChicken-FourJays/Scripts/*.* dist/configs
	find ../Releases/SlimChicken-FourJays/ -mindepth 1 -type d -not -path '*/\.*' -prune | xargs rm -rf
	cp -vr dist/* ../Releases/SlimChicken-FourJays/

graeters:
	$(MAKE) setup
	(cd Payroll.Tool && make clean $(MACHINE_OS))
	(cd Paycom2.Tool && make clean $(MACHINE_OS))
	(cd DataCentral.Tool && make clean $(MACHINE_OS))
	(cd Clients/Graeters/Graeters.Tool && make $(MACHINE_OS))
	mkdir dist/configs
	cp -r Clients/Graeters/Scripts/*.ini dist/configs
	cp -r Clients/Graeters/Scripts/*.timer dist/configs
	cp -r Clients/Graeters/Scripts/*.service dist/configs
	cp -r Clients/Graeters/Scripts/*.sh dist/
	find ../Releases/Graeters/ -mindepth 1 -type d -not -path '*/\.*' -prune | xargs rm -rf
	cp -vr dist/* ../Releases/Graeters/

upward:
	$(MAKE) setup
	(cd Payroll.Tool && make clean $(MACHINE_OS))
	(cd Toast.Tool && make clean $(MACHINE_OS))
	(cd 7shifts.Tool && make clean $(MACHINE_OS))
	(cd Radar.Tool && make clean $(MACHINE_OS))
	(cd Paylocity.Tool && make clean $(MACHINE_OS))
	(cd Clients/Upward/Upward.Tool && make $(MACHINE_OS))
	mkdir dist/configs
	cp -r Clients/Upward/Scripts/*.ini dist/configs
	cp -r Clients/Upward/Scripts/*.sh dist/
	find ../Releases/Upward/ -mindepth 1 -type d -not -path '*/\.*' -prune | xargs rm -rf
	cp -vr dist/* ../Releases/Upward/

rockhill:
	$(MAKE) setup
	(cd Payroll.Tool && make win8)
	(cd AD.Tool && make win8)
	(cd Paycom2.Tool && make win8)
	(cd Clients/RockHill/Tool && make win8)
	mkdir dist/Scripts
	cp -r Clients/RockHill/Scripts/* dist/Scripts/
	cp -r Clients/RockHill/Scripts/* dist/
	find ../Releases/RockHill/ -mindepth 1 -type d -not -path '*/\.*' -prune | xargs rm -rf
	cp -r dist/* ../Releases/RockHill/

artbrew:
	$(MAKE) setup
	(cd Payroll.Tool && make clean $(MACHINE_OS))
	(cd Toast.Tool && make clean $(MACHINE_OS))
	(cd Clients/ArtBrew/Tool && make clean $(MACHINE_OS))
	mkdir dist/Scripts
	cp -r Clients/ArtBrew/Scripts/* dist/Scripts/
	cp -r Clients/ArtBrew/Scripts/*.sh dist/
	find ../Releases/ArtBrew/ -mindepth 1 -type d -not -path '*/\.*' -prune | xargs rm -rf
	cp -r dist/* ../Releases/ArtBrew/

clean:
	find . -type d -name "bin" | xargs rm -rf && find . -type d -name "obj" | xargs rm -rf
	rm -rf ../Releases/Graeters/*
	rm -rf ../Releases/ArtBrew/*
	rm -rf ../Releases/SlimChicken/*
	rm -rf ../Releases/RockHill/*
	rm -rf ../Releases/SlimChicken-FourJays/*
	rm -rf ../Releases/SlimChicken-Corp/*

publish-upward:
	rclone sync --no-update-modtime -P ../Releases/Upward/ r2:upward/PayrollTools/

publish-artbrew:
	rclone sync --no-update-modtime -P ../Releases/ArtBrew/ r2:artbrew/

publish-rockhill:
	rclone sync --no-update-modtime -P ../Releases/RockHill/ r2:rockhill/

publish-graeters:
	rclone sync --no-update-modtime -P ../Releases/Graeters/ r2:graeters/PayrollTools

publish-slim-corp:
	rclone sync --no-update-modtime -P ../Releases/SlimChicken-Corp/ r2:slim/Corp

publish-slim-4jays:
	rclone sync --no-update-modtime -P ../Releases/SlimChicken-FourJays/ r2:slim/FourJays

publish-ap-group:
	rclone sync --no-update-modtime -P ../Releases/Ap-Group/ r2:ap-group
