<component name="ProjectRunConfigurationManager">
  <configuration default="false" name="Publish Brink.Tool for Windows Distribution" type="DotNetFolderPublish" factoryName="Publish to folder">
    <riderPublish configuration="Release" platform="Any CPU" produce_single_file="true" runtime="win-x86" target_folder="$PROJECT_DIR$/dist/win/brink" target_framework="netcoreapp3.1" uuid_high="4807816100467983812" uuid_low="-7348335637566373992" />
    <method v="2" />
  </configuration>
  <configuration default="false" name="Publish Brink.Tool for Linux Distribution" type="DotNetFolderPublish" factoryName="Publish to folder">
    <riderPublish configuration="Release" platform="Any CPU" produce_single_file="true" runtime="linux-x64" target_folder="$PROJECT_DIR$/dist/linux/brink" target_framework="netcoreapp3.1" uuid_high="4807816100467983812" uuid_low="-7348335637566373992" />
    <method v="2" />
  </configuration>
</component>