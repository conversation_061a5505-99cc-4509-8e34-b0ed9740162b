<component name="ProjectRunConfigurationManager">
  <configuration default="false" name="Publish Ultipro.Tool For Windows Distribution" type="DotNetFolderPublish" factoryName="Publish to folder">
    <riderPublish configuration="Release" platform="Any CPU" produce_single_file="true" runtime="win-x86" target_folder="$PROJECT_DIR$/dist/win/ulti" target_framework="netcoreapp3.1" uuid_high="4313126177020461794" uuid_low="-5195112844556979587" />
    <method v="2" />
  </configuration>
  <configuration default="false" name="Publish Ultipro.Tool For Linux Distribution" type="DotNetFolderPublish" factoryName="Publish to folder">
    <riderPublish configuration="Release" platform="Any CPU" produce_single_file="true" runtime="linux-x64" target_folder="$PROJECT_DIR$/dist/linux/ulti" target_framework="netcoreapp3.1" uuid_high="4313126177020461794" uuid_low="-5195112844556979587" />
    <method v="2" />
  </configuration>
</component>