<component name="ProjectRunConfigurationManager">
  <configuration default="false" name="Publish Payroll.Tool for Windows Distribution" type="DotNetFolderPublish" factoryName="Publish to folder">
    <riderPublish configuration="Release" platform="Any CPU" produce_single_file="true" runtime="win-x86" target_folder="$PROJECT_DIR$/dist/win/payroll" target_framework="netcoreapp3.1" uuid_high="-2509592542389318628" uuid_low="-6214934435279936118" />
    <method v="2" />
  </configuration>
  <configuration default="false" name="Publish Payroll.Tool for Linux Distribution" type="DotNetFolderPublish" factoryName="Publish to folder">
    <riderPublish configuration="Release" platform="Any CPU" produce_single_file="true" runtime="linux-x64" target_folder="$PROJECT_DIR$/dist/linux/payroll" target_framework="netcoreapp3.1" uuid_high="-2509592542389318628" uuid_low="-6214934435279936118" />
    <method v="2" />
  </configuration>
</component>