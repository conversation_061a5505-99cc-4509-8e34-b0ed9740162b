<component name="ProjectRunConfigurationManager">
  <configuration default="false" name="Publish Micros.Tool for Windows Distribution" type="DotNetFolderPublish" factoryName="Publish to folder">
    <riderPublish configuration="Release" platform="Any CPU" produce_single_file="true" runtime="win-x86" target_folder="$PROJECT_DIR$/dist/win/micros" target_framework="netcoreapp3.1" uuid_high="7059394646362112601" uuid_low="-8693629909710705119" />
    <method v="2" />
  </configuration>
</component>