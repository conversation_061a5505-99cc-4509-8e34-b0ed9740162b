<component name="ProjectRunConfigurationManager">
  <configuration default="false" name="Publish Paycom.Tool for Windows Distribution" type="DotNetFolderPublish" factoryName="Publish to folder">
    <riderPublish configuration="Release" platform="Any CPU" produce_single_file="true" runtime="win-x86" target_folder="$PROJECT_DIR$/dist/win/paycom" target_framework="netcoreapp3.1" uuid_high="-5623620864204586423" uuid_low="-8904670922500032259" />
    <method v="2" />
  </configuration>
  <configuration default="false" name="Publish Paycom.Tool for Linux Distribution" type="DotNetFolderPublish" factoryName="Publish to folder">
    <riderPublish configuration="Release" platform="Any CPU" produce_single_file="true" runtime="linux-x64" target_folder="$PROJECT_DIR$/dist/linux/paycom" target_framework="netcoreapp3.1" uuid_high="-5623620864204586423" uuid_low="-8904670922500032259" />
    <method v="2" />
  </configuration>
</component>