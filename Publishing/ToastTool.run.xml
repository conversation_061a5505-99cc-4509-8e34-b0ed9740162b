<component name="ProjectRunConfigurationManager">
  <configuration default="false" name="Publish Toast.Tool for Windows Distribution" type="DotNetFolderPublish" factoryName="Publish to folder">
    <riderPublish configuration="Release" platform="Any CPU" produce_single_file="true" runtime="win-x86" target_folder="$PROJECT_DIR$/dist/win/toast" target_framework="netcoreapp3.1" uuid_high="6566841193629042483" uuid_low="-6658948143446192874" />
    <method v="2" />
  </configuration>
  <configuration default="false" name="Publish Toast.Tool for Linux Distribution" type="DotNetFolderPublish" factoryName="Publish to folder">
    <riderPublish configuration="Release" platform="Any CPU" produce_single_file="true" runtime="linux-x64" target_folder="$PROJECT_DIR$/dist/linux/toast" target_framework="netcoreapp3.1" uuid_high="6566841193629042483" uuid_low="-6658948143446192874" />
    <method v="2" />
  </configuration>
</component>