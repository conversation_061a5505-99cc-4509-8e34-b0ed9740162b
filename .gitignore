.DS_Store
.idea/
logfile
temp.txt
Payroll.Shared/CommitHash.cs
Payroll.Shared/BuildInfo.cs

###################
# compiled source #
###################
*.com
*.class
*.dll
*.exe
*.pdb
*.dll.config
*.cache
*.suo
# Include dlls if they’re in the NuGet packages directory
!/packages/*/lib/*.dll
!/packages/*/lib/*/*.dll
# Include dlls if they're in the CommonReferences directory
!*CommonReferences/*.dll
####################
# VS Upgrade stuff #
####################
UpgradeLog.XML
_UpgradeReport_Files/
###############
# Directories #
###############
bin/
obj/
TestResults/
###################
# Web publish log #
###################
*.Publish.xml
#############
# Resharper #
#############
/_ReSharper.*
*.ReSharper.*
############
# Packages #
############
# it’s better to unpack these files and commit the raw source
# git has its own built in compression methods
*.7z
*.dmg
*.gz
*.iso
*.jar
*.rar
*.tar
*.zip
######################
# Logs and databases #
######################
*.log
*.sqlite
# OS generated files #
######################
.DS_Store?
ehthumbs.db
Icon?
Thumbs.db
[Bb]in
[Oo]bj
[Tt]est[Rr]esults
*.suo
*.user
*.[Cc]ache
*[Rr]esharper*
packages
NuGet.exe
_[Ss]cripts
*.exe
*.dll
*.nupkg
*.ncrunchsolution
*.dot[Cc]over
### keep Sybase DLLs ###
!/Assemblies/*.dll
!WinSCP.exe
dist/
*~
.vs/

LABOR.xml
*.json
lfs/