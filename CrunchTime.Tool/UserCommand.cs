﻿using System;
using System.Collections.Generic;
using Payroll.Shared;
using Serilog;

namespace CrunchTime.Tool
{
    public class UserCommand
    {
        public void List(List<string> args)
        {
            try
            {
                using (var crunchTimeService = new CrunchTimeService())
                {
                    var body = crunchTimeService.GetUsersAsync().Result;
                    Console.WriteLine(body);
                }
            }
            catch (Exception e)
            {
                Log.Logger.Error(e.Message);
                Log.Logger.Error(e.StackTrace);
            }
        }
    }
}
