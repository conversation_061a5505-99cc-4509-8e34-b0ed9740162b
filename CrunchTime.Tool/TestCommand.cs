using System;
using System.Globalization;
namespace CrunchTime.Tool;

public class TestCommand
{
    public void Parse(string[] args)
    {
        if (args.Length < 1)
        {
            Console.WriteLine("Usage: crunchtime test parse <dateString>");
            return;
        }

        var dateString = args[0];
        var format = (args.Length > 1) ? args[1] : "HH:mm MM/dd/yyyy ZZZ";

        if (DateTimeOffset.TryParseExact(dateString, format, CultureInfo.InvariantCulture, DateTimeStyles.None, out DateTimeOffset result))
        {
            Console.WriteLine(result);
        }
        else
        {
            Console.WriteLine($"Invalid date string '{dateString}' for format: '{format}'");
        }
    }
}
