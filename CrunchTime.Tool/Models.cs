using System;
using System.Collections.Generic;
using System.Text.Json.Serialization;
using LiteDB;

namespace CrunchTime.Tool;

public class Location
{
    [JsonPropertyName("locationAltAddressDetails")]
    public List<LocationAltAddressDetail> LocationAltAddressDetails { get; set; }

    [JsonPropertyName("locationCode")]
    public string LocationCode { get; set; }

    [JsonPropertyName("locationConceptInfoDetails")]
    public List<LocationConceptInfoDetail> LocationConceptInfoDetails { get; set; }

    [JsonPropertyName("locationDeliverInfoDetails")]
    public List<LocationDeliverInfoDetail> LocationDeliverInfoDetails { get; set; }

    [JsonPropertyName("locationDetailDetails")]
    public List<LocationDetailDetail> LocationDetailDetails { get; set; }

    [JsonPropertyName("locationHoursOfOperationDetails")]
    public List<LocationHoursOfOperationDetail> LocationHoursOfOperationDetails { get; set; }

    [JsonPropertyName("locationLaborDetails")]
    public List<LocationLaborDetail> LocationLaborDetails { get; set; }

    [JsonPropertyName("locationNameAddressDetails")]
    public List<LocationNameAddressDetail> LocationNameAddressDetails { get; set; }

    [JsonPropertyName("locationPostDetails")]
    public List<LocationPostDetail> LocationPostDetails { get; set; }

    [JsonPropertyName("locationTimeClockInTwxDetails")]
    public List<LocationTimeClockInTwxDetail> LocationTimeClockInTwxDetails { get; set; }

    [JsonPropertyName("locationUdaDetails")]
    public List<LocationUdaDetail> LocationUdaDetails { get; set; }
}

public class LocationAltAddressDetail
{
    [JsonPropertyName("acapCode")]
    public string AcapCode { get; set; }

    [JsonPropertyName("accoCode")]
    public string AccoCode { get; set; }

    [JsonPropertyName("altAddressOne")]
    public string AltAddressOne { get; set; }

    [JsonPropertyName("altAddressTwo")]
    public string AltAddressTwo { get; set; }

    [JsonPropertyName("altPhone")]
    public long AltPhone { get; set; }

    [JsonPropertyName("cdpEMail")]
    public string CdpEMail { get; set; }

    [JsonPropertyName("city")]
    public string City { get; set; }

    [JsonPropertyName("companyNameForPO")]
    public string CompanyNameForPO { get; set; }

    [JsonPropertyName("exportCode")]
    public string ExportCode { get; set; }

    [JsonPropertyName("importCode")]
    public string ImportCode { get; set; }

    [JsonPropertyName("locationCode")]
    public string LocationCode { get; set; }

    [JsonPropertyName("payrollExportCode")]
    public string PayrollExportCode { get; set; }

    [JsonPropertyName("prefix")]
    public string Prefix { get; set; }

    [JsonPropertyName("stateProvince")]
    public string StateProvince { get; set; }

    [JsonPropertyName("zip")]
    public int Zip { get; set; }
}

public class LocationConceptInfoDetail
{
    [JsonPropertyName("conceptCode")]
    public string ConceptCode { get; set; }

    [JsonPropertyName("conceptName")]
    public string ConceptName { get; set; }

    [JsonPropertyName("locationCode")]
    public string LocationCode { get; set; }

    [JsonPropertyName("prefix")]
    public string Prefix { get; set; }

    [JsonPropertyName("remove")]
    public string Remove { get; set; }
}

public class LocationDeliverInfoDetail
{
    [JsonPropertyName("activeFlag")]
    public string ActiveFlag { get; set; }

    [JsonPropertyName("delete")]
    public string Delete { get; set; }

    [JsonPropertyName("deliverToCode")]
    public string DeliverToCode { get; set; }

    [JsonPropertyName("deliverToName")]
    public string DeliverToName { get; set; }

    [JsonPropertyName("locationCode")]
    public string LocationCode { get; set; }

    [JsonPropertyName("prefix")]
    public string Prefix { get; set; }
}

public class LocationDetailDetail
{
    [JsonPropertyName("allowAutoPost")]
    public string AllowAutoPost { get; set; }

    [JsonPropertyName("allowAutoTransfer")]
    public string AllowAutoTransfer { get; set; }

    [JsonPropertyName("autoPostDaysOffset")]
    public int AutoPostDaysOffset { get; set; }

    [JsonPropertyName("autoPostMethod")]
    public string AutoPostMethod { get; set; }

    [JsonPropertyName("autoPostTime")]
    public string AutoPostTime { get; set; }

    [JsonPropertyName("autoPrepForDepletion")]
    public string AutoPrepForDepletion { get; set; }

    [JsonPropertyName("baseCurrency")]
    public string BaseCurrency { get; set; }

    [JsonPropertyName("biziqCompDate")]
    public string BiziqCompDate { get; set; }

    [JsonPropertyName("borrowCnsEndDate")]
    public string BorrowCnsEndDate { get; set; }

    [JsonPropertyName("borrowCnsLocationCode")]
    public string BorrowCnsLocationCode { get; set; }

    [JsonPropertyName("buffetQuantityAlertPercent")]
    public int BuffetQuantityAlertPercent { get; set; }

    [JsonPropertyName("dailyPrepReview")]
    public string DailyPrepReview { get; set; }

    [JsonPropertyName("earliestPostDateTimeBasedOn")]
    public string EarliestPostDateTimeBasedOn { get; set; }

    [JsonPropertyName("gmtOffset")]
    public int GmtOffset { get; set; }

    [JsonPropertyName("grandOpeningDate")]
    public string GrandOpeningDate { get; set; }

    [JsonPropertyName("locationCode")]
    public string LocationCode { get; set; }

    [JsonPropertyName("lotTracking")]
    public string LotTracking { get; set; }

    [JsonPropertyName("market")]
    public string Market { get; set; }

    [JsonPropertyName("missingPrepCheck")]
    public string MissingPrepCheck { get; set; }

    [JsonPropertyName("optimalDaysOnHand")]
    public int OptimalDaysOnHand { get; set; }
    [JsonPropertyName("ownershipEntity")]
    public string OwnershipEntity { get; set; }

    [JsonPropertyName("posFilePath")]
    public string PosFilePath { get; set; }

    [JsonPropertyName("posType")]
    public string PosType { get; set; }

    [JsonPropertyName("prefix")]
    public string Prefix { get; set; }

    [JsonPropertyName("productionQuantityAlertPercent")]
    public int ProductionQuantityAlertPercent { get; set; }

    [JsonPropertyName("purchaseType")]
    public string PurchaseType { get; set; }

    [JsonPropertyName("secMrktPurchase")]
    public string SecMrktPurchase { get; set; }

    [JsonPropertyName("smartPicklist")]
    public string SmartPicklist { get; set; }

    [JsonPropertyName("software")]
    public string Software { get; set; }

    [JsonPropertyName("timezone")]
    public int Timezone { get; set; }
}

public class LocationHoursOfOperationDetail
{
    [JsonPropertyName("closingTime")]
    public string ClosingTime { get; set; }

    [JsonPropertyName("closingTimeCleanup")]
    public string ClosingTimeCleanup { get; set; }

    [JsonPropertyName("corporateFlag")]
    public string CorporateFlag { get; set; }

    [JsonPropertyName("dayOfWeek")]
    public int DayOfWeek { get; set; }

    [JsonPropertyName("locationCode")]
    public string LocationCode { get; set; }

    [JsonPropertyName("openingTime")]
    public string OpeningTime { get; set; }

    [JsonPropertyName("openingTimeSetup")]
    public string OpeningTimeSetup { get; set; }

    [JsonPropertyName("prefix")]
    public string Prefix { get; set; }
}

public class LocationLaborDetail
{
    [JsonPropertyName("businessDayBegins")]
    public string BusinessDayBegins { get; set; }

    [JsonPropertyName("corporateFlag")]
    public string CorporateFlag { get; set; }

    [JsonPropertyName("locationCode")]
    public string LocationCode { get; set; }

    [JsonPropertyName("prefix")]
    public string Prefix { get; set; }
}

public class LocationNameAddressDetail
{
    [JsonPropertyName("activeFlag")]
    public string ActiveFlag { get; set; }

    [JsonPropertyName("addressOne")]
    public string AddressOne { get; set; }

    [JsonPropertyName("addressTwo")]
    public string AddressTwo { get; set; }

    [JsonPropertyName("city")]
    public string City { get; set; }

    [JsonPropertyName("country")]
    public string Country { get; set; }

    [JsonPropertyName("eMail")]
    public string EMail { get; set; }

    [JsonPropertyName("fax")]
    public string Fax { get; set; }

    [JsonPropertyName("franchiseCode")]
    public string FranchiseCode { get; set; }

    [JsonPropertyName("locationADRemoteName")]
    public string LocationADRemoteName { get; set; }

    [JsonPropertyName("locationCode")]
    public string LocationCode { get; set; }

    [JsonPropertyName("locationName")]
    public string LocationName { get; set; }

    [JsonPropertyName("phone")]
    public string Phone { get; set; }

    [JsonPropertyName("prefix")]
    public string Prefix { get; set; }

    [JsonPropertyName("stateProvince")]
    public string StateProvince { get; set; }

    [JsonPropertyName("zip")]
    public string Zip { get; set; }
}

public class LocationPostDetail
{
    [JsonPropertyName("enabledFlag")]
    public string EnabledFlag { get; set; }

    [JsonPropertyName("locationCode")]
    public string LocationCode { get; set; }

    [JsonPropertyName("postingGrp")]
    public string PostingGrp { get; set; }

    [JsonPropertyName("prefix")]
    public string Prefix { get; set; }
}

public class LocationTimeClockInTwxDetail
{
    [JsonPropertyName("allowTimeClockInTWX")]
    public bool AllowTimeClockInTWX { get; set; }

    [JsonPropertyName("locationCode")]
    public string LocationCode { get; set; }

    [JsonPropertyName("minutesEarly")]
    public int MinutesEarly { get; set; }

    [JsonPropertyName("minutesLate")]
    public int MinutesLate { get; set; }

    [JsonPropertyName("prefix")]
    public string Prefix { get; set; }
}

public class LocationUdaDetail
{
    [JsonPropertyName("dataType")]
    public string DataType { get; set; }

    [JsonPropertyName("lastTouchDate")]
    public long LastTouchDate { get; set; }

    [JsonPropertyName("locationCode")]
    public string LocationCode { get; set; }

    [JsonPropertyName("prefix")]
    public string Prefix { get; set; }

    [JsonPropertyName("replicatedFlag")]
    public bool ReplicatedFlag { get; set; }

    [JsonPropertyName("requiredFlag")]
    public string RequiredFlag { get; set; }

    [JsonPropertyName("udaCode")]
    public string UdaCode { get; set; }

    [JsonPropertyName("udaDescription")]
    public string UdaDescription { get; set; }

    [JsonPropertyName("udaGroup")]
    public string UdaGroup { get; set; }

    [JsonPropertyName("udaType")]
    public string UdaType { get; set; }

    [JsonPropertyName("udaValue")]
    public string UdaValue { get; set; }
}

// ... existing code ...

public class CrunchTimeEmployee
{
    [JsonPropertyName("address1")]
    public string Address1 { get; set; }

    [JsonPropertyName("address2")]
    public string Address2 { get; set; }

    [JsonPropertyName("age")]
    [JsonIgnore(Condition = JsonIgnoreCondition.WhenWritingDefault)]
    public int Age { get; set; }

    [JsonPropertyName("allergies")]
    public string Allergies { get; set; }

    [JsonPropertyName("breakWaiver")]
    public string BreakWaiver { get; set; }

    [JsonPropertyName("broadcastSchedule")]
    public string BroadcastSchedule { get; set; }

    [JsonPropertyName("city")]
    public string City { get; set; }

    [JsonPropertyName("country")]
    public string Country { get; set; }

    [JsonPropertyName("createUser")]
    [JsonIgnore(Condition = JsonIgnoreCondition.WhenWritingDefault)]
    public int CreateUser { get; set; }

    [JsonPropertyName("dateCreate")]
    public string DateCreate { get; set; }

    [JsonPropertyName("dateEdit")]
    public string DateEdit { get; set; }

    [JsonPropertyName("dateEditUtc")]
    [JsonIgnore(Condition = JsonIgnoreCondition.WhenWritingDefault)]
    public string DateEditUtc { get; set; }

    [JsonPropertyName("dateHired")]
    public string DateHired { get; set; }

    [JsonPropertyName("dateOfBirth")]
    public string DateOfBirth { get; set; }

    [JsonPropertyName("dateTerminated")]
    public string DateTerminated { get; set; }

    [JsonPropertyName("editUser")]
    [JsonIgnore(Condition = JsonIgnoreCondition.WhenWritingDefault)]
    public int EditUser { get; set; }

    [JsonPropertyName("eligibleForRehire")]
    public string EligibleForRehire { get; set; }

    [JsonPropertyName("emailAddress")]
    public string EmailAddress { get; set; }

    [JsonPropertyName("emergencyContactName")]
    public string EmergencyContactName { get; set; }

    [JsonPropertyName("emergencyContactPhone")]
    public string EmergencyContactPhone { get; set; }

    [JsonPropertyName("employeeAudits")]
    public List<EmployeeAudit> EmployeeAudits { get; set; }

    [JsonPropertyName("employeeChecklistItem01")]
    public string EmployeeChecklistItem01 { get; set; }
    [JsonPropertyName("employeeChecklistItem02")]
    public string EmployeeChecklistItem02 { get; set; }

    [JsonPropertyName("employeeChecklistItem03")]
    public string EmployeeChecklistItem03 { get; set; }

    [JsonPropertyName("employeeLocations")]
    public List<EmployeeLocation> EmployeeLocations { get; set; }

    [JsonPropertyName("employeeNickname")]
    public string EmployeeNickname { get; set; }

    // LiteDB requires an Id field for the collection. Mapping it only in CrunchTime.Tool.Program, for example, does not help in another tool.
    [BsonId]
    [JsonPropertyName("employeeNumber")]
    public string EmployeeNumber { get; set; }

    [JsonPropertyName("employeePositions")]
    public List<EmployeePosition> EmployeePositions { get; set; }

    [JsonPropertyName("ethnicGroup")]
    public string EthnicGroup { get; set; }

    [JsonPropertyName("federalAdditionalWithholding")]
    [JsonIgnore(Condition = JsonIgnoreCondition.WhenWritingDefault)]
    public decimal FederalAdditionalWithholding { get; set; }

    [JsonPropertyName("federalExemptions")]
    [JsonIgnore(Condition = JsonIgnoreCondition.WhenWritingDefault)]
    public int FederalExemptions { get; set; }

    [JsonPropertyName("federalMaritalStatus")]
    public string FederalMaritalStatus { get; set; }

    [JsonPropertyName("firstName")]
    public string FirstName { get; set; }

    [JsonPropertyName("flexField01")]
    public string FlexField01 { get; set; }

    [JsonPropertyName("flexField02")]
    public string FlexField02 { get; set; }

    [JsonPropertyName("flexField03")]
    public string FlexField03 { get; set; }

    [JsonPropertyName("lastName")]
    public string LastName { get; set; }

    [JsonPropertyName("locationId")]
    public string LocationId { get; set; }

    [JsonPropertyName("locationName")]
    public string LocationName { get; set; }

    [JsonPropertyName("manager")]
    public string Manager { get; set; }

    [JsonPropertyName("mealProgramDeduction")]
    [JsonIgnore(Condition = JsonIgnoreCondition.WhenWritingDefault)]
    public decimal MealProgramDeduction { get; set; }

    [JsonPropertyName("middleName")]
    public string MiddleName { get; set; }

    [JsonPropertyName("minorLaborProgramCode")]
    public string MinorLaborProgramCode { get; set; }

    [JsonPropertyName("partTime")]
    public string PartTime { get; set; }

    [JsonIgnore(Condition = JsonIgnoreCondition.WhenWritingDefault)]
    [JsonPropertyName("payRate01")]
    public decimal PayRate01 { get; set; }

    [JsonIgnore(Condition = JsonIgnoreCondition.WhenWritingDefault)]
    [JsonPropertyName("payRate02")]
    public decimal PayRate02 { get; set; }

    [JsonIgnore(Condition = JsonIgnoreCondition.WhenWritingDefault)]
    [JsonPropertyName("payRate03")]
    public decimal PayRate03 { get; set; }

    [JsonIgnore(Condition = JsonIgnoreCondition.WhenWritingDefault)]
    [JsonPropertyName("payType")]
    public string PayType { get; set; }

    [JsonPropertyName("payrollIdNumber")]
    public string PayrollIdNumber { get; set; }

    [JsonPropertyName("phoneNumber")]
    public string PhoneNumber { get; set; }

    [JsonPropertyName("posId")]
    public string PosId { get; set; }
    [JsonIgnore(Condition = JsonIgnoreCondition.WhenWritingDefault)]
    [JsonPropertyName("positionCode01")]
    public string PositionCode01 { get; set; }

    [JsonPropertyName("positionCode02")]
    public string PositionCode02 { get; set; }

    [JsonPropertyName("positionCode03")]
    public string PositionCode03 { get; set; }

    [JsonPropertyName("postalCode")]
    public string PostalCode { get; set; }

    [JsonPropertyName("primaryLocationCode")]
    public string PrimaryLocationCode { get; set; }

    [JsonPropertyName("primaryPositionName")]
    public string PrimaryPositionName { get; set; }

    [JsonPropertyName("pto01")]
    public object Pto01 { get; set; }

    [JsonPropertyName("pto02")]
    public object Pto02 { get; set; }

    [JsonPropertyName("pto03")]
    public object Pto03 { get; set; }

    [JsonIgnore(Condition = JsonIgnoreCondition.WhenWritingDefault)]
    [JsonPropertyName("salaryAmount")]
    public decimal SalaryAmount { get; set; }

    [JsonPropertyName("sex")]
    public string Sex { get; set; }

    [JsonIgnore(Condition = JsonIgnoreCondition.WhenWritingDefault)]
    [JsonPropertyName("sickHours")]
    public decimal SickHours { get; set; }

    [JsonIgnore(Condition = JsonIgnoreCondition.WhenWritingDefault)]
    [JsonPropertyName("skillLevel01")]
    public int SkillLevel01 { get; set; }

    [JsonIgnore(Condition = JsonIgnoreCondition.WhenWritingDefault)]
    [JsonPropertyName("skillLevel02")]
    public int SkillLevel02 { get; set; }

    [JsonIgnore(Condition = JsonIgnoreCondition.WhenWritingDefault)]
    [JsonPropertyName("skillLevel03")]
    public int SkillLevel03 { get; set; }

#nullable enable
    [JsonPropertyName("socialSecurityNumber")]
    [JsonIgnore(Condition = JsonIgnoreCondition.WhenWritingDefault)]
    public string? SocialSecurityNumber { get; set; }
#nullable disable

    [JsonIgnore(Condition = JsonIgnoreCondition.WhenWritingDefault)]
    [JsonPropertyName("stateAdditionalWithholding")]
    public decimal StateAdditionalWithholding { get; set; }

    [JsonIgnore(Condition = JsonIgnoreCondition.WhenWritingDefault)]
    [JsonPropertyName("stateExemptions")]
    public int StateExemptions { get; set; }

    [JsonPropertyName("stateMaritalStatus")]
    public string StateMaritalStatus { get; set; }

    [JsonPropertyName("stateProvince")]
    public string StateProvince { get; set; }

    [JsonPropertyName("status")]
    public string Status { get; set; }

    [JsonPropertyName("statusCode")]
    public string StatusCode { get; set; }

    [JsonPropertyName("suiSdiTaxJurisdictionCode")]
    public string SuiSdiTaxJurisdictionCode { get; set; }

    [JsonPropertyName("terminationReason")]
    public string TerminationReason { get; set; }

    [JsonIgnore(Condition = JsonIgnoreCondition.WhenWritingDefault)]
    [JsonPropertyName("uniformDeductionAmount")]
    public decimal UniformDeductionAmount { get; set; }

    [JsonPropertyName("unionMembership")]
    public string UnionMembership { get; set; }

    [JsonPropertyName("wageMatrixRow01")]
    [JsonIgnore(Condition = JsonIgnoreCondition.WhenWritingDefault)]
    public int WageMatrixRow01 { get; set; }

    [JsonIgnore(Condition = JsonIgnoreCondition.WhenWritingDefault)]
    [JsonPropertyName("wageMatrixRow02")]
    public int WageMatrixRow02 { get; set; }

    [JsonIgnore(Condition = JsonIgnoreCondition.WhenWritingDefault)]
    [JsonPropertyName("wageMatrixRow03")]
    public int WageMatrixRow03 { get; set; }

    public bool IsSalaried() { return PayType != "H"; }
}

public class EmployeeAudit
{
    [JsonPropertyName("auditDate")]
    public DateTime AuditDate { get; set; }

    [JsonPropertyName("changeType")]
    public string ChangeType { get; set; }

    [JsonPropertyName("changeValue")]
    public string ChangeValue { get; set; }

    [JsonPropertyName("editValue")]
    public string EditValue { get; set; }

    [JsonPropertyName("fieldName")]
    public string FieldName { get; set; }

    [JsonPropertyName("id")]
    public int Id { get; set; }

    [JsonPropertyName("originalValue")]
    public string OriginalValue { get; set; }

    [JsonPropertyName("sectionName")]
    public string SectionName { get; set; }

    [JsonPropertyName("userFirstName")]
    public string UserFirstName { get; set; }

    [JsonPropertyName("userId")]
    public string UserId { get; set; }

    [JsonPropertyName("userLastName")]
    public string UserLastName { get; set; }

    [JsonPropertyName("valueDataType")]
    public string ValueDataType { get; set; }
}

public class EmployeeLocation
{
    [JsonIgnore(Condition = JsonIgnoreCondition.WhenWritingDefault)]
    [JsonPropertyName("activeFlag")]
    public string ActiveFlag { get; set; }

    [JsonPropertyName("allocateLaborTo")]
    public string AllocateLaborTo { get; set; }

    [JsonPropertyName("deleteFlag")]
    public string DeleteFlag { get; set; }

    [JsonPropertyName("employeeNumber")]
    public string EmployeeNumber { get; set; }

    [JsonPropertyName("id")]
    [JsonIgnore(Condition = JsonIgnoreCondition.WhenWritingDefault)]
    public int Id { get; set; }

    [JsonIgnore(Condition = JsonIgnoreCondition.WhenWritingDefault)]
    [JsonPropertyName("locationCityName")]
    public string LocationCityName { get; set; }

    [JsonPropertyName("locationCode")]
    public string LocationCode { get; set; }

    [JsonIgnore(Condition = JsonIgnoreCondition.WhenWritingDefault)]
    [JsonPropertyName("locationMarketName")]
    public string LocationMarketName { get; set; }

    [JsonPropertyName("locationName")]
    public string LocationName { get; set; }

    [JsonPropertyName("locationStateName")]
    public string LocationStateName { get; set; }

    [JsonPropertyName("posExportFlag")]
    [JsonIgnore(Condition = JsonIgnoreCondition.WhenWritingDefault)]
    public bool PosExportFlag { get; set; }

    [JsonPropertyName("posId")]
    public string PosId { get; set; }

    [JsonPropertyName("primaryLocationFlag")]
    public bool PrimaryLocationFlag { get; set; }
}

public class EmployeePosition
{
    [JsonPropertyName("altLocationsFlag")]
    public string AltLocationsFlag { get; set; }

    [JsonPropertyName("deleteFlag")]
    public string DeleteFlag { get; set; }

    [JsonPropertyName("employeeNumber")]
    public string EmployeeNumber { get; set; }

    [JsonPropertyName("id")]
    public int Id { get; set; }

    [JsonPropertyName("payRate")]
    [JsonIgnore(Condition = JsonIgnoreCondition.WhenWritingDefault)]
    public decimal PayRate { get; set; }

    [JsonPropertyName("payRateDefault")]
    [JsonIgnore(Condition = JsonIgnoreCondition.WhenWritingDefault)]
    public decimal PayRateDefault { get; set; }

    [JsonPropertyName("payRateMaximum")]
    [JsonIgnore(Condition = JsonIgnoreCondition.WhenWritingDefault)]
    public decimal PayRateMaximum { get; set; }

    [JsonPropertyName("payRateMinimum")]
    [JsonIgnore(Condition = JsonIgnoreCondition.WhenWritingDefault)]
    public decimal PayRateMinimum { get; set; }

    [JsonPropertyName("payrollCode")]
    public string PayrollCode { get; set; }

    [JsonPropertyName("posCode")]
    public string PosCode { get; set; }

    [JsonPropertyName("positionCode")]
    public string PositionCode { get; set; }

    [JsonPropertyName("positionGlDescription")]
    public string PositionGlDescription { get; set; }

    [JsonPropertyName("positionGlSubstructure")]
    public string PositionGlSubstructure { get; set; }

    [JsonPropertyName("positionName")]
    public string PositionName { get; set; }

    [JsonPropertyName("positionPlGroupingCode")]
    public string PositionPlGroupingCode { get; set; }

    [JsonPropertyName("positionPlGroupingDesc")]
    public string PositionPlGroupingDesc { get; set; }

    [JsonPropertyName("primaryPositionFlag")]
    public string PrimaryPositionFlag { get; set; }

    [JsonPropertyName("skillLevelCode")]
    public string SkillLevelCode { get; set; }

    [JsonPropertyName("skillLevelDescription")]
    public string SkillLevelDescription { get; set; }

    [JsonPropertyName("wageMatrix")]

    public string WageMatrix { get; set; }
}

public class TimeClockEnhancedDetailDetails
{
    [JsonPropertyName("breakType")]
    public string BreakType { get; set; }

    [JsonPropertyName("cashSales")]
    public decimal CashSales { get; set; }

    [JsonPropertyName("cashTips")]
    public decimal CashTips { get; set; }

    [JsonPropertyName("chargeSales")]
    public decimal ChargeSales { get; set; }

    [JsonPropertyName("chargeTips")]
    public decimal ChargeTips { get; set; }

    [JsonPropertyName("clockIn")]
    public string ClockIn { get; set; }

    [JsonPropertyName("clockOut")]
    public string ClockOut { get; set; }

    [JsonPropertyName("editedBy")]
    public string EditedBy { get; set; }

    [JsonPropertyName("employeeNumber")]
    public string EmployeeNumber { get; set; }

    [JsonPropertyName("posCode")]
    public string PosCode { get; set; }
}

public class TimeClockEnhancedDetailsResponse
{
    [JsonPropertyName("timeClockEnhancedHeaderDetails")]
    public TimeClockEnhancedHeaderDetails TimeClockEnhancedHeaderDetails { get; set; }

    [JsonPropertyName("timeClockEnhancedDetailDetails")]
    public List<TimeClockEnhancedDetailDetails> TimeClockEnhancedDetailDetails { get; set; }
}

public class TimeClockEnhancedHeaderDetails
{
    [JsonPropertyName("locationCode")]
    public string LocationCode { get; set; }

    [JsonPropertyName("laborDate")]
    public string LaborDate { get; set; }

    [JsonPropertyName("importDataDateTime")]
    public string ImportDataDateTime { get; set; }
}

public class CrunchTimeEmployeeRequest
{
    [JsonPropertyName("employee")]
    public List<CrunchTimeEmployee> Employee { get; set; }

    [JsonPropertyName("franchiseSite")]
    public string FranchiseSite { get; set; }
}
public class SaveEmployeeResponse
{
    public int lineNumber { get; set; }
    public List<SaveEmployeeError> errors { get; set; }
    public string employeeId { get; set; }
    public string locationId { get; set; }
    public string statusCode { get; set; }
    public string locationName { get; set; }
    // Add other properties as needed
}

public class SaveEmployeeError
{
    public int lineNumber { get; set; }
    public int fieldNumber { get; set; }
    public string errorMessage { get; set; }
}
