﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text.Json;
using Payroll.Shared;
using Serilog;
using LiteDB;

namespace CrunchTime.Tool
{
    public class CacheCommand
    {
        public static readonly string POS_EMPLOYEES_CACHE_COLLECTION = "pos_employees";

        public void Clear(List<string> args)
        {
            if (args == null || args.Count < 1)
            {
                Console.WriteLine("Usage: DataCentral.Tool.exe cache clear [employees|locations]");
                return;
            }

            var scmd = args[0];
            switch (scmd)
            {
                // dont let users just pass any random collection name
                case "locations":
                    CacheService.RemoveRecords<Location>(AppConfig.POS_LOCATIONS_CACHE_COLLECTION);
                    break;
                case "employees":
                    CacheService.RemoveRecords<CrunchTimeEmployee>(AppConfig.POS_EMPLOYEES_CACHE_COLLECTION);
                    break;
                default:
                    Console.WriteLine("Error: unrecognized cache dump object: {0}", scmd);
                    break;
            }
        }

        public void Dump(List<string> args)
        {
            string entity = "employees";
            if (args != null && args.Count > 0)
            {
                entity = args[0];
            }

            try
            {
                using (var cache = CacheService.OpenCache(SystemConfig.CacheName()))
                {
                    if (entity == "employees")
                    {
                        var col = cache.GetCollection<CrunchTimeEmployee>(AppConfig.POS_EMPLOYEES_CACHE_COLLECTION);
                        var employees = col.Query().OrderBy(x => x.EmployeeNumber).ToList();
                        ConsoleService.PrintFormattedJson(employees);
                    }
                    else if (entity == "debug")
                    {
                        var col = cache.GetCollection(AppConfig.POS_EMPLOYEES_CACHE_COLLECTION);

                        foreach (var bsonDoc in col.Query().ToList())
                        {
                            var empNumber = bsonDoc["EmployeeNumber"].AsString;
                            foreach (var item in bsonDoc.Keys)
                            {
                                var value = bsonDoc[item];
                                Console.WriteLine("Item: {0}, Type: {1}", item, value.GetType());
                                Console.WriteLine("Value: {0}", LiteDB.JsonSerializer.Serialize(value));
                            }

                            try
                            {
                                var employee = BsonMapper.Global.Deserialize<CrunchTimeEmployee>(bsonDoc);
                                employee.EmployeePositions = null;
                                ConsoleService.PrintFormattedJson(employee);
                            }
                            catch (Exception e)
                            {
                                Console.WriteLine("Error deserializing employee: {0}", empNumber);
                                Console.WriteLine(e.Message);
                                Console.WriteLine(e.StackTrace);
                            }
                        }
                    }
                    else if (entity == "locations")
                    {
                        var col = cache.GetCollection<Payroll.Shared.Location>(AppConfig.POS_LOCATIONS_CACHE_COLLECTION);
                        var locations = col.Query().OrderBy(x => x.Code).ToList();
                        ConsoleService.PrintFormattedJson(locations);
                    }
                }
            }
            catch (Exception e)
            {
                Log.Logger.Fatal(e.Message);
                Log.Logger.Fatal(e.StackTrace);
            }
        }

        private void CacheLocations(IEnumerable<Payroll.Shared.Location> locations)
        {
            CacheService.CacheLastUpdatedTime(DateTime.Now);
            if (locations == null) return;
            CacheService.CacheRecords<Payroll.Shared.Location>(AppConfig.POS_LOCATIONS_CACHE_COLLECTION, locations);
        }

        private void CacheEmployees(IEnumerable<CrunchTimeEmployee> employees)
        {
            CacheService.CacheLastUpdatedTime(DateTime.Now);
            if (employees == null) return;
            CacheService.CacheRecords<CrunchTimeEmployee>(AppConfig.POS_EMPLOYEES_CACHE_COLLECTION, employees);
        }

        public int Employee(List<string> args)
        {
            if (args == null || args.Count < 1)
            {
                Console.WriteLine("Usage: CrunchTime.Tool.exe cache employee <eecode>");
                return 1;
            }

            var employeeNumber = args[0];
            try
            {
                using (var crunchTimeService = new CrunchTimeService())
                {
                    CrunchTimeEmployee employee = crunchTimeService.GetEmployeeDetailsAsync(employeeNumber, false).Result;
                    Log.Logger.Debug(System.Text.Json.JsonSerializer.Serialize(employee, new JsonSerializerOptions { WriteIndented = true }));

                    var payrollEmployee = Converter.ConvertEmployee(employee);
                    ConsoleService.PrintFormattedJson(payrollEmployee);
                }
            }
            catch (Exception e)
            {
                Log.Logger.Fatal(e.Message);
                Log.Logger.Fatal(e.StackTrace);
                return 1;
            }

            return 0;
        }

        public void Init(List<string> args)
        {
            // we don't want to use the cache here, because we want to update the cache with the latest data!
            var options = new CacheableFetchOptions { UseCache = false, IncludeTerminated = true };

            try
            {
                using (var crunchTimeService = new CrunchTimeService())
                {
                    var activeLocations = crunchTimeService.GetActiveLocationsAsync(options).Result;
                    CacheLocations(activeLocations);

                    var employees = crunchTimeService.GetAllEmployeesAsync(options).Result;
                    foreach (var location in employees.Keys)
                    {
                        CacheEmployees(employees[location].Values);
                    }
                }
            }
            catch (Exception e)
            {
                Log.Logger.Fatal(e.Message);
                Log.Logger.Fatal(e.StackTrace);
            }
        }

        // just an alias for Init
        public void Update(List<string> args)
        {
            Init(args);
        }

        public void Location(List<string> args)
        {
            if (args == null || args.Count < 1)
            {
                Console.WriteLine($"Usage: CrunchTime.Tool.exe cache location <location code>");
                return;
            }

            var locationCode = args[0];
            var options = new CacheableFetchOptions { UseCache = false };

            try
            {
                using var crunchTimeService = new CrunchTimeService();
                var activeLocations = crunchTimeService.GetActiveLocationsAsync().Result;
                if (locationCode != "all") activeLocations.RemoveAll(x => x.Code != locationCode);

                foreach (var location in activeLocations)
                {
                    Log.Logger.Debug($"Processing location: {location.Code}");
                    var employees = crunchTimeService.GetCrunchTimeEmployeesAsync(location.Code, options).Result;
                    CacheEmployees(employees.Values);
                }
            }
            catch (Exception e)
            {
                Log.Logger.Error(e, $"Error caching employees for location {locationCode}");
            }
        }

        public void Locations(List<string> args)
        {
            // we don't want to use the cache here, because we want to update the cache with the latest data!
            var options = new CacheableFetchOptions { UseCache = false };

            try
            {
                using (var crunchTimeService = new CrunchTimeService())
                {
                    var locations = crunchTimeService.GetLocationsAsync(options).Result;
                    CacheService.CacheRecords<Payroll.Shared.Location>(AppConfig.POS_LOCATIONS_CACHE_COLLECTION, locations);
                }
            }
            catch (Exception e)
            {
                Log.Logger.Fatal(e.Message);
                Log.Logger.Fatal(e.StackTrace);
            }
        }

        public void View(List<string> args)
        {
            if (args == null || args.Count < 1)
            {
                Console.WriteLine("Usage: CrunchTime.Tool.exe cache view <emp-id>");
                return;
            }

            var idString = args[0];

            try
            {
                var findEmp = CacheService.FetchRecord<CrunchTimeEmployee>(AppConfig.POS_EMPLOYEES_CACHE_COLLECTION, idString);
                if (findEmp == null)
                {
                    Console.WriteLine("Failed to find a cached pos employee with id: {0}", idString);
                    return;
                }

                var employees = new List<CrunchTimeEmployee>();
                employees.Add(findEmp);
                ConsoleService.PrintFormattedJson(employees);
            }
            catch (Exception e)
            {
                Log.Logger.Error(e.Message);
                Log.Logger.Error(e.StackTrace);
            }
        }

    }
}
