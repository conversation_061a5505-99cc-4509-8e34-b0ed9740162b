﻿using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using Serilog;
using Payroll.Shared;
using System.Text.Json;

namespace CrunchTime.Tool
{
    public class LocationCommand
    {
        private void PrintLocations(List<Payroll.Shared.Location> locations)
        {
            Console.WriteLine("Id  Code  TZ                 \tName                       \tActive\tDescription                         \tLat,Long");
            foreach (var location in locations)
            {
                Console.WriteLine($"{location.Id.PadRight(3)} {location.Code.PadLeft(5)} {location.TimeZone?.PadRight(15)}\t{location.Name?.PadRight(25)}\t{location.Active} \t{location.Description.PadRight(36)}\t{location.Latitude}, {location.Longitude}");
            }
        }

        public void View(List<string> args)
        {
            if (args == null || args.Count < 1)
            {
                Console.WriteLine("Usage: CrunchTime.Tool.exe location view <location-code>");
                return;
            }

            var locationCode = args[0];

            try
            {
                using var crunchTimeService = new CrunchTimeService();
                var location = crunchTimeService.GetLocationAsync(locationCode).Result;
                ConsoleService.PrintFormattedJson(location);
            }
            catch (Exception e)
            {
                Log.Logger.Error(e, "Error listing active locations");
            }
        }

        public void List(List<string> args)
        {
            try
            {
                using var crunchTimeService = new CrunchTimeService();
                var locations = crunchTimeService.GetActiveLocationsAsync().Result;
                PrintLocations(locations);
            }
            catch (Exception e)
            {
                Log.Logger.Error(e, "Error listing active locations");
            }
        }

        public void All(List<string> args)
        {
            try
            {
                using var crunchTimeService = new CrunchTimeService();
                var options = CacheableFetchOptions.Default();
                options.IncludeTerminated = true;

                var locations = crunchTimeService.GetLocationsAsync(options).Result;
                PrintLocations(locations);
            }
            catch (Exception e)
            {
                Log.Logger.Error(e, "Error listing all locations");
            }
        }

        public void Enable(List<string> args)
        {
            if (args == null || args.Count < 1)
            {
                Console.WriteLine("Usage: CrunchTime.Tool.exe location enable <location-code>");
                return;
            }

            try
            {
                using var crunchTimeService = new CrunchTimeService();
                var ctLocation = crunchTimeService.GetLocationAsync(args[0]).Result;
                var location = Converter.ConvertLocation(AppConfig.Instance, ctLocation);

                using (var locationService = new LocationService())
                {
                    var success = locationService.DecorateLocationWithTimeZoneInfo(AppConfig.POS_LOCATIONS_CACHE_COLLECTION, location).Result;
                    if (!success)
                    {
                        Log.Logger.Warning("Failed to decorate location {id} with timezone info", location.Id);
                    }
                }

                CacheService.CacheRecord<Payroll.Shared.Location>(AppConfig.POS_LOCATIONS_CACHE_COLLECTION, location);
            }
            catch (Exception e)
            {
                Log.Logger.Error(e, "Error enabling location {id}", args[0]);
                Log.Logger.Error("Be sure to use the location id, not the code!");
            }
        }

        public void Disable(List<string> args)
        {
            if (args == null || args.Count < 1)
            {
                Console.WriteLine("Usage: CrunchTime.Tool.exe location disable <location-id>");
                return;
            }

            try
            {
                var location = CacheService.FetchRecord<Payroll.Shared.Location>(AppConfig.POS_LOCATIONS_CACHE_COLLECTION, args[0]);
                location.Active = false;
                CacheService.CacheRecord<Payroll.Shared.Location>(AppConfig.POS_LOCATIONS_CACHE_COLLECTION, location);
            }
            catch (Exception e)
            {
                Log.Logger.Error(e, "Error disabling location {id}", args[0]);
                Log.Logger.Error("Be sure to use the location id, not the code!");
            }
        }
    }
}
