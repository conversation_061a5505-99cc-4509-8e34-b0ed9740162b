﻿using System;
using System.Collections.Generic;
using System.Linq;
using Payroll.Shared;
using Serilog;

namespace CrunchTime.Tool
{
    public class ExportCommand : PosExportCommand
    {
        public void Settings(List<string> args)
        {
            {
                var atoken = Environment.GetEnvironmentVariable(AppConfig.ENV_KEY_USER_API_KEY);
                Console.WriteLine($"export {AppConfig.ENV_KEY_USER_API_KEY}='{atoken}'");
            }
            {
                var atoken = Environment.GetEnvironmentVariable(AppConfig.ENV_KEY_EMP_API_KEY);
                Console.WriteLine($"export {AppConfig.ENV_KEY_EMP_API_KEY}='{atoken}'");
            }
            {
                var atoken = Environment.GetEnvironmentVariable(AppConfig.ENV_KEY_CLOCK_API_KEY);
                Console.WriteLine($"export {AppConfig.ENV_KEY_CLOCK_API_KEY}='{atoken}'");
            }
            {
                var atoken = Environment.GetEnvironmentVariable(AppConfig.ENV_KEY_LOCATION_API_KEY);
                Console.WriteLine($"export {AppConfig.ENV_KEY_LOCATION_API_KEY}='{atoken}'");
            }
            {
                var atoken = Environment.GetEnvironmentVariable(AppConfig.ENV_KEY_SITENAME);
                Console.WriteLine($"export {AppConfig.ENV_KEY_SITENAME}='{atoken}'");
            }
            {
                var atoken = Environment.GetEnvironmentVariable(AppConfig.ENV_KEY_USERNAME);
                Console.WriteLine($"export {AppConfig.ENV_KEY_USERNAME}='{atoken}'");
            }
            {
                var atoken = Environment.GetEnvironmentVariable(AppConfig.ENV_KEY_PASSWORD);
                Console.WriteLine($"export {AppConfig.ENV_KEY_PASSWORD}='{atoken}'");
            }
            {
                var atoken = Environment.GetEnvironmentVariable(AppConfig.ENV_KEY_TIMEZONE_API_KEY);
                Console.WriteLine($"export {AppConfig.ENV_KEY_TIMEZONE_API_KEY}='{atoken}'");
            }
        }

        private void PrintTimeCommandInfo()
        {
            Console.WriteLine("Usage: CrunchTime.Tool.exe export time <restaurant> <start-day> <end-day>");
            Console.WriteLine("  where: <restaurant> is the restaurant location code like MBMB or 'all' for all locations");
            Console.WriteLine(
                "  where: <start-day> and <end-day> are integers representing how many days prior to today");
            Console.WriteLine(
                "  e.g. 'CrunchTime.Tool.exe export time 39 7 1' would export time from 7 days ago till yesterday at the location with ID 39.");
        }

        public int Time(List<string> args)
        {
            if (args == null || args.Count < 3)
            {
                PrintTimeCommandInfo();
                return 1;
            }

            try
            {
                // initialize directories to reduce API calls
                using (var service = new CrunchTimeService())
                {
                    var locationArg = args[0].Trim();

                    var locations = service.GetActiveLocationsAsync().Result;
                    Log.Logger.Debug($"Locations: {string.Join(", ",
                        locations.Select(l => l.Code.Trim()).OrderBy(code => code))}");

                    if (locationArg != "all") locations.RemoveAll(x => x.Code != locationArg);

                    var allPunches = new List<PunchPair>();
                    BuildLocalDateWindowFromCommandLine(args[1], args[2], out DateTime startDate, out DateTime endDate);

                    // optional employee number, most likely used for testing
                    string employeeNumber = args.Count > 3 ? args[3] : null;

                    using (var locationService = new LocationService())
                    {
                        foreach (var location in locations)
                        {
                            Dictionary<DateOnly, TimeSpan> timeZoneOffsetByDay = locationService.GetTimeZoneOffsets(location, startDate, endDate);
                            Log.Logger.Debug($"Processing location: {location.Code}");
                            var timeClockDetailsByDate = new Dictionary<DateOnly, List<TimeClockEnhancedDetailDetails>>();

                            DateOnly sDate = DateOnly.FromDateTime(startDate);
                            DateOnly eDate = DateOnly.FromDateTime(endDate);
                            for (DateOnly currentDate = sDate; currentDate <= eDate; currentDate = currentDate.AddDays(1))
                            {
                                var punches = service.GetTimeClockDetailsAsync(location.Code, currentDate, employeeNumber).Result;
                                Log.Logger.Debug("Punches returned for {date}: {cnt}", currentDate.ToShortDateString(), punches.Count);

                                foreach (var p in punches)
                                {
                                    if (string.IsNullOrEmpty(p.EmployeeNumber))
                                    {
                                        Log.Logger.Warning("Skipping punch for employee {posCode} because it the employee number is null or empty", p.PosCode);
                                        continue;
                                    }

                                    var employee = service.GetEmployeeDetailsAsync(p.EmployeeNumber).Result;
                                    if (employee.IsSalaried())
                                    {
                                        Log.Logger.Warning("Skipping punch for employee {empNumber} because it is a salaried employee", p.EmployeeNumber);
                                        continue;
                                    }

                                    if (!timeClockDetailsByDate.ContainsKey(currentDate))
                                    {
                                        timeClockDetailsByDate[currentDate] = new List<TimeClockEnhancedDetailDetails>();
                                    }

                                    if (p.ClockIn == p.ClockOut)
                                    {
                                        Log.Logger.Warning("Skipping punch for employee {empNumber} on {date} because the clock in and clock out are the same", p.EmployeeNumber, currentDate.ToShortDateString());
                                        continue;
                                    }

                                    timeClockDetailsByDate[currentDate].Add(p);
                                }
                            }

                            var consolidatedPunches = new List<PunchPair>();
                            foreach (var laborDate in timeClockDetailsByDate.Keys)
                            {
                                var datePunches = timeClockDetailsByDate[laborDate];
                                var groupedPunches = datePunches.GroupBy(p => p.EmployeeNumber);

                                foreach (var group in groupedPunches)
                                {
                                    var employeeTimeClockDetails = group.OrderBy(p => p.ClockIn).ToList();
                                    TimeClockEnhancedDetailDetails firstTcd = employeeTimeClockDetails.First();

                                    while (employeeTimeClockDetails.Count > 0)
                                    {
                                        // pop first punch
                                        TimeClockEnhancedDetailDetails startingTcdForSequence = employeeTimeClockDetails.FirstOrDefault();
                                        employeeTimeClockDetails.Remove(startingTcdForSequence);

                                        var timeZoneOffset = timeZoneOffsetByDay[laborDate];
                                        var startingPunchForSequence =
                                            Converter.ConvertToPunchPair(laborDate, startingTcdForSequence, location, timeZoneOffset);
                                        consolidatedPunches.Add(startingPunchForSequence);

                                        // if it's the only punch for the day, or the next punch is not a break, move on
                                        TimeClockEnhancedDetailDetails possibleBreakTcd = employeeTimeClockDetails.FirstOrDefault();
                                        if (possibleBreakTcd == null || startingTcdForSequence.ClockOut != possibleBreakTcd.ClockIn || possibleBreakTcd.BreakType != "2")
                                            continue;

                                        // we are dealing with a break, remove it from the list
                                        employeeTimeClockDetails.Remove(possibleBreakTcd);

                                        // add the break to the punch
                                        var tcdTimeInDto = Converter.ParseDateTime(possibleBreakTcd.ClockIn, timeZoneOffset) ?? DateTimeOffset.MinValue;
                                        var tcdTimeOutDto = Converter.ParseDateTime(possibleBreakTcd.ClockOut, timeZoneOffset) ?? DateTimeOffset.MinValue;
                                        startingPunchForSequence.Breaks.Add(new Break()
                                        {
                                            TimeIn = tcdTimeInDto,
                                            TimeOut = tcdTimeOutDto
                                        });

                                        // when there is a break, the next punch is the close out the shift punch
                                        TimeClockEnhancedDetailDetails closeOutPunch = employeeTimeClockDetails.FirstOrDefault();
                                        if (closeOutPunch == null)
                                        {
                                            // assumption failure, this should not happen
                                            Log.Logger.Fatal("No close out punch for employee {empNumber} on {date}", startingTcdForSequence.EmployeeNumber, laborDate.ToShortDateString());
                                        }
                                        else
                                        {
                                            // add the close out punch to the punch
                                            if (possibleBreakTcd.ClockOut == closeOutPunch.ClockIn)
                                            {
                                                var closeOutTimeOut = Converter.ParseDateTime(closeOutPunch.ClockOut, timeZoneOffset) ?? DateTimeOffset.MinValue;
                                                startingPunchForSequence.TimeOut = closeOutTimeOut;
                                                employeeTimeClockDetails.Remove(closeOutPunch);
                                            }
                                        }
                                    }
                                }
                            }

                            allPunches.AddRange(consolidatedPunches);
                        }
                    }

                    ConsoleService.PrintFormattedJson(allPunches);
                }
            }
            catch (Exception e)
            {
                Log.Logger.Fatal(e.Message);
                Log.Logger.Fatal(e.StackTrace);
                return 1;
            }

            return 0;
        }
    }
}
