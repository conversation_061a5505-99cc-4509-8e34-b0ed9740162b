using System;
using System.Collections.Generic;
using System.Linq;
using System.Globalization;
using System.Text.RegularExpressions;
using Serilog;
using Payroll.Shared;

namespace CrunchTime.Tool;

static class Converter
{
    public static Payroll.Shared.Location ConvertLocation(AppConfig appConfig, CrunchTime.Tool.Location crunchTimeLocation)
    {
        var location = new Payroll.Shared.Location
        {
            Code = crunchTimeLocation.LocationCode,
        };

        var locationAltAddressDetail = crunchTimeLocation.LocationAltAddressDetails.FirstOrDefault();
        if (locationAltAddressDetail != null)
        {
            location.Id = locationAltAddressDetail.PayrollExportCode;
        }

        // If strict mode is not enabled, use the location code as the id if no payroll export code is found - clients may not have this data when we start
        if (!appConfig.StrictMode)
        {
            if (string.IsNullOrEmpty(location.Id))
            {
                location.Id = location.Code;
            }
        }

        var locAddrDetail = crunchTimeLocation.LocationNameAddressDetails.FirstOrDefault();
        if (locAddrDetail != null)
        {
            location.Name = locAddrDetail.LocationName;
            location.Active = locAddrDetail.ActiveFlag == "Y";
            location.Description = $"{locAddrDetail.City}, {locAddrDetail.StateProvince} - Franchise: {locAddrDetail.FranchiseCode}";

            location.StreetAddress = locAddrDetail.AddressOne;
            location.CityAddress = locAddrDetail.City;
            location.State = locAddrDetail.StateProvince;
            location.Zip = locAddrDetail.Zip;
        }

        // start with default timezone, lookup the timezone for the location using the store address later
        location.TimeZone = appConfig.DefaultTimeZone;
        return location;
    }

    public static List<Payroll.Shared.Location> ConvertLocations(AppConfig appConfig, IEnumerable<CrunchTime.Tool.Location> crunchTimeLocations)
    {
        return crunchTimeLocations.Select(location => ConvertLocation(appConfig, location)).ToList();
    }

    public static Payroll.Shared.Employee ConvertEmployee(CrunchTimeEmployee crunchTimeEmployee)
    {
        var employee = new Payroll.Shared.Employee
        {
            Id = crunchTimeEmployee.EmployeeNumber,
            FirstName = crunchTimeEmployee.FirstName,
            LastName = crunchTimeEmployee.LastName,
            StreetAddress = string.IsNullOrEmpty(crunchTimeEmployee.Address2)
                ? crunchTimeEmployee.Address1
                : $"{crunchTimeEmployee.Address1}, {crunchTimeEmployee.Address2}",
            CityAddress = crunchTimeEmployee.City,
            State = crunchTimeEmployee.StateProvince,
            Zip = crunchTimeEmployee.PostalCode,
            CellPhone = crunchTimeEmployee.PhoneNumber,
            WorkEmail = crunchTimeEmployee.EmailAddress,
            PrimaryWorkLocation = crunchTimeEmployee.PrimaryLocationCode,
            Description = crunchTimeEmployee.EmployeeNickname,
            HireDate = ParseDate(crunchTimeEmployee.DateHired),
            TermDate = ParseDate(crunchTimeEmployee.DateTerminated),
            DateOfBirth = ParseDate(crunchTimeEmployee.DateOfBirth),
            LastUpdated = ParseDate(crunchTimeEmployee.DateEdit),
            Active = crunchTimeEmployee.Status.Equals("Active", StringComparison.OrdinalIgnoreCase),
            Salaried = crunchTimeEmployee.IsSalaried()
        };

        // Add attributes
        employee.AddAttribute("EmployeeNumber", crunchTimeEmployee.EmployeeNumber);
        employee.AddAttribute("PayrollIdNumber", crunchTimeEmployee.PayrollIdNumber);
        employee.AddAttribute("PosId", crunchTimeEmployee.PosId);

        foreach (var position in crunchTimeEmployee.EmployeePositions)
        {
            employee.Jobs.Add(new Payroll.Shared.Job
            {
                Name = position.PositionName,
                Rate = position.PayRate,
                Code = position.PosCode,
                IsPrimary = position.PrimaryPositionFlag == "Y"
            });
        }

        return employee;
    }

    public static CrunchTimeEmployee ConvertToCrunchTimeEmployee(Payroll.Shared.Employee payrollEmployee)
    {

        Dictionary<string, string> attrs = payrollEmployee.Attributes;
        string posId = attrs?.GetValueOrDefault(key: "PosId") ?? "";

        var address1 = payrollEmployee.StreetAddress.Split(',').FirstOrDefault();
        var address2 = payrollEmployee.StreetAddress.Contains(",") ? payrollEmployee.StreetAddress.Split(',').LastOrDefault() : string.Empty;

        var crunchTimeEmployee = new CrunchTimeEmployee
        {
            EmployeeNumber = payrollEmployee.ClockSeq,
            FirstName = payrollEmployee.FirstName,
            LastName = payrollEmployee.LastName,
            Address1 = address1.Head(30),
            Address2 = address2.Head(30),
            City = payrollEmployee.CityAddress,
            StateProvince = payrollEmployee.State,
            PostalCode = payrollEmployee.Zip,
            PhoneNumber = payrollEmployee.OfficePhone,
            EmailAddress = string.IsNullOrEmpty(payrollEmployee.PersonalEmail) ? payrollEmployee.WorkEmail : payrollEmployee.PersonalEmail,
            //PrimaryLocationCode = payrollEmployee.PrimaryWorkLocation, //skip because it's part of locations
            //EmployeeNickname = payrollEmployee.Description,
            DateHired = payrollEmployee.HireDate?.ToString("MM/dd/yyyy"),
            DateTerminated = payrollEmployee.TermDate?.ToString("MM/dd/yyyy"),
            DateOfBirth = payrollEmployee.DateOfBirth?.ToString("MM/dd/yyyy"),
            DateEdit = payrollEmployee.LastUpdated?.ToString("MM/dd/yyyy"),
            Status = payrollEmployee.Active ? "Active" : "Inactive",
            PayType = payrollEmployee.Salaried ? "S" : "H",
            PayrollIdNumber = FieldService.GetPayrollId(payrollEmployee),

            PosId = posId,
            EmployeeLocations = new List<EmployeeLocation>
            {
                new EmployeeLocation
                {
                    LocationCode = payrollEmployee.PrimaryWorkLocation,
                    EmployeeNumber = payrollEmployee.ClockSeq, // jwr: added because the docs say it's required (https://developer.crunchtime.com/reference/saveemployeev1usingpost)
                    PrimaryLocationFlag = true
                }
            },
            EmployeePositions = payrollEmployee.Jobs.Select(job => new EmployeePosition
            {
                PositionCode = job.Code,
                PrimaryPositionFlag = job.Name == payrollEmployee.Jobs.FirstOrDefault()?.Name ? "Y" : "N",
                PayRate = Math.Round(job.Rate, 2),
                PosCode = job.Code
            }).ToList()
        };

        return crunchTimeEmployee;
    }

    public static Payroll.Shared.PunchPair ConvertToPunchPair(DateOnly laborDate, TimeClockEnhancedDetailDetails timeClockDetail, Payroll.Shared.Location location, TimeSpan timeZoneOffset)
    {
        var punchPair = new Payroll.Shared.PunchPair
        {
            ClockSeq = timeClockDetail.EmployeeNumber,
            EECode = timeClockDetail.PosCode,
            Date = laborDate.ToDateTime(TimeOnly.MinValue),
            TimeIn = ParseDateTime(timeClockDetail.ClockIn, timeZoneOffset) ?? DateTimeOffset.MinValue,
            TimeOut = ParseDateTime(timeClockDetail.ClockOut, timeZoneOffset) ?? DateTimeOffset.MinValue,
            JobCode = timeClockDetail.PosCode,
            NonCashTip = timeClockDetail.ChargeTips,
            CashTip = timeClockDetail.CashTips,
            Sales = timeClockDetail.CashSales + timeClockDetail.ChargeSales,
            Description = $"Exported on {DateTime.Now.ToString("MM/dd/yyyy")} at {DateTime.Now.ToString("HH:mm")}",
            TimeZone = location.TimeZone,
            Location = location.Code
        };

        // Calculate hours worked
        if (punchPair.TimeIn != DateTimeOffset.MinValue && punchPair.TimeOut != DateTimeOffset.MinValue)
        {
            punchPair.Hours = (decimal)(punchPair.TimeOut - punchPair.TimeIn).TotalHours;
        }

        return punchPair;
    }

    private static DateTime? ParseDate(string dateString)
    {
        if (string.IsNullOrEmpty(dateString))
        {
            return null;
        }

        if (DateTime.TryParseExact(dateString, "MM/dd/yyyy", CultureInfo.InvariantCulture, DateTimeStyles.None, out DateTime result))
        {
            return result;
        }

        return null;
    }

    public static DateTimeOffset? ParseDateTime(string dateTimeString, TimeSpan tzOffset)
    {
        var numbers = Regex.Matches(dateTimeString, @"\d+")
                           .Cast<Match>()
                           .Select(m => int.Parse(m.Value))
                           .ToList();

        if (numbers.Count != 5)
        {
            Log.Error("Unknown date time string: {dateTimeString}", dateTimeString);
            return null;
        }

        int hour = numbers[0];
        int minute = numbers[1];
        int month = numbers[2];
        int day = numbers[3];
        int year = numbers[4];

        try
        {
            var dateTime = new DateTime(year, month, day, hour, minute, 0);
            DateTimeOffset dateTimeOffset = new DateTimeOffset(dateTime, tzOffset);
            return dateTimeOffset;
        }
        catch (Exception ex)
        {
            Log.Error(ex, "Error parsing date time string: {dateTimeString}", dateTimeString);
            Log.Error("Year: {year}, Month: {month}, Day: {day}, Hour: {hour}, Minute: {minute}", year, month, day, hour, minute);
            return null;
        }
    }
}

