﻿using Payroll.Shared;
using System;
using System.Collections.Generic;
using System.Net;
using System.Text;

namespace CrunchTime.Tool
{
    public static class FieldService
    {
        public readonly static string FieldSection = "crunchtime_fields";
        public static string PayrollIdField = "id"; // payroll_id in config

        static FieldService()
        {
            Setting.Init();
            PayrollIdField = Setting.Get(FieldSection, "payroll_id").ToLower();
        }

        public static string GetPayrollId(Employee record)
        {
            switch (PayrollIdField)
            {
                case "id":
                    return record.Id;
                case "clockseq":
                    return record.ClockSeq;
                default:
                    return record.Id;
            }
        }
    }
}
