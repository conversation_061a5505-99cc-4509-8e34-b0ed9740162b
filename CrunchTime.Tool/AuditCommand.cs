using System;
using System.Collections.Generic;
using Payroll.Shared;
using Serilog;

namespace CrunchTime.Tool;

public class AuditCommand
{
    public int Employees(List<string> args)
    {
        try
        {
            var employees = CacheService.FetchRecords<CrunchTimeEmployee>(AppConfig.POS_EMPLOYEES_CACHE_COLLECTION);

            Console.WriteLine("Employees Missing PayrollIdNumber:");
            foreach (var emp in employees)
            {
                if (string.IsNullOrEmpty(emp.PayrollIdNumber))
                {
                    Console.WriteLine("PayrollIdNumber '{0}' does not match EmployeeNumber '{1}', for employee {2} {3}",
                    emp.PayrollIdNumber, emp.EmployeeNumber, emp.FirstName, emp.LastName);
                }
            }

            Console.WriteLine("Employees with PayrollIdNumber but not matching EmployeeNumber:");
            foreach (var emp in employees)
            {
                if (string.IsNullOrEmpty(emp.PayrollIdNumber))
                    continue;

                // this output is client specific but maybe useful in the future with different language...
                if (emp.PayrollIdNumber != emp.EmployeeNumber)
                    Console.WriteLine("EmployeeNumber '{1}' does not match Paycom ClockSeq '{0}' for employee {2} {3}",
                    emp.PayrollIdNumber, emp.EmployeeNumber, emp.FirstName, emp.LastName);
            }
        }
        catch (Exception e)
        {
            Log.Logger.Error(e.Message);
            return -1;
        }

        return 0;
    }
}