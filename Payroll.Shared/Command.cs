using Newtonsoft.Json;
using Serilog;
using System;
using System.Collections.Generic;
using System.Reflection;
using System.Text;

namespace Payroll.Shared
{
    static public class Command<T> where T : new()
    {
        static public int Invoke(ProgramArguments programArguments)
        {
            var ca = new CommandArguments()
            {
                Verb = programArguments.Verb,
                Arguments = programArguments.Arguments
            };

            return Invoke(ca);
        }

        static public int Invoke(CommandArguments command)
        {
            if (string.IsNullOrEmpty(command.Verb))
            {
                Log.Logger.Debug("No command name provided...");
                command.Verb = "info";
                Log.Logger.Debug("Defaulting to command {cmd}", command.Verb);
            }

            var invokable = new T();
            Type thisType = invokable.GetType();

            string methodName = System.Threading.Thread.CurrentThread.CurrentCulture.
                TextInfo.ToTitleCase(command.Verb.ToLower());

            MethodInfo theMethod = thisType.GetMethod(methodName);
            if (theMethod == null)
            {
                var baseType = thisType.BaseType;
                theMethod = thisType.BaseType.GetMethod(methodName);

                if (theMethod == null)
                {
                    Log.Logger.Fatal("Failed to find method {m} for type {t} or parent {p}",
                        methodName, thisType.Name, baseType.Name);
                    return 1; // error
                }
            }

            var args = new List<string>();
            if (command.Arguments != null)
            {
                args.AddRange(command.Arguments);
            }

            var rc = theMethod.Invoke(invokable, new object[] { args });
            if (rc != null && rc.GetType() == typeof(int)) return (int)rc;

            return 0;
        }
    }

}
