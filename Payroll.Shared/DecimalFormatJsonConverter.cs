using System;
using System.Text.Json;
using System.Text.Json.Serialization;

namespace Payroll.Shared
{
    public class DecimalFormatJsonConverter : JsonConverter<decimal>
    {
        public override decimal Read(ref Utf8JsonReader reader, Type typeToConvert, JsonSerializerOptions options)
        {
            if (reader.TokenType == JsonTokenType.Number)
            {
                return reader.GetDecimal();
            }
            
            if (reader.TokenType == JsonTokenType.String)
            {
                string stringValue = reader.GetString();
                if (decimal.TryParse(stringValue, out decimal value))
                {
                    return value;
                }
            }
            
            throw new JsonException($"Unable to convert {reader.TokenType} to Decimal");
        }

        public override void Write(Utf8JsonWriter writer, decimal value, JsonSerializerOptions options)
        {
            // Format with at most 2 decimal places
            writer.WriteStringValue(value.ToString("0.##"));
        }
    }
}
