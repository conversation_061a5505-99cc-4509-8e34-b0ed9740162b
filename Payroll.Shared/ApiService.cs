using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading;
using Serilog;

namespace Payroll.Shared;

public class ApiService : IDisposable
{
    private readonly int ApiDelayInSeconds = 0;
    private uint TotalApiRequestsMade { get; set; } = 0;
    private DateTime StartTime { get; set; } = DateTime.Now;
    private Dictionary<string, uint> ApiCallCounter { get; set; } = new Dictionary<string, uint>();

    public void LogApiRequest(string endpoint)
    {
        TotalApiRequestsMade++;

        if (ApiCallCounter.ContainsKey(endpoint))
        {
            ApiCallCounter[endpoint]++;
        }
        else
        {
            ApiCallCounter.Add(endpoint, 1);
        }

        if (ApiDelayInSeconds > 0) Thread.Sleep(ApiDelayInSeconds * 1000);
    }

    void IDisposable.Dispose()
    {
        var endTime = DateTime.Now;
        var totalTime = endTime - StartTime;
        var requestsPerSec = TotalApiRequestsMade / totalTime.TotalSeconds;

        // update cache stats
        CacheService.CacheStats(ApiCallCounter.ToList());

        Log.Logger.Information("Total Api Requests Made = {total}", TotalApiRequestsMade);
        Log.Logger.Information("Elapsed Time in Seconds = {time}", totalTime.TotalSeconds);

        if (requestsPerSec > 15)
            Log.Logger.Warning("Requests Per Second     = {stat}", requestsPerSec);
        else
            Log.Logger.Debug("Requests Per Second     = {stat}", requestsPerSec);
    }
}
