using System;
using System.Collections.Generic;
using Serilog;

namespace Payroll.Shared;

public static class TimeZoneInfoExtensions
{
    public static TimeZoneInfo TimeZoneInfoForAbbreviation(string timeZoneAbbreviation)
    {
        var timeZoneMap = new Dictionary<string, string>(StringComparer.OrdinalIgnoreCase)
        {
            { "EST", "Eastern Standard Time" },
            { "EDT", "Eastern Standard Time" },
            { "CST", "Central Standard Time" },
            { "CDT", "Central Standard Time" },
            { "MST", "Mountain Standard Time" },
            { "MDT", "Mountain Standard Time" },
            { "PST", "Pacific Standard Time" },
            { "PDT", "Pacific Standard Time" },
            { "AKST", "Alaskan Standard Time" },
            { "AKDT", "Alaskan Standard Time" },
            { "HST", "Hawaiian Standard Time" },
            { "HDT", "Hawaiian Standard Time" },
        };

        if (timeZoneMap.TryGetValue(timeZoneAbbreviation, out string windowsTimeZoneId))
        {
            try
            {
                return TimeZoneInfo.FindSystemTimeZoneById(windowsTimeZoneId);
            }
            catch (TimeZoneNotFoundException)
            {
                Log.Warning($"Time zone not found for abbreviation: {timeZoneAbbreviation}");
            }
        }
        else
        {
            Log.Warning($"Unknown time zone abbreviation: {timeZoneAbbreviation}");
        }

        // Default to UTC if the time zone is not recognized
        return TimeZoneInfo.Utc;
    }
}
