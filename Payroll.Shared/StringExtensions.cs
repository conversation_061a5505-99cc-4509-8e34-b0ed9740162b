using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Text.RegularExpressions;

namespace Payroll.Shared
{
    public static class StringExtensions
    {
        private static Random _random = new Random();

        public static string RemoveAllSpecialCharsExcept(this string input, string exceptChars)
        {
            var pattern = $"[^a-zA-Z0-9{exceptChars}]";
            return Regex.Replace(input, pattern, "");
        }

        public static string RemoveSpecialCharsExceptDash(this string input)
        {
            return Regex.Replace(input, "[^a-zA-Z0-9 -]", "");
        }

        public static string Head(this string source, int head_length)
        {
            if (head_length >= source.Length)
                return source;
            return source.Substring(0, head_length);
        }

        public static string RandomString(int length)
        {
            const string chars = "ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789";
            return new string(Enumerable.Repeat(chars, length)
              .Select(s => s[_random.Next(s.Length)]).ToArray());
        }

        public static string Sha1Hash(this string input)
        {
            if (String.IsNullOrEmpty(input)) return String.Empty;

            // Use input string to calculate MD5 hash
            using (System.Security.Cryptography.SHA1 sha1 = System.Security.Cryptography.SHA1.Create())
            {
                byte[] inputBytes = System.Text.Encoding.ASCII.GetBytes(input);
                byte[] hashBytes = sha1.ComputeHash(inputBytes);

                // Convert the byte array to hexadecimal string
                StringBuilder sb = new StringBuilder();
                for (int i = 0; i < hashBytes.Length; i++)
                {
                    sb.Append(hashBytes[i].ToString("X2"));
                }

                return sb.ToString();
            }
        }

	    public static string Tail(this string source, int tail_length)
        {
            if (tail_length >= source.Length)
                return source;
            return source.Substring(source.Length - tail_length);
        }

        public static string ToProperCase(this string value)
        {
            if (string.IsNullOrEmpty(value))
            {
                return "";
            }

            return System.Threading.Thread.CurrentThread.CurrentCulture.
                            TextInfo.ToTitleCase(value.ToLower());
        }

        public static string ToProperCaseWithOddCaps(this string value)
        {
            if (string.IsNullOrEmpty(value))
            {
                return "";
            }

            string proper = System.Threading.Thread.CurrentThread.CurrentCulture.
                            TextInfo.ToTitleCase(value.ToLower());

            // handle a few limited special cases, just at the beginning of the string
            {
                var oddCaps = new string[] { "D'", "O'", "Mc" };
                foreach (var oddCap in oddCaps)
                {
                    if (proper.StartsWith(oddCap))
                    {
                        proper = oddCap + proper.Substring(oddCap.Length).ToProperCase();
                    }
                }
            }

            // handle a few limited special cases, just at the end of the string
            {
                var oddCaps = new string[] { " Ii", " Iii", " Iiii", " Iv" };
                foreach (var oddCap in oddCaps)
                {
                    if (proper.EndsWith(oddCap))
                    {
                        proper = proper.Substring(0, proper.Length - oddCap.Length) + oddCap.ToUpper();
                        break;
                    }
                }
            }

            return proper;
        }

        /// <summary>
        /// Truncates a string to a maximum length.
        /// </summary>
        /// <param name="value">The string to truncate.</param>
        /// <param name="length">The maximum length of the returned string.</param>
        /// <returns>The input string, truncated to <paramref name="length"/> characters.</returns>
        public static string Truncate(this string value, int length)
        {
            if (value == null)
                throw new ArgumentNullException("value");
            return value.Length <= length ? value : value.Substring(0, length);
        }

        public static int IndexOfAny(this string test, string[] values)
        {
            int first = -1;
            foreach (string item in values)
            {
                int i = test.IndexOf(item);
                if (i > 0)
                {
                    if (first > 0)
                    {
                        if (i < first)
                        {
                            first = i;
                        }
                    }
                    else
                    {
                        first = i;
                    }
                }
            }
            return first;
        }

        public static string EscapeCharacter(this string str, char characterToEscape, char escapeCharacter)
        {
            string newStr = "";

            foreach (char c in str)
            {
                if (c == characterToEscape)
                {
                    newStr += escapeCharacter;
                }
                newStr += c;
            }

            return newStr;
        }
    }
}
