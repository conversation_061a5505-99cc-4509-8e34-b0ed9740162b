﻿using System;
using System.Text.Json.Serialization;

namespace Payroll.Shared;

public class Location
{
    public string Id { get; set; }
    public string Name { get; set; }
    public string Code { get; set; }
    public string TimeZone { get; set; }
    public string Description { get; set; } // some description of this location

    [JsonIgnore(Condition = JsonIgnoreCondition.WhenWritingDefault)]
     public string StreetAddress { get; set; }
    
    [JsonIgnore(Condition = JsonIgnoreCondition.WhenWritingDefault)] 
    public string CityAddress { get; set; }
    
    [JsonIgnore(Condition = JsonIgnoreCondition.WhenWritingDefault)] 
    public string State { get; set; }

    [JsonIgnore(Condition = JsonIgnoreCondition.WhenWritingDefault)] 
    public string Zip { get; set; }

    [JsonIgnore(Condition = JsonIgnoreCondition.WhenWritingDefault)] 
    public double Latitude { get; set; }

    [JsonIgnore(Condition = JsonIgnoreCondition.WhenWritingDefault)] 
    public double Longitude { get; set; }

    public bool Active { get; set; }

    public bool ShouldSerializeActive()
    {
        // serialize it if true
        return Active;
    }
}
