using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.Text;
using MongoDB.Bson.Serialization.Attributes;
using MongoDB.Bson.Serialization.IdGenerators;

namespace Payroll.Shared
{
    public class PunchPair
    {
        public PunchPair()
        {
            Breaks = new List<Break>();
        }

        // Copy constructor
        public PunchPair(PunchPair other)
        {
            Id = other.Id;
            ClockSeq = other.ClockSeq;
            EECode = other.EECode;
            Date = other.Date;
            TimeIn = other.TimeIn;
            TimeOut = other.TimeOut;
            Location = other.Location;
            TimeZone = other.TimeZone;
            Created = other.Created;
            Modified = other.Modified;
            FirstName = other.FirstName;
            LastName = other.LastName;
            JobCode = other.JobCode;
            NonCashTip = other.NonCashTip;
            CashTip = other.CashTip;
            Sales = other.Sales;
            Description = other.Description;
            Breaks = new List<Break>(other.Breaks); // Create a new list with the same breaks
        }

        [BsonId(IdGenerator = typeof(StringObjectIdGenerator))]
        public string Id { get; set; }

        [JsonProperty(NullValueHandling = NullValueHandling.Ignore)]
        public string EECode { get; set; }
        public string ClockSeq { get; set; }
        
        public DateTimeOffset Date { get; set; }
        public DateTimeOffset TimeIn { get; set; }
        public DateTimeOffset TimeOut { get; set; }

        // to track when the punch was created/modified by the POS
        public DateTimeOffset Created { get; set; }
        public DateTimeOffset Modified { get; set; }

        // this field may not actually be in much use
        [JsonProperty(NullValueHandling = NullValueHandling.Ignore)]
        public string TimeZone { get; set; }

        // these are convenience fields
        [JsonProperty(NullValueHandling = NullValueHandling.Ignore)]
        public string FirstName { get; set; }

        [JsonProperty(NullValueHandling = NullValueHandling.Ignore)]
        public string LastName { get; set; }

        // job related
        public decimal Rate { get; set; }
        public string JobCode { get; set; }

        // tip related
        public decimal NonCashTip { get; set; }
        public decimal CashTip { get; set; }
        public decimal Sales { get; set; }
        public decimal ExceptionPay { get; set; }
        public decimal TotalPay { get; set; }

        public string Description { get; set; }

        [JsonProperty(NullValueHandling = NullValueHandling.Ignore)]
        public string Location { get; set; }

        public List<Break> Breaks { get; set; }

        public bool ShouldSerializeCreated()
        {
            return (Created != DateTime.MinValue);
        }

        public bool ShouldSerializeModified()
        {
            return (Modified != DateTime.MinValue);
        }

        public bool ShouldSerializeSales()
        {
            // don't serialize sales if zero
            return (Sales != 0);
        }

        public bool ShouldSerializeBreaks()
        {
            // serialize breaks if present
            return (Breaks != null && Breaks.Count > 0);
        }

        public bool ShouldSerializeExceptionPay()
        {
            // don't serialize non-cash tip if zero
            return (ExceptionPay != 0);
        }

        public bool ShouldSerializeNonCashTip()
        {
            // don't serialize non-cash tip if zero
            return (NonCashTip != 0);
        }

        public bool ShouldSerializeCashTip()
        {
            // don't serialize cash tip if zero
            return (CashTip != 0);
        }

        public bool ShouldSerializeTotalPay()
        {
            // don't serialize cash tip if zero
            return (TotalPay != 0);
        }

        public decimal Hours { get; set; }
        public bool ShouldSerializeHours()
        {
            // don't serialize hours if zero
            return (Hours != 0);
        }

        public decimal Overtime { get; set; }
        public bool ShouldSerializeOvertime()
        {
            // don't serialize overtime if zero
            return (Overtime != 0);
        }
    }
}
