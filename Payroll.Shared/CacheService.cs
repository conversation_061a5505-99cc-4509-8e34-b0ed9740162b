﻿using LiteDB;
using System;
using System.Collections.Generic;
using System.IO;
using Serilog;
using static System.Environment;
using System.Linq;
using System.Runtime.InteropServices;

namespace Payroll.Shared
{
    public static class CacheService
    {
        public readonly static string EMPLOYEE_CACHE = "employees";
        public readonly static string STAT_CACHE = "stats";

        static CacheService()
        {
            // setup database services...
            var mapper = BsonMapper.Global;
            mapper.Entity<Employee>().Id(x => x.Id);
            Log.Logger.Debug("CacheService initialized, entity mapping complete.");
        }

        public static LiteDatabase OpenCache(string name)
        {
            var connectionString = $"Filename={Path.Combine(DefaultCachePath(), name)}; Connection=Shared";
            return new LiteDatabase(connectionString);
        }

        public static string DefaultCachePath()
        {
            var appDataPath = SpecialFolder.CommonApplicationData;

            // you cannot write to CommonAppData path on osx, so use localappdata instead
            OperatingSystem os = Environment.OSVersion;
            if (os.Platform == PlatformID.Unix && os.VersionString.Contains("Darwin") || RuntimeInformation.IsOSPlatform(OSPlatform.OSX))
            {
                appDataPath = SpecialFolder.LocalApplicationData;
            }

            var commonAppDataPath = Environment.GetFolderPath(appDataPath);
            return Path.Combine(commonAppDataPath, "PayrollTools");
        }

        public static void CacheLastUpdatedTime(DateTime lastUpdatedTime)
        {
            CacheTimeStat("LastUpdatedTime", lastUpdatedTime);
        }

        public static void CacheLastRunTime(string label, DateTime lastRunTime)
        {
            var rtKey = $"LastRunTime_{label}";
            CacheTimeStat(rtKey, lastRunTime);
        }

        public static void CacheTimeStat(string statId, DateTime timeStat)
        {
            try
            {
                using (var cache = CacheService.OpenCache(SystemConfig.CacheName()))
                {
                    var statCache = cache.GetCollection<Stat>(STAT_CACHE);
                    var findStat = statCache.Find(x => x.Id == statId);

                    if (findStat.Any())
                    {
                        var oStat = findStat.First();
                        oStat.Value = timeStat.ToBinary();
                        statCache.Upsert(oStat);
                    }
                    else
                        statCache.Insert(new Stat { Id = statId, Value = timeStat.ToBinary() });

                    Log.Logger.Information("{statId} set to {x} ({y})", statId, timeStat.ToString("yyyy-MM-dd HH:mm:ss"), timeStat.ToBinary());
                }
            }
            catch (Exception e)
            {
                Log.Logger.Fatal(e.Message);
                Log.Logger.Fatal(e.StackTrace);
            }
        }

        public static void CacheStats(IEnumerable<KeyValuePair<string, uint>> stats)
        {
            if (stats == null) return;

            try
            {
                using (var cache = CacheService.OpenCache(SystemConfig.CacheName()))
                {
                    var statCache = cache.GetCollection<Stat>(STAT_CACHE);
                    foreach (var kvp in stats)
                    {
                        var findStat = statCache.Find(x => x.Id == kvp.Key);

                        if (findStat.Count() > 0)
                        {
                            var oStat = findStat.First();
                            oStat.Value += kvp.Value;
                            statCache.Upsert(oStat);
                        }
                        else
                            statCache.Insert(new Stat { Id = kvp.Key, Value = kvp.Value });
                    }

                    statCache.EnsureIndex(x => x.Id, true);
                }
            }
            catch (Exception e)
            {
                Log.Logger.Fatal(e.Message);
                Log.Logger.Fatal(e.StackTrace);
            }
        }

        public static void ClearEmployeeCache()
        {
            try
            {
                using (var cache = CacheService.OpenCache(SystemConfig.CacheName()))
                {
                    var col = cache.GetCollection(EMPLOYEE_CACHE);
                    col.DeleteAll();
                    Log.Logger.Information("Cleared '{0}' cache", EMPLOYEE_CACHE);
                }
            }
            catch (Exception e)
            {
                Log.Logger.Fatal(e.Message);
                Log.Logger.Fatal(e.StackTrace);
            }
        }

        public static DateTime? GetLastUpdatedTime()
        {
            return GetTimeStat("LastUpdatedTime");
        }

        public static DateTime? GetLastRunTime(string label)
        {
            var rtKey = $"LastRunTime_{label}";
            return GetTimeStat(rtKey);
        }

        public static DateTime? GetTimeStat(string statId)
        {
            long ticks = 0;
            if (string.IsNullOrEmpty(statId)) return null;

            try
            {
                using (var cache = CacheService.OpenCache(SystemConfig.CacheName()))
                {
                    ILiteCollection<Stat> statCache = cache.GetCollection<Stat>(STAT_CACHE);
                    var stat = statCache.FindById(statId);
                    if (stat == null)
                    {
                        Log.Logger.Warning("Failed to find {0} time, assuming first run", statId);
                        return null;
                    }

                    ticks = stat.Value;
                }
            }
            catch (Exception e)
            {
                Log.Logger.Fatal(e.Message);
                Log.Logger.Fatal(e.StackTrace);
            }

            return DateTime.FromBinary(ticks);
        }

        public static void CacheRecords<T>(string cacheName, IEnumerable<T> records)
        {
            if (records == null) return;

            try
            {
                using (var cache = CacheService.OpenCache(SystemConfig.CacheName()))
                {
                    var recordCache = cache.GetCollection<T>(cacheName);
                    foreach (var record in records)
                    {
                        recordCache.Upsert(record);
                    }
                }
            }
            catch (Exception e)
            {
                Log.Logger.Fatal(e.Message);
                Log.Logger.Fatal(e.StackTrace);
            }
        }

        public static void CacheRecord<T>(string cacheName, T record)
        {
            try
            {
                using (var cache = CacheService.OpenCache(SystemConfig.CacheName()))
                {
                    var recordCache = cache.GetCollection<T>(cacheName);
                    recordCache.Upsert(record);
                }
            }
            catch (Exception e)
            {
                Log.Logger.Fatal(e.Message);
                Log.Logger.Fatal(e.StackTrace);
            }
        }

        public static T FetchRecord<T>(string cacheName, string recordId)
        {
            T record = default(T);

            try
            {
                using (var cache = CacheService.OpenCache(SystemConfig.CacheName()))
                {
                    var recordCache = cache.GetCollection<T>(cacheName);
                    record = recordCache.FindById(recordId);
                    if (record == null)
                    {
                        Log.Logger.Debug("Failed to find record '{0}' in cache", recordId);
                    }
                }
            }
            catch (Exception e)
            {
                Log.Logger.Fatal("Failed to fetch employee {eid} from cache {cname}", recordId, cacheName);
                Log.Logger.Error(e.Message);
                Log.Logger.Error(e.StackTrace);
            }

            return record;
        }

        public static IEnumerable<T> FetchRecords<T>(string cacheName)
        {
            IEnumerable<T> records = null;

            try
            {
                using (var cache = CacheService.OpenCache(SystemConfig.CacheName()))
                {
                    var recordCache = cache.GetCollection<T>(cacheName);
                    records = recordCache.FindAll();
                    if (records == null)
                    {
                        Log.Logger.Debug("Failed to find records in cache");
                    }
                }
            }
            catch (Exception e)
            {
                Log.Logger.Fatal(e.Message);
                Log.Logger.Fatal(e.StackTrace);
            }

            return records;
        }

        public static void RemoveRecord<T>(string cacheName, string id)
        {
            try
            {
                using (var cache = CacheService.OpenCache(SystemConfig.CacheName()))
                {
                    var recordCache = cache.GetCollection<T>(cacheName);
                    var rc = recordCache.Delete(id);
                    if (rc) Log.Logger.Information("Removed record '{0}' from cache", id);
                }
            }
            catch (Exception e)
            {
                Log.Logger.Fatal(e.Message);
                Log.Logger.Fatal(e.StackTrace);
            }
        }

        public static void RemoveRecords<T>(string cacheName)
        {
            try
            {
                using (var cache = CacheService.OpenCache(SystemConfig.CacheName()))
                {
                    var col = cache.GetCollection(cacheName);
                    col.DeleteAll();
                }
            }
            catch (Exception e)
            {
                Log.Logger.Fatal(e.Message);
                Log.Logger.Fatal(e.StackTrace);
            }
        }
    }
}
