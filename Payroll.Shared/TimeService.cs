﻿using Payroll.Shared;
using Serilog;
using System;
using System.Collections.Generic;
using System.Net.Http;
using System.Text.Json;
using System.Text.Json.Serialization;
using System.Threading;
using System.Threading.Tasks;

namespace Payroll.Shared
{
    public class TimeService : IDisposable
    {
        private static int _sleepTime = 100;

        private readonly HttpClient _httpClient;

        public TimeService()
        {
            _httpClient = new HttpClient();
        }

        void IDisposable.Dispose()
        {
            _httpClient.Dispose();
        }

        public static string TimeZoneFromIanaName(string ianaCode)
        {
            switch (ianaCode)
            {
                case "America/New_York":
                case "America/Indiana/Indianapolis":
                    return "EST";
                case "America/Chicago":
                    return "CST";
                case "America/Denver":
                    return "MST";
                case "America/Los_Angeles":
                    return "PST";
                case "America/Phoenix":
                    return "MST";
            }

            return ianaCode;
        }

        public TimeSpan GetOffsetForTimeZoneAbbrev(string tzAbbreviation, DateTimeOffset dateTimeOffset)
        {
            var timeZoneInfo = TimeZoneInfo.FindSystemTimeZoneById(tzAbbreviation);
            return timeZoneInfo.GetUtcOffset(dateTimeOffset);
        }

        public async Task<DateTime> GetCurrentTimeForLocation(double latitude, double longitude)
        {
            return await GetTimeForLocation(latitude, longitude, DateTimeOffset.UtcNow);
        }

        public async Task<DateTime> GetTimeForLocation(double latitude, double longitude, DateTimeOffset dateTimeOffset)
        {
            var timeZoneInfo = await GetTimeInfoForLocation(latitude, longitude, dateTimeOffset);
            Log.Logger.Debug(JsonSerializer.Serialize(timeZoneInfo));

            if (timeZoneInfo.Status != "OK")
            {
                throw new Exception(timeZoneInfo.Status);
            }

            var utcTime = dateTimeOffset;
            var offsetSeconds = timeZoneInfo.RawOffset + timeZoneInfo.DstOffset;
            return utcTime.AddSeconds(offsetSeconds).DateTime;
        }

        public async Task<TimeZoneResponse> GetTimeInfoForLocation(double latitude, double longitude, DateTimeOffset dateTimeOffset)
        {
            long timestamp = dateTimeOffset.ToUnixTimeSeconds();
            string apiKey = SystemConfig.TimeZoneApiKey();
            
            string url = $"https://maps.googleapis.com/maps/api/timezone/json?location={latitude},{longitude}&timestamp={timestamp}&key={apiKey}";
            Log.Logger.Debug("Timezone URL: {url}", url);

            var response = await _httpClient.GetStringAsync(url);
            Thread.Sleep(_sleepTime);
            return JsonSerializer.Deserialize<TimeZoneResponse>(response);
        }

        public async Task<(double latitude, double longitude)> GetCoordinatesForLocation(Location location)
        {
            // Build the address string
            var addressComponents = new List<string>();
            if (!string.IsNullOrWhiteSpace(location.StreetAddress))
                addressComponents.Add(location.StreetAddress);
            if (!string.IsNullOrWhiteSpace(location.CityAddress))
                addressComponents.Add(location.CityAddress);
            if (!string.IsNullOrWhiteSpace(location.State))
                addressComponents.Add(location.State);
            if (!string.IsNullOrWhiteSpace(location.Zip))
                addressComponents.Add(location.Zip);

            string address = string.Join(",", addressComponents);
            
            if (string.IsNullOrWhiteSpace(address)) return (0, 0);
            
            string apiKey = SystemConfig.TimeZoneApiKey();
            string encodedAddress = Uri.EscapeDataString(address);
            
            string url = $"https://maps.googleapis.com/maps/api/geocode/json?address={encodedAddress}&key={apiKey}";
            Log.Logger.Debug("Geocoding URL: {url}", url);

            var response = await _httpClient.GetStringAsync(url);
            Thread.Sleep(_sleepTime);
            var result = JsonSerializer.Deserialize<GeocodingResponse>(response);

            if (result.Status != "OK" || result.Results.Length == 0)
            {
                throw new Exception($"Geocoding failed with status: {result.Status}");
            }

            var geometry = result.Results[0].Geometry;
            return (geometry.Location.Latitude, geometry.Location.Longitude);
        }
    }

    public class TimeZoneResponse
    {
        [JsonPropertyName("status")]
        public string Status { get; set; }

        [JsonPropertyName("rawOffset")]
        public int RawOffset { get; set; }

        [JsonPropertyName("dstOffset")]
        public int DstOffset { get; set; }

        [JsonPropertyName("timeZoneId")]
        public string TimeZoneId { get; set; }

        [JsonPropertyName("timeZoneName")]
        public string TimeZoneName { get; set; }
    }

    public class GeocodingResponse
    {
        [JsonPropertyName("results")]
        public GeocodingResult[] Results { get; set; }

        [JsonPropertyName("status")]
        public string Status { get; set; }
    }

    public class GeocodingResult
    {
        [JsonPropertyName("geometry")]
        public Geometry Geometry { get; set; }
    }

    public class Geometry
    {
        [JsonPropertyName("location")]
        public GeoLocation Location { get; set; }
    }

    public class GeoLocation
    {
        [JsonPropertyName("lat")]
        public double Latitude { get; set; }

        [JsonPropertyName("lng")]
        public double Longitude { get; set; }
    }
}