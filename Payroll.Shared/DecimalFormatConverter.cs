using System;
using Newtonsoft.Json;

namespace Payroll.Shared
{
    public class DecimalFormatConverter : JsonConverter
    {
        public override bool CanConvert(Type objectType)
        {
            return objectType == typeof(decimal);
        }

        public override void Write<PERSON><PERSON>(JsonWriter writer, object value, JsonSerializer serializer)
        {
            writer.WriteRawValue($"{value:0.##}");
        }

        public override bool CanRead => false;

        public override object Read<PERSON>son(JsonReader reader, Type objectType, object existingValue,
            JsonSerializer serializer)
        {
            throw new NotImplementedException();
        }
    }
}
