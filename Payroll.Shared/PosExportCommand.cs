using System;

namespace Payroll.Shared;

public abstract class PosExportCommand
{
    protected DateTimeOffset DateOffsetFromDaysInThePast(string daysBack)
    {
        var days = Convert.ToInt32(daysBack);

        // for debugging
        if (days == 0) return DateTimeOffset.UtcNow.Date.AddDays(1);

        return DateTimeOffset.UtcNow.Date.AddDays(-days);
    }

    protected void BuildDateOffsetWindowsFromCommandLine(string start, string end, out DateTimeOffset startDate, out DateTimeOffset endDate)
    {
        startDate = DateOffsetFromDaysInThePast(start);
        endDate = DateOffsetFromDaysInThePast(end);

        // set the end date to the very last second of the day...
        endDate += new TimeSpan(23, 59, 59);
    }

    protected DateTime DateFromDaysInThePast(string daysBack)
    {
        var days = Convert.ToInt32(daysBack);

        // for debugging
        if (days == 0) return DateTime.UtcNow.Date.AddDays(1);

        return DateTime.UtcNow.Date.AddDays(-days);
    }

    protected void BuildDateWindowsFromCommandLine(string start, string end, out DateTime startDate,
        out DateTime endDate)
    {
        startDate = DateFromDaysInThePast(start);
        endDate = DateFromDaysInThePast(end);

        // set the end date to the very last second of the day...
        endDate += new TimeSpan(23, 59, 59);
    }

    protected DateTime LocalDateFromDaysInThePast(string daysBack)
    {
        var days = Convert.ToInt32(daysBack);

        // for debugging
        if (days == 0) return DateTime.Now.Date.AddDays(1);

        return DateTime.Now.Date.AddDays(-days);
    }

    //  This is used by newer POS APIs that have a better sense of time!
    protected void BuildLocalDateWindowFromCommandLine(string start, string end, out DateTime startDate,
    out DateTime endDate)
    {
        startDate = LocalDateFromDaysInThePast(start);
        endDate = LocalDateFromDaysInThePast(end);

        // set the end date to the very last second of the day...
        endDate += new TimeSpan(23, 59, 59);
    }
}


