using System.Collections.Generic;

namespace Payroll.Shared
{
    public class RestaurantInfo
    {
        public string Id { get; set; }
        public string Code { get; set; }
    }

    public static class ConfigService
    {
        public static Dictionary<string, RestaurantInfo> RestaurantsById(string sectionName)
        {
            var map = new Dictionary<string, RestaurantInfo>();
            var section = Setting.ListSection(sectionName);

            foreach (var item in section)
            {
                var ri = new RestaurantInfo()
                {
                    Id = item.Key,
                    Code = item.Value
                };
                map.Add(item.Key, ri);
            }

            return map;
        }

        public static Dictionary<string, RestaurantInfo> RestaurantsByCode(string sectionName)
        {
            var map = new Dictionary<string, RestaurantInfo>();
            var section = Setting.ListSection(sectionName);

            foreach (var item in section)
            {
                var ri = new RestaurantInfo()
                {
                    Id = item.Key,
                    Code = item.Value
                };
                map.Add(item.Value, ri);
            }

            return map;
        }
    }
}