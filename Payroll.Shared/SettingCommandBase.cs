using System;
using System.Collections.Generic;

using Serilog;

namespace Payroll.Shared
{
    public abstract class SettingCommandBase
    {
        protected abstract string Category();

        public void Set(List<string> args)
        {
            var key = args[0];
            var val = (args.Count > 1) ? args[1] : "";

            Log.Logger.Information("Storing setting {key}={val}", key, val);
            Setting.Set(Category(), key, val);
        }

        public void Get(List<string> args)
        {
            var key = args[0];
            var val = Setting.Get(Category(), key);
            Console.WriteLine(val);
        }

        public virtual void List(List<string> args)
        {
            var settings = Setting.ListSection(Category());
            if (settings == null)
            {
                Log.Logger.Information("No {cat} settings found.", Category());
                return;
            }

            foreach (var kvp in settings)
            {
                Console.WriteLine($"{kvp.Key.PadRight(12)}\t{kvp.Value}");
            }
        }
    }
}