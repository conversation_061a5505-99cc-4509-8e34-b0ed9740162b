﻿using Serilog;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text.Json;

namespace Payroll.Shared
{
    public static class Converter
    {
        public static string FormatPhoneNumber(string value)
        {
            if (string.IsNullOrEmpty(value)) return string.Empty;
            value = new System.Text.RegularExpressions.Regex(@"\D")
                .Replace(value, string.Empty);
            value = value.TrimStart('1');
            if (value.Length == 7)
                return Convert.ToInt64(value).ToString("###-####");
            if (value.Length == 10)
                return Convert.ToInt64(value).ToString("###-###-####");
            if (value.Length > 10)
                return Convert.ToInt64(value)
                    .ToString("###-###-#### " + new String('#', (value.Length - 10)));
            return value;
        }

        public static Employee DirectoryEntryToEmployee(EmployeeDirectoryEntry ede)
        {
            var e = new Employee()
            {
                FirstName = ede.GivenName,
                LastName = ede.SurName,
                StreetAddress = ede.StreetAddress,
                CityAddress = ede.City,
                State = ede.State,
                Zip = ede.PostalCode,
                WorkEmail = ede.Email,
                OfficePhone = ede.OfficePhone,
                CellPhone = ede.MobilePhone,
                OrgName = ede.Company,
                LastUpdated = ede.LastUpdated,
                Id = ede.PrimaryKey,
                ClockSeq = ede.EmployeeId
            };

            e.Attributes = new Dictionary<string, string>();

            if (!string.IsNullOrEmpty(ede.MiddleName))
                e.AddAttribute("middle_name", ede.MiddleName);
            if (!string.IsNullOrEmpty(ede.DivisionCode))
                e.AddAttribute("division_code", ede.DivisionCode);
            if (!string.IsNullOrEmpty(ede.DivisionName))
                e.AddAttribute("division_name", ede.DivisionName);
            if (!string.IsNullOrEmpty(ede.Manager))
                e.AddAttribute("manager", ede.Manager);
            if (!string.IsNullOrEmpty(ede.BadgeNo))
                e.AddAttribute("badge_no", ede.BadgeNo);
            if (!string.IsNullOrEmpty(ede.PersonalTitle))
                e.AddAttribute("post_nominals", ede.PersonalTitle);

            return e;
        }

        public static EmployeeDirectoryEntry EmployeeToDirectoryEntry(Employee employee)
        {
            var cleanLastName = employee.LastName.RemoveSpecialCharsExceptDash();
            var cleanFirstName = employee.FirstName.RemoveSpecialCharsExceptDash();
            var fullName = $"{employee.LastName}, {employee.FirstName}";
            var currentTenureStart = employee.RehireDate > employee.HireDate ? employee.RehireDate : employee.HireDate;

            Dictionary<string, string> attrs = employee.Attributes;
            var divisionCode = attrs?.GetValueOrDefault("division_code") ?? "";
            var divisionName = attrs?.GetValueOrDefault("division_name") ?? "";
            var manager = attrs?.GetValueOrDefault("manager") ?? "";
            var businessTitle = attrs?.GetValueOrDefault("business_title") ?? "";
            var cityPhone = attrs?.GetValueOrDefault("city_phone") ?? "";
            var cityPhoneFmt = FormatPhoneNumber(cityPhone);
            var cityMobile = attrs?.GetValueOrDefault("city_mobile") ?? "";
            var postNominals = attrs?.GetValueOrDefault("post_nominals") ?? "";
            var middleName = attrs?.GetValueOrDefault("middle_name") ?? "";

            string samAccountName = "";
            if (!string.IsNullOrEmpty(employee.WorkEmail) && employee.WorkEmail.IndexOf('@') != -1)
            {
                string[] emailParts = employee.WorkEmail.Split('@');
                samAccountName = emailParts[0];
            }
            else
            {
                // some names might be two words, so we must remove spaces
                samAccountName = $"{cleanFirstName.ToProperCase()}.{cleanLastName.ToProperCase()}".Replace(" ", "");
            }

            var dEntry = new EmployeeDirectoryEntry()
            {
                GivenName = cleanFirstName,
                SurName = cleanLastName,
                MiddleName = middleName,
                Name = fullName,
                City = employee.CityAddress,
                Comment = $"{cleanFirstName.ToProperCase()} {cleanLastName.ToProperCaseWithOddCaps()}",
                Company = employee.OrgName,
                Department = employee.DeptName,
                Description = fullName,
                DivisionCode = divisionCode,
                DivisionName = divisionName,
                EmployeeId = employee.ClockSeq,
                HireDate = currentTenureStart,
                OfficePhone = cityPhoneFmt,
                Manager = manager,
                MobilePhone = FormatPhoneNumber(cityMobile),
                PostalCode = employee.Zip,
                State = employee.State,
                StreetAddress = employee.PrimaryWorkLocation,
                SamAccountName = samAccountName,
                Title = businessTitle,
                PersonalTitle = postNominals,
                PrimaryKey = employee.Id,
                LastUpdated = employee.LastUpdated
            };

            if (!string.IsNullOrEmpty(employee.CellPhone))
            {
                dEntry.ExtensionAttribute6 = employee.CellPhone.Tail(4);
            }

            if (employee.TermDate != null)
            {
                dEntry.ExtensionAttribute8 = employee.TermDate?.ToString("MM-dd-yyyy");
                dEntry.TermDate = employee.TermDate;
            }

            return dEntry;
        }
    }
}
