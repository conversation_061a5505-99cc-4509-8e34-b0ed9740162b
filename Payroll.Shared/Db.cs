using System;
using MongoDB.Driver;
using Serilog;

namespace Payroll.Shared
{
    static public class Db
    {
        static public IMongoDatabase Connect()
        {
            try
            {
                var dsn = SystemConfig.Dsn;
                var dbname = SystemConfig.DatabaseName;

                var client = new MongoClient(dsn);
                return client.GetDatabase(dbname);
            }
            catch (Exception e)
            {
                Log.Logger.Error(e.Message);
            }

            return null;
        }
    }
}