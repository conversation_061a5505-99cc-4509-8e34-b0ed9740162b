using System;
using System.Collections.Generic;
using System.Text;

namespace Payroll.Shared
{
    // a job+location pair
    public class JobKey : IComparable<JobKey>
    {
        public JobKey() { }

        public JobKey(Job job)
        {
            JobCode = job.Code;
            Location = job.Location;
        }

        public string JobCode { get; set; }
        public string Location { get; set; }

        public int CompareTo(JobKey that)
        {
            var c1 = String.Compare(this.JobCode, that.JobCode, StringComparison.Ordinal);
            if (c1 != 0) return c1;

            if (this.Location == null && that.Location == null)
                return 0;
            else if (this.Location == null)
                return -1;
            else if (that.Location == null)
                return 1;

            return String.Compare(this.Location, that.Location, StringComparison.Ordinal);
        }
        
        // Define the is greater than operator.
        public static bool operator >  (JobKey operand1, JobKey operand2)
        {
            return operand1.CompareTo(operand2) == 1;
        }
    
        // Define the is less than operator.
        public static bool operator <  (<PERSON><PERSON><PERSON> operand1, JobKey operand2)
        {
            return operand1.CompareTo(operand2) == -1;
        }

        // Define the is greater than or equal to operator.
        public static bool operator >=  (<PERSON>Key operand1, JobKey operand2)
        {
            return operand1.CompareTo(operand2) >= 0;
        }
    
        // Define the is less than or equal to operator.
        public static bool operator <=  (JobKey operand1, JobKey operand2)
        {
            return operand1.CompareTo(operand2) <= 0;
        }
    }
}
