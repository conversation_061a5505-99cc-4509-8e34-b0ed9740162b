﻿using Microsoft.Extensions.Primitives;
using Payroll.Shared;
using Serilog;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Net.Http;
using System.Text.Json;
using System.Text.Json.Serialization;
using System.Threading;
using System.Threading.Tasks;

namespace Payroll.Shared
{
    public class LocationService : IDisposable
    {
        private Dictionary<string, Dictionary<DateOnly, TimeSpan>> TimeZoneAdjByAbbrevByDay = new Dictionary<string, Dictionary<DateOnly, TimeSpan>>();
        private TimeService TimeService { get; set; } = new TimeService();

        public LocationService()
        {
        }

        void IDisposable.Dispose()
        {
        }

        public async Task<bool> DecorateLocationWithTimeZoneInfo(string cacheName, Location location)
        {
            // get cached location data if available
            bool success = true;

            var cachedLocation = CacheService.FetchRecords<Payroll.Shared.Location>(cacheName).FirstOrDefault(l => l.Id == location.Id);
            if (cachedLocation != null && cachedLocation.Latitude != 0 && cachedLocation.Longitude != 0)
            {
                location.Latitude = cachedLocation.Latitude;
                location.Longitude = cachedLocation.Longitude;
            }
            else
            {
                if (string.IsNullOrEmpty(location.StreetAddress) || string.IsNullOrEmpty(location.Zip))
                {
                    Log.Logger.Information("Skipping geocoding for location {id}, no address data...", location.Id);
                    success = false;
                }
                else
                {
                    var coordinates = await TimeService.GetCoordinatesForLocation(location);
                    location.Latitude = coordinates.latitude;
                    location.Longitude = coordinates.longitude;
                }
            }

            // no abbreviations please
            if (cachedLocation != null && !string.IsNullOrEmpty(cachedLocation.TimeZone) && cachedLocation.TimeZone.Length > 3)
                location.TimeZone = cachedLocation.TimeZone;
            else
            {
                if (location.Latitude == 0 || location.Longitude == 0)
                {
                    Log.Logger.Information("Skipping timezone lookup for location {id}, no coordinates...", location.Id);
                    success = false;
                }
                else
                {
                    var info = await TimeService.GetTimeInfoForLocation(location.Latitude, location.Longitude, DateTimeOffset.UtcNow);
                    location.TimeZone = info.TimeZoneId;
                }
            }

            CacheService.CacheRecord<Payroll.Shared.Location>(cacheName, location);
            return success;
        }

        public Dictionary<DateOnly, TimeSpan> GetTimeZoneOffsets(Location location, DateOnly sDate, DateOnly eDate)
        {
            if (TimeZoneAdjByAbbrevByDay.ContainsKey(location.TimeZone))
            {
                return TimeZoneAdjByAbbrevByDay[location.TimeZone];
            }

            var tz = TimeZoneInfo.FindSystemTimeZoneById(location.TimeZone);

            var startDateWithOffset
                = new DateTimeOffset(sDate.Year, sDate.Month, sDate.Day, 6, 0, 0, tz.BaseUtcOffset);
            var endDateWithOffset
                = new DateTimeOffset(eDate.Year, eDate.Month, eDate.Day, 6, 0, 0, tz.BaseUtcOffset);

            Dictionary<DateOnly, TimeSpan> timeZoneByDay = new Dictionary<DateOnly, TimeSpan>();
            for (DateTimeOffset i = startDateWithOffset; i <= endDateWithOffset; i = i.AddDays(1))
            {
                var when = i.AddHours(6); // switch over happens at 2am normally
                timeZoneByDay[DateOnly.FromDateTime(i.Date)] = tz.GetUtcOffset(when);
                Log.Logger.Debug("TZO offset: {0}, when: {1}, tz: {2}, tzname: {3}",
                    timeZoneByDay[DateOnly.FromDateTime(i.Date)].Hours, when, location.TimeZone, tz.Id);
            }

            TimeZoneAdjByAbbrevByDay[location.TimeZone] = timeZoneByDay;
            return timeZoneByDay;
        }

        public Dictionary<DateOnly, TimeSpan> GetTimeZoneOffsets(Location location, DateTimeOffset sDate, DateTimeOffset eDate)
        {
            return GetTimeZoneOffsets(location,
                DateOnly.FromDateTime(sDate.Date),
                DateOnly.FromDateTime(eDate.Date));
        }
    }
}