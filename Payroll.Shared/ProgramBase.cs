using Newtonsoft.Json;
using Serilog;
using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;

namespace Payroll.Shared
{
    public class ProgramConfig
    {
        public static void LoadOptionalConfig(string configOptionFileName)
        {
            if (string.IsNullOrEmpty(configOptionFileName)) return;

            string configFileName;

            // fullpath filename provided
            if (File.Exists(configOptionFileName))
            {
                configFileName = configOptionFileName;
            }
            else
            {
                var configPath = Payroll.Shared.Setting.DefaultSettingsPath();
                string fname;
                if (configOptionFileName.EndsWith(".ini"))
                    fname = configOptionFileName;
                else
                    fname = configOptionFileName + ".ini";

                configFileName = Path.Combine(configPath, fname);

                if (!File.Exists(configFileName))
                {
                    Log.Logger.Fatal("Configuration file {fname} does not exist, cowardly refusing to run.", configFileName);
                    Environment.Exit(-1);
                }
            }

            Setting.LoadIniData(configFileName);
            Log.Logger.Debug("Loading configuration file {fname}", configFileName);
        }
    }

    public class ProgramDriver<T> where T : new()
    {
        public static int Run(ProgramArguments programArguments)
        {
            // load optionally specified config file settings
            ProgramConfig.LoadOptionalConfig(programArguments.Config);
            Log.Logger.Debug(JsonConvert.SerializeObject(programArguments));

            if (programArguments.Arguments == null || !programArguments.Arguments.Any() &&
                string.IsNullOrEmpty(programArguments.Verb))
            {
                var command = new CommandArguments() { Verb = "Help" };
                Command<T>.Invoke(command);
                return 1;
            }

            return Command<T>.Invoke(programArguments);
        }
    }

    public abstract class ProgramBase <T> where T : new()
    {
        public abstract int ShowUsage();

        public static int InvokeCommand<PT>(PT _, ProgramArguments programArguments) where PT: new()
        {
            ProgramConfig.LoadOptionalConfig(programArguments.Config);

            // load optionally specified config file settings
            return Command<PT>.Invoke(programArguments);
        }

        public int Command(List<string> args)
        {
            var command = new CommandArguments(args);
            return Command<T>.Invoke(command);
        }

        public virtual void Setting(List<string> args)
        {
            var command = new CommandArguments(args);
            Command<T>.Invoke(command);
        }
    }

    public abstract class ProgramBase2
    {
        public abstract int ShowUsage();

        public static int InvokeCommand<PT>(PT _, ProgramArguments programArguments) where PT : new()
        {
            ProgramConfig.LoadOptionalConfig(programArguments.Config);

            // load optionally specified config file settings
            return Command<PT>.Invoke(programArguments);
        }
    }
}
