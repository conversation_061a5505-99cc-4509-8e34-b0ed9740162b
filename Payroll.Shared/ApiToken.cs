using System;

namespace Payroll.Shared;

public class ApiToken
{
    public ApiToken()
    {
        Token = string.Empty;
        ExpirationUtc = DateTime.UtcNow;
        TokenType = string.Empty;
    }

    public string Token { get; set; }
    public DateTime ExpirationUtc { get; set; }
    public string TokenType { get; set; }

    public bool IsAboutToExpire => DateTime.UtcNow > ExpirationUtc.AddMinutes(-5);
}
