using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;

namespace Payroll.Shared
{
    public class CommandArguments
    {
        public CommandArguments()
        {
        }

        public CommandArguments(List<string> args)
        {
            if (args.Count == 0) return;
            Verb = args[0];

            if (args.Count == 1) return;
            Arguments = args.GetRange(1, args.Count - 1);
        }

        public string Verb { get; set; }

        public IEnumerable<string> Arguments { get; set; }
    }
}
