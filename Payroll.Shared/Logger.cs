using System;
using System.Collections.Generic;
using System.IO;
using System.Text;
using Microsoft.Extensions.Configuration;
using Serilog;
using Serilog.Core;
using Serilog.Events;
using Serilog.Formatting.Compact;

namespace Payroll.Shared
{
    public static class Logger
    {
        public static bool IsDebugEnabled { get; private set; } = false;

        private static void SetupUsingAppSettingsFile(string filePath, string fileName)
        {
            var configuration = new ConfigurationBuilder()
                .SetBasePath(filePath)
                .AddJsonFile(fileName)
                .Build();

            Log.Logger = new LoggerConfiguration()
                .ReadFrom.Configuration(configuration)
                .CreateLogger();
        }

        private static void SetupUsingEnvVars(string banner)
        {
            var errFile = Environment.GetEnvironmentVariable("ERRFILE");
            var logFile = Environment.GetEnvironmentVariable("LOGFILE");
            var logLevel = Environment.GetEnvironmentVariable("LOGLEVEL");
            var logJson = Environment.GetEnvironmentVariable("LOGJSON");
            var debug = Environment.GetEnvironmentVariable("DEBUG");

            // setup logging services...
            var minimumLevel = LogEventLevel.Information;
            if (!string.IsNullOrEmpty(logLevel))
            {
                if (string.Equals(logLevel, "debug", StringComparison.OrdinalIgnoreCase))
                    minimumLevel = LogEventLevel.Debug;
                if (string.Equals(logLevel, "warning", StringComparison.OrdinalIgnoreCase))
                    minimumLevel = LogEventLevel.Warning;
                if (string.Equals(logLevel, "error", StringComparison.OrdinalIgnoreCase))
                    minimumLevel = LogEventLevel.Error;
            }

            // debug override
            if (!string.IsNullOrEmpty(debug))
            {
                minimumLevel = LogEventLevel.Debug;
                IsDebugEnabled = true;
            }

            var loggingLevel = new LoggingLevelSwitch() { MinimumLevel = minimumLevel };
            var loggerConfiguration = new LoggerConfiguration()
                .MinimumLevel.ControlledBy(loggingLevel)
                .WriteTo.Console(standardErrorFromLevel: minimumLevel);

            var writeToFile = errFile ?? logFile;
            if (!string.IsNullOrEmpty(writeToFile))
            {
                if (string.IsNullOrEmpty(logJson))
                {
                    loggerConfiguration.WriteTo.File(writeToFile, retainedFileCountLimit: 7);
                }
                else
                {
                    loggerConfiguration.WriteTo.File(new CompactJsonFormatter(), writeToFile, retainedFileCountLimit: 7);
                }
            }
            Log.Logger = loggerConfiguration.CreateLogger();
        }

        public static void Setup(string banner)
        {
            SetupUsingEnvVars(banner);
            Log.Logger.Debug(banner);
        }
    }
}
