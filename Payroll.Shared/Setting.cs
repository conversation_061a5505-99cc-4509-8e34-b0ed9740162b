using System;
using System.Collections.Generic;
using System.IO;
using IniParser.Model.Configuration;
using IniParser.Model;
using IniParser;
using Serilog;
using static System.Environment;
using System.Runtime.InteropServices;

namespace Payroll.Shared
{
    public static class Setting
    {
        static IniData IniData { get; set; }
        static FileIniDataParser FileIniDataParser { get; set; }
        public static string IniFilePath { get; private set; }

        static Setting()
        {
            IniData = null;
            FileIniDataParser = new FileIniDataParser();
            FileIniDataParser.Parser.Configuration.CommentString = "#";
        }

        public static string Get(string section, string name)
        {
            Init();
            if (IniData == null) return string.Empty;

            if (IniData.TryGetKey($"{section}-{name}", out string val))
                return val;

            return string.Empty;
        }

        public static bool GetBoolean(string section, string name)
        {
            var val = Get(section, name);
            return (val == "1");
        }

        public static string GetFromConfigOrEnv(string section, string name, string envKey)
        {
            var setting = Setting.Get(section, name);
            if (!string.IsNullOrEmpty(setting)) return setting;
            return Environment.GetEnvironmentVariable(envKey);
        }

        public static void Set(string section, string name, string val)
        {
            Init();
            IniData[section][name] = val;
            FileIniDataParser.WriteFile(IniFilePath, IniData);
        }

        public static Dictionary<string, string> List()
        {
            Init();
            var list = new Dictionary<string, string>();
            if (IniData == null)
            {
                Log.Logger.Warning("Setting store uninitialized.");
                return list;
            }

            foreach (var section in IniData.Sections)
            {
                foreach (var key in section.Keys)
                {
                    var name = $"{section.SectionName}-{key.KeyName}";
                    list.Add(name, key.Value);
                }
            }

            return list;
        }

        private static Dictionary<string, string> DoListSection(string sectionName,
            Action<Dictionary<string, string>, string, string> addFunc)
        {
            Init();
            var sectionMap = new Dictionary<string, string>();

            var section = IniData[sectionName];
            if (section == null) return sectionMap;

            foreach (var key in section)
            {
                addFunc(sectionMap, key.KeyName, key.Value);
            }

            return sectionMap;
        }

        public static Dictionary<string, string> ListSection(string sectionName)
        {
            Init();
            var sectionMap = DoListSection(sectionName, (dictionary, key, val) => dictionary.Add(key, val));
            return sectionMap;
        }

        public static Dictionary<string, string> ListSectionReversed(string sectionName)
        {
            Init();
            var sectionMap = DoListSection(sectionName, (dictionary, key, val) => dictionary.Add(val, key));
            return sectionMap;
        }

        public static string DefaultSettingsPath()
        {
            var appDataPath = SpecialFolder.CommonApplicationData;

            // you cannot write to CommonAppData path on osx, so use localappdata instead
            OperatingSystem os = Environment.OSVersion;
            if (os.Platform == PlatformID.Unix && os.VersionString.Contains("Darwin") || RuntimeInformation.IsOSPlatform(OSPlatform.OSX))
            {
                appDataPath = SpecialFolder.LocalApplicationData;
            }

           Log.Logger.Debug("AppDataPath: {AppDataPath}", appDataPath);

            var commonAppDataPath = Environment.GetFolderPath(appDataPath);
            return Path.Combine(commonAppDataPath, "PayrollTools");
        }

        private static string DefaultSettingsFile()
        {
            var configPath = DefaultSettingsPath();
            var iniFilePath = Path.Combine(configPath, "settings.ini");
            Log.Logger.Debug("DefaultSettingsFile: {IniFilePath}", iniFilePath);

            try
            {
                if (!Directory.Exists(configPath))
                {
                    Log.Logger.Debug("Creating directory {ptools}", configPath);
                    Directory.CreateDirectory(configPath);
                }

                if (!File.Exists(iniFilePath))
                {
                    Log.Logger.Debug("Creating settings file {IniFilePath}", iniFilePath);
                    File.Create(iniFilePath);
                    Log.Logger.Debug("Settings store created.");
                    
                    IniData = new IniData();
                    IniData["general"]["created"] = DateTime.Now.ToShortDateString();
                    FileIniDataParser.WriteFile(iniFilePath, IniData);
                    Log.Logger.Debug("Settings store initialized.");
                }
            }
            catch (Exception e)
            {
                Log.Logger.Fatal(e.Message);
            }

            return iniFilePath;
        }

        public static void LoadIniData(string settingFileName)
        {
            IniFilePath = settingFileName;

            try
            {
                IniData = FileIniDataParser.ReadFile(IniFilePath);
                IniData.SectionKeySeparator = '-';
            }
            catch (Exception e)
            {
                Log.Logger.Fatal(e.Message);
            }

            Log.Logger.Debug("Settings store path: {path}", IniFilePath);
        }

        public static string SettingFileName()
        {
            var settingFileName = Environment.GetEnvironmentVariable("CONFIG");
            
            if (string.IsNullOrEmpty(settingFileName))
            {
                return DefaultSettingsFile();
            }

            return settingFileName;
        }

        public static void Init()
        {
            if (IniData != null) return;
            var sfn = SettingFileName();
            LoadIniData(sfn);
        }

        public static ExecutionMode ExecutionMode(string sectionName, string modeName)
        {
            var mode = Setting.Get(sectionName, modeName);

            if (string.IsNullOrEmpty(mode)) return Payroll.Shared.ExecutionMode.DryRun;
            if (mode == "disabled") return Payroll.Shared.ExecutionMode.Disabled;
            
            return (mode == "execute") ? Payroll.Shared.ExecutionMode.Execute : Payroll.Shared.ExecutionMode.DryRun;
        }
    }
}
