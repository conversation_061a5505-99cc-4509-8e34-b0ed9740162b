﻿using Payroll.Shared;
using System;

namespace Payroll.Shared
{
    public static class SystemConfig
    {
        public readonly static string SettingSection = "system";
        public readonly static string DefaultCacheName = "ptools.ldb";

        public static string EmailSubjectForErrors { get; private set; }
        public static string EmailBodyForErrors { get; private set; }

        public static string DatabaseName { get; private set; }
        public static string SmtpUsername { get; private set; }
        public static string SmtpPassword { get; private set; }
        public static string SmtpHost { get; private set; }
        public static string NotifyAddress { get; private set; }
        public static string ErrorsNotifyAddress { get; private set; }
        public static string FromAddress { get; private set; }
        public static string DatabaseType { get; private set; }
        public static string Dsn { get; private set; }

        public static readonly string ENV_KEY_TIMEZONE_API_KEY = "PTOOLS_TIMEZONE_API_KEY";
        public static readonly string ENV_KEY_CACHE_NAME = "PTOOLS_CACHE_NAME";

        static SystemConfig()
        {
            Setting.Init();
            EmailSubjectForErrors = Setting.Get(SettingSection, "email_subject_errors");
            EmailBodyForErrors = Setting.Get(SettingSection, "email_body_errors");
            DatabaseName = Setting.Get(SettingSection, "dbname");

            SmtpUsername = Setting.Get(SettingSection, "smtp_user");
            SmtpPassword = Setting.Get(SettingSection, "smtp_pwd");
            SmtpHost = Setting.Get(SettingSection, "smtp_host");

            // Notify addresses
            NotifyAddress = Setting.Get(SettingSection, "notify_addr");
            ErrorsNotifyAddress = Setting.Get(SettingSection, "errors_notify_addr");

            var from = Setting.Get(SettingSection, "from_addr");
            FromAddress = string.IsNullOrEmpty(from) ? SmtpUsername : from;

            var dbtype = Setting.Get(SettingSection, "dbtype");
            DatabaseType = string.IsNullOrEmpty(dbtype) ? "mongo" : dbtype;

            Dsn = Setting.Get(SettingSection, "dsn");
        }

        public static string TimeZoneApiKey()
        {
            return Environment.GetEnvironmentVariable(ENV_KEY_TIMEZONE_API_KEY);
        }

        public static string CacheName()
        {
            var cacheOveride = Environment.GetEnvironmentVariable(ENV_KEY_CACHE_NAME);

            if (!string.IsNullOrEmpty(cacheOveride))
                return cacheOveride;

            return DefaultCacheName;
        }
    }
}
