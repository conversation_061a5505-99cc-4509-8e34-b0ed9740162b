using System;
using System.Collections.Generic;

namespace Payroll.Shared
{
    public enum ResultType
    {
        NewHire,
        ReHire,
        Change,
        Update,
        Termination,
        Error,
        Unknown
    }

    public class Result
    {
        public static Result Failure = new Result() { Success = false };

        public Result()
        {
            Id = Guid.NewGuid().ToString();
            Args = new SortedDictionary<string, string>();
            Success = true;
            ResultType = ResultType.Unknown;
            Timestamp = DateTime.Now;
        }

        public bool AddArg(string key, string val)
        {
            if (string.IsNullOrEmpty(value: val) || string.IsNullOrEmpty(key)) return false;
            Args.Add(key, val);
            return true;
        }

        public string Id { get; set; }
        public DateTime Timestamp { get; set; }

        public bool Success { get; set; }
        public ResultType ResultType { get; set; }

        public SortedDictionary<string, string> Args { get; set; }
    }
}