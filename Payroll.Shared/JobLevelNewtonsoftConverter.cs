using Newtonsoft.Json;
using System;

namespace Payroll.Shared
{
    public class JobLevelNewtonsoftConverter : JsonConverter
    {
        public override bool CanConvert(Type objectType)
        {
            return objectType == typeof(JobLevel);
        }

        public override object ReadJson(JsonReader reader, Type objectType, object existingValue, JsonSerializer serializer)
        {
            if (reader.TokenType == JsonToken.Null)
                return JobLevel.None;

            if (reader.TokenType == JsonToken.Integer)
            {
                int value = Convert.ToInt32(reader.Value);
                return (JobLevel)value;
            }

            throw new JsonSerializationException($"Unexpected token {reader.TokenType} when parsing JobLevel");
        }

        public override void WriteJson(JsonWriter writer, object value, JsonSerializer serializer)
        {
            JobLevel jobLevel = (JobLevel)value;
            
            if (jobLevel == JobLevel.None)
            {
                // Skip writing the property entirely when it's None
                writer.WriteNull();
                return;
            }

            writer.WriteValue((int)jobLevel);
        }
    }
}
