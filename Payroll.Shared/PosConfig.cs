using System;

namespace Payroll.Shared;

public class PosConfig
{
    public readonly string SettingSection;
    public readonly int HireLimit;
    public readonly int SyncLimit;
    public readonly int TermLimit;

    public readonly ExecutionMode HireMode;
    public readonly ExecutionMode SyncMode;
    public readonly ExecutionMode TermMode;
    public readonly int ApiDelayInSeconds;

    public PosConfig(string settingSection)
    {
        SettingSection = settingSection;

        {
            string limit = Setting.Get(SettingSection, "hire_limit");
            HireLimit = string.IsNullOrEmpty(limit) ? 0 : int.Parse(limit);
        }

        {
            string limit = Setting.Get(SettingSection, "sync_limit");
            SyncLimit = string.IsNullOrEmpty(limit) ? 0 : int.Parse(limit);
        }

        {
            string limit = Setting.Get(SettingSection, "term_limit");
            TermLimit = string.IsNullOrEmpty(limit) ? 0 : int.Parse(limit);
        }

        var delay = Setting.Get(SettingSection, "delay");
        ApiDelayInSeconds = (string.IsNullOrEmpty(delay)) ? 0 : Convert.ToInt32(delay);
        
        HireMode = Setting.ExecutionMode(SettingSection, "hire_mode");
        SyncMode = Setting.ExecutionMode(SettingSection, "sync_mode");
        TermMode = Setting.ExecutionMode(SettingSection, "term_mode");
    }
}
