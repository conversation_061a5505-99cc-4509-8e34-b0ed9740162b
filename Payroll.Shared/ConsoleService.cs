using Newtonsoft.Json;
using Serilog;
using System;
using System.IO;
using System.Collections.Generic;
using System.Text.Json;
using System.Linq;

namespace Payroll.Shared
{
    public static class ConsoleService
    {
        public static void PrintFormattedJson<T>(T t)
        {
            var options = new JsonSerializerOptions
            {
                WriteIndented = true
            };

            string formattedJson = System.Text.Json.JsonSerializer.Serialize(t, Json.DefaultSerializerOutputStyle);
            Console.WriteLine(formattedJson);
        }

        private static DateTime OffsetDate(DateTime dateTime, string offset)
        {
            if (string.IsNullOrEmpty(offset)) return dateTime;

            bool isNumeric = offset.All(char.IsDigit);
            if (isNumeric)
            {
                int offsetDays = Convert.ToInt32(offset);
                return dateTime.AddDays(-offsetDays);
            }

            if (offset.Contains('h'))
            {
                var offsetNumeric = offset.TrimEnd('h');
                int offsetHours = Convert.ToInt32(offsetNumeric);
                return dateTime.AddHours(-offsetHours);
            }

            if (offset.Contains('m'))
            {
                var offsetNumeric = offset.TrimEnd('m');
                int offsetMins = Convert.ToInt32(offsetNumeric);
                return dateTime.AddMinutes(-offsetMins);
            }

            if (offset.Contains('d'))
            {
                var offsetNumeric = offset.TrimEnd('d');
                int offsetDays = Convert.ToInt32(offsetNumeric);
                return dateTime.AddDays(-offsetDays);
            }

            if (offset.Contains('y'))
            {
                var offsetNumeric = offset.TrimEnd('y');
                int offsetAmt = Convert.ToInt32(offsetNumeric);
                return dateTime.AddYears(-offsetAmt);
            }

            return dateTime;
        }

        public static bool GetDateTimeWindow(string startDateStr, string endDateStr, ref DateTime startDate, ref DateTime endDate)
        {
            startDate = OffsetDate(DateTime.Now.Date, startDateStr);
            endDate = OffsetDate(DateTime.Now.Date, endDateStr);
            Log.Logger.Information("Date window of {start} to {end}", startDate, endDate);
            return true;
        }


        // this newer version does not initialize the startdate/enddate fields, that's left up to the caller.
        // why leave it up to the caller?
        // for punch windows, we often want 11.59pm on the end date
        // for change windows, we do not want such things, especially if we want "last hour of changes" for example
        public static bool GetDateTimeWindowFromArgs(List<string> args, ref DateTime startDate, ref DateTime endDate)
        {
            if (args == null) return true;

            if (args.Count > 1)
            {
                startDate = OffsetDate(DateTime.Now, args[0]);
                endDate = OffsetDate(DateTime.Now, args[1]);
            }
            else if (args.Count > 0)
            {
                startDate = OffsetDate(DateTime.Now, args[0]);
            }

            Log.Logger.Information("Date window of {start} to {end}", startDate, endDate);
            return true;
        }

        public static bool GetDateWindowFromArgs(List<string> args, out DateTime startDate, out DateTime endDate)
        {
            startDate = DateTime.Now.AddDays(-1);
            endDate = DateTime.Now.Date.AddDays(1).AddSeconds(-1);
            return GetDateTimeWindowFromArgs(args, ref startDate, ref endDate);
        }

        public static bool TryGetEmployeesFromInput(out List<Employee> employees)
        {
            var json = Console.In.ReadToEnd();
            employees = JsonConvert.DeserializeObject<List<Employee>>(json);

            if (employees == null)
            {
                Log.Logger.Error("Failed to parse employee list");
                return false;
            }

            Log.Logger.Debug($"Processing {employees.Count} employee records...");
            return true;
        }

        public static bool TryGetDirectoryEntriesFromInput(out List<EmployeeDirectoryEntry> employees)
        {
            var json = Console.In.ReadToEnd();
            employees = JsonConvert.DeserializeObject<List<EmployeeDirectoryEntry>>(json);

            if (employees == null)
            {
                Log.Logger.Information("Failed to parse employee directory entry list, may be empty...");
                return false;
            }

            Log.Logger.Debug($"Processing {employees.Count} employee directory entry records...");
            return true;
        }

        public static bool TryGetChangesFromInput(out List<Change> changes)
        {
            var json = Console.In.ReadToEnd();
            changes = JsonConvert.DeserializeObject<List<Change>>(json);

            if (changes == null)
            {
                Log.Logger.Error("Failed to parse changes");
                return false;
            }

            Log.Logger.Debug($"Processing {changes.Count} change records...");
            return true;
        }

        public static bool TryGetResultsFromInput(out List<Result> results)
        {
            var json = Console.In.ReadToEnd();
            results = JsonConvert.DeserializeObject<List<Result>>(json);

            if (results == null)
            {
                Log.Logger.Error("Failed to parse results list");
                return false;
            }

            Log.Logger.Debug($"Processing {results.Count} result records...");
            return true;
        }

        public static bool TryGetPunchesFromInput(out List<PunchPair> punches)
        {
            var json = Console.In.ReadToEnd();
            punches = System.Text.Json.JsonSerializer.Deserialize<List<PunchPair>>(json, new JsonSerializerOptions
            {
                PropertyNameCaseInsensitive = true
            });

            if (punches == null)
            {
                Log.Logger.Error("Failed to parse punches");
                return false;
            }

            Log.Logger.Debug($"Processing {punches.Count} punch records...");
            return true;
        }
    }
}
