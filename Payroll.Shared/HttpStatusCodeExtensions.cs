using System.Net;

namespace Payroll.Shared;

public static class HttpStatusCodeExtensions
{
    public static bool IsRetryable(this HttpStatusCode statusCode)
    {
        return statusCode == HttpStatusCode.RequestTimeout
            || statusCode == HttpStatusCode.InternalServerError
            || statusCode == HttpStatusCode.ServiceUnavailable
            || statusCode == HttpStatusCode.GatewayTimeout
            || statusCode == HttpStatusCode.TooManyRequests;
    }
}
